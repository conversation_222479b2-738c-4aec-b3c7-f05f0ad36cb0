#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
妙手ERP限时秒杀自动化程序
基于原有AddProductAutomation程序架构，专门用于妙手ERP系统的限时秒杀活动操作

主要功能：
1. 在指纹浏览器中启动程序
2. 自动化操作妙手ERP系统的限时秒杀功能
3. 创建限时秒杀活动
4. 添加产品到活动中
5. 设置产品折扣和限购数量

作者：基于张丽原程序改编
日期：2025-07-08
"""

import sys
import time
import requests
from playwright import sync_api
import logging
import threading
from threading import Thread
from datetime import datetime, timedelta
from logging import FileHandler
import pandas as pd
import os
from openpyxl import load_workbook
from typing import Dict, List, Optional
import gc
from concurrent.futures import ThreadPoolExecutor
from playwright.sync_api import sync_playwright
import json

# ==================== 全局配置变量 ====================

# 调试控制
DEBUG_SAVE_PAGE_INFO = True  # 全局调试变量：控制是否保存页面调试信息（HTML源码和截图）

# 线程控制
max_threads = 1  # 修改为1个线程便于调试
semaphore = threading.Semaphore(max_threads)
chunk_size = 100

# 时间控制
wait_time = 0  # 程序启动前等待时间（秒）
operation_wait_time = 2  # 操作间隔时间（秒）
page_load_timeout = 30000  # 页面加载超时时间（毫秒）
loop_interval_seconds = 3600  # 循环间隔时间（秒）- 1小时

# 重试配置
try_open_times = 5  # 尝试打开网页的次数
max_open_times = 1000  # 最大打开浏览器次数

# 文件路径配置
environment_path = "miaoshou_environment.xlsx"  # 环境配置文件
config_path = "miaoshou_config.json"  # 配置文件

# 妙手ERP相关URL
MIAOSHOU_BASE_URL = "https://erp.91miaoshou.com"
# 直接访问创建活动页面，跳过中间页面提高效率
FLASH_SALE_URL = f"{MIAOSHOU_BASE_URL}/tiktok/marketing/flashSale/create"

# 浏览器计数器
open_counter = 0

# 当前日期
current_date = datetime.now().strftime('%Y-%m-%d %H-%M-%S')

# ==================== 工具函数 ====================

# 创建全局锁对象用于Excel写入
excel_lock = threading.Lock()

def write_to_excel_realtime(data: Dict, file_name: str):
    """实时写入Excel文件"""
    with excel_lock:
        reports_dir = 'reports'
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        file_path = os.path.join(reports_dir, file_name)

        if os.path.exists(file_path):
            df = pd.read_excel(file_path)
        else:
            df = pd.DataFrame()

        new_row = pd.DataFrame([data])
        df = pd.concat([df, new_row], ignore_index=True)
        df.to_excel(file_path, index=False, engine='openpyxl')

def load_config(config_file: str = config_path) -> Dict:
    """加载配置文件"""
    default_config = {
        "flash_sale": {
            "discount_percentage": 20,  # 默认折扣百分比
            "limit_quantity": 100,      # 默认限购数量
            "activity_duration": 24     # 活动持续时间（小时）
        },
        "selectors": {
            "create_activity_btn": "//button[contains(text(), '创建活动')]",
            "activity_name_input": "//input[@placeholder='请输入活动名称']",
            "add_product_btn": "//button[contains(text(), '添加产品')]",
            "discount_input": "//input[@placeholder='请输入折扣']",
            "quantity_input": "//input[@placeholder='请输入限购数量')]"
        }
    }
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
        except Exception as e:
            print(f"加载配置文件失败，使用默认配置: {e}")
    
    # 创建默认配置文件
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(default_config, f, ensure_ascii=False, indent=2)
    
    return default_config

# ==================== 核心自动化类 ====================

class MiaoshouAutomation:
    """妙手ERP自动化操作类"""
    
    def __init__(self, container_code: str, debugging_port: int, logger: logging.Logger, env_name: str):
        """
        初始化妙手自动化实例
        
        Args:
            container_code: 容器代码
            debugging_port: 调试端口
            logger: 日志记录器
            env_name: 环境名称
        """
        self.container_code = container_code
        self.debugging_port = debugging_port
        self.logger = logger
        self.env_name = env_name
        self.config = load_config()
        
        # 初始化Playwright
        self.playwright = sync_api.sync_playwright().start()
        self.browser, self.browser_context = self.get_browser_context(
            self.playwright, self.debugging_port)
    
    @staticmethod
    def get_browser_context(playwright, port: int):
        """获取浏览器上下文"""
        browser = playwright.chromium.connect_over_cdp(f"http://127.0.0.1:{port}")
        context = browser.contexts[0]
        return browser, context
    
    def close_browser(self):
        """关闭浏览器"""
        url = 'http://127.0.0.1:6873/api/v1/browser/stop'
        data = {"containerCode": self.container_code}
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 200:
                res_json = response.json()
                if res_json.get('code') == 0:
                    self.logger.info(f"成功关闭了容器代码为 {self.container_code} 的浏览器")
                else:
                    self.logger.error(f"关闭容器代码为 {self.container_code} 的浏览器失败。错误：{res_json.get('msg')}")
            else:
                self.logger.error(f"关闭容器代码为 {self.container_code} 的浏览器失败。HTTP状态码：{response.status_code}")
                self.close_browser_with_playwright()
        except Exception as e:
            self.logger.error(f"关闭浏览器时发生异常：{e}")
            self.close_browser_with_playwright()
    
    def close_browser_with_playwright(self):
        """使用Playwright关闭浏览器"""
        try:
            self.browser.close()
            self.logger.info(f"使用Playwright成功关闭了容器代码为 {self.container_code} 的浏览器")
        except Exception as e:
            self.logger.error(f"使用Playwright关闭容器代码为 {self.container_code} 的浏览器失败。异常：{e}")
    
    def close_other_pages(self, current_page):
        """关闭除当前页面外的所有页面"""
        for page in self.browser_context.pages:
            if page != current_page:
                page.close()
        time.sleep(30)
        self.close_browser()
    
    def wait_and_click(self, page, xpath: str, description: str = "", timeout: int = 10000, retries: int = 3) -> bool:
        """等待元素并点击"""
        for attempt in range(retries):
            try:
                page.wait_for_selector(f"xpath={xpath}", timeout=timeout)
                
                js_script = f'''
                (function() {{
                    const xpath = "{xpath}";
                    const element = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                    if (element) {{
                        const clickEvent = new MouseEvent("click", {{
                            bubbles: true,
                            cancelable: true,
                            view: window
                        }});
                        element.dispatchEvent(clickEvent);
                        return true;
                    }}
                    return false;
                }})()
                '''
                
                result = page.evaluate(js_script)
                if result:
                    self.logger.info(f"成功点击元素: {description or xpath}")
                    return True
                else:
                    self.logger.warning(f"元素存在但点击失败: {description or xpath}")
                    
            except Exception as e:
                self.logger.error(f"点击元素时发生异常 (尝试 {attempt + 1}/{retries}): {e}")
                
        self.logger.error(f"点击元素失败，超过重试次数: {description or xpath}")
        return False
    
    def input_text(self, page, xpath: str, text: str, description: str = "", clear_first: bool = True) -> bool:
        """输入文本到指定元素"""
        try:
            page.wait_for_selector(f"xpath={xpath}", timeout=10000)
            element = page.locator(f"xpath={xpath}")
            
            if clear_first:
                element.clear()
            
            element.fill(text)
            self.logger.info(f"成功输入文本到 {description or xpath}: {text}")
            return True
            
        except Exception as e:
            self.logger.error(f"输入文本失败 {description or xpath}: {e}")
            return False

    def _debug_page_buttons(self, page):
        """调试页面按钮，查看所有可用的按钮"""
        try:
            self.logger.info("🔍 调试: 查看页面上的所有按钮")

            # 获取所有按钮
            js_script = '''
            (function() {
                const buttons = document.querySelectorAll('button');
                const buttonInfo = [];
                buttons.forEach((btn, index) => {
                    buttonInfo.push({
                        index: index,
                        text: btn.innerText.trim(),
                        className: btn.className,
                        visible: btn.offsetParent !== null
                    });
                });
                return buttonInfo;
            })()
            '''

            buttons = page.evaluate(js_script)
            self.logger.info(f"找到 {len(buttons)} 个按钮:")

            for btn in buttons[:10]:  # 只显示前10个
                if btn['visible']:
                    self.logger.info(f"  按钮 {btn['index']}: '{btn['text']}' (可见)")
                else:
                    self.logger.info(f"  按钮 {btn['index']}: '{btn['text']}' (隐藏)")

        except Exception as e:
            self.logger.warning(f"调试按钮失败: {e}")

    def _debug_page_labels(self, page):
        """调试页面标签，查看所有可用的label元素"""
        try:
            self.logger.info("🔍 调试: 查看页面上的所有label元素")

            # 获取所有label元素
            js_script = '''
            (function() {
                const labels = document.querySelectorAll('label');
                const labelInfo = [];
                labels.forEach((label, index) => {
                    labelInfo.push({
                        index: index,
                        id: label.id || 'no-id',
                        className: label.className || 'no-class',
                        text: label.innerText.trim() || 'no-text',
                        for: label.getAttribute('for') || 'no-for',
                        visible: label.offsetParent !== null,
                        xpath: getXPath(label)
                    });
                });

                // 获取元素的XPath
                function getXPath(element) {
                    if (element.id !== '') {
                        return '//*[@id="' + element.id + '"]';
                    }
                    if (element === document.body) {
                        return '/html/body';
                    }
                    var ix = 0;
                    var siblings = element.parentNode.childNodes;
                    for (var i = 0; i < siblings.length; i++) {
                        var sibling = siblings[i];
                        if (sibling === element) {
                            return getXPath(element.parentNode) + '/' + element.tagName.toLowerCase() + '[' + (ix + 1) + ']';
                        }
                        if (sibling.nodeType === 1 && sibling.tagName === element.tagName) {
                            ix++;
                        }
                    }
                }

                return labelInfo;
            })()
            '''

            labels = page.evaluate(js_script)
            self.logger.info(f"找到 {len(labels)} 个label元素:")

            for label in labels[:15]:  # 只显示前15个
                if '选择店铺' in label['text'] or 'shop' in label['text'].lower():
                    self.logger.info(f"  🎯 重要: label {label['index']}: '{label['text']}' (ID: {label['id']}, 可见: {label['visible']})")
                    self.logger.info(f"      XPath: {label['xpath']}")
                elif label['visible']:
                    self.logger.info(f"  label {label['index']}: '{label['text']}' (ID: {label['id']}, 可见)")

        except Exception as e:
            self.logger.warning(f"调试label失败: {e}")

    def save_page_debug_info(self, page, step_name: str):
        """保存页面调试信息：HTML源码和截图"""
        # 检查全局调试变量，如果为False则跳过保存
        if not DEBUG_SAVE_PAGE_INFO:
            self.logger.info(f"🔍 调试信息保存已禁用，跳过保存: {step_name}")
            return True

        try:
            import os
            from datetime import datetime

            # 创建调试目录
            debug_dir = "debug_pages"
            if not os.path.exists(debug_dir):
                os.makedirs(debug_dir)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存HTML源码
            html_content = page.content()
            html_file = os.path.join(debug_dir, f"{step_name}_{timestamp}.html")
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            self.logger.info(f"📄 已保存HTML源码: {html_file}")

            # 保存截图
            screenshot_file = os.path.join(debug_dir, f"{step_name}_{timestamp}.png")
            page.screenshot(path=screenshot_file, full_page=True)
            self.logger.info(f"📸 已保存页面截图: {screenshot_file}")

            # 保存页面信息摘要
            info_file = os.path.join(debug_dir, f"{step_name}_{timestamp}_info.txt")
            with open(info_file, 'w', encoding='utf-8') as f:
                f.write(f"页面调试信息 - {step_name}\n")
                f.write(f"时间: {timestamp}\n")
                f.write(f"URL: {page.url}\n")
                f.write(f"标题: {page.title()}\n")
                f.write(f"页面大小: {len(html_content)} 字符\n")
                f.write("\n" + "="*50 + "\n")

                # 获取所有表单元素
                try:
                    form_elements = page.evaluate('''
                    () => {
                        const elements = [];
                        // 获取所有input元素
                        document.querySelectorAll('input').forEach((el, i) => {
                            elements.push({
                                type: 'input',
                                index: i,
                                id: el.id || 'no-id',
                                name: el.name || 'no-name',
                                placeholder: el.placeholder || 'no-placeholder',
                                className: el.className || 'no-class',
                                type_attr: el.type || 'no-type'
                            });
                        });

                        // 获取所有label元素
                        document.querySelectorAll('label').forEach((el, i) => {
                            elements.push({
                                type: 'label',
                                index: i,
                                id: el.id || 'no-id',
                                text: el.innerText.trim() || 'no-text',
                                for: el.getAttribute('for') || 'no-for'
                            });
                        });

                        // 获取所有button元素
                        document.querySelectorAll('button').forEach((el, i) => {
                            elements.push({
                                type: 'button',
                                index: i,
                                id: el.id || 'no-id',
                                text: el.innerText.trim() || 'no-text',
                                className: el.className || 'no-class'
                            });
                        });

                        return elements;
                    }
                    ''')

                    f.write("表单元素列表:\n")
                    for element in form_elements:
                        f.write(f"  {element['type']} {element['index']}: {element}\n")

                except Exception as e:
                    f.write(f"获取表单元素失败: {e}\n")

            self.logger.info(f"📋 已保存页面信息: {info_file}")
            return True

        except Exception as e:
            self.logger.warning(f"保存页面调试信息失败: {e}")
            return False

    def create_flash_sale_activity(self, activity_name: str, duration_hours: int = 24) -> bool:
        """
        创建限时秒杀活动 - 完整流程

        Args:
            activity_name: 活动名称
            duration_hours: 活动持续时间（小时）

        Returns:
            bool: 创建是否成功
        """
        new_page = None
        try:
            self.logger.info(f"开始创建限时秒杀活动: {activity_name}")

            # 步骤1: 清理多余标签页并打开限时秒杀主页面
            self.logger.info("🧹 清理多余的标签页...")
            all_pages = self.browser_context.pages
            self.logger.info(f"当前共有 {len(all_pages)} 个标签页")

            # 关闭除第一个之外的所有标签页
            for i, page in enumerate(all_pages):
                if i > 0:  # 保留第一个标签页
                    try:
                        page.close()
                        self.logger.info(f"已关闭标签页 {i}")
                    except Exception as e:
                        self.logger.warning(f"关闭标签页 {i} 失败: {e}")

            self.logger.info(f"步骤1: 直接打开创建活动页面: {FLASH_SALE_URL}")
            new_page = self.browser_context.new_page()
            new_page.goto(FLASH_SALE_URL, timeout=page_load_timeout)
            new_page.wait_for_load_state("load", timeout=page_load_timeout)

            page_title = new_page.title()
            page_url = new_page.url

            self.logger.info(f"页面加载成功 - 标题: {page_title}")
            self.logger.info(f"当前URL: {page_url}")

            # 等待创建活动页面完全加载，增加等待时间确保页面稳定
            wait_seconds = 3 + (time.time() % 2)  # 3-5秒随机等待
            self.logger.info(f"等待创建活动页面完全加载 {wait_seconds:.1f} 秒...")
            time.sleep(wait_seconds)

            # 步骤2: 已跳过 - 直接访问创建页面，无需点击创建活动按钮
            self.logger.info("步骤2: 已跳过点击创建活动按钮（直接访问创建页面）")

            # 验证是否成功进入创建活动页面
            if not ("/create" in page_url or "/flashSale/create" in page_url):
                self.logger.warning(f"⚠️ 当前URL可能不是创建页面: {page_url}")
                # 但继续执行，可能页面结构有变化
            else:
                self.logger.info("✅ 已成功进入创建活动页面")

            # 步骤2.5: 等待页面网络请求完成，确保页面完全加载
            try:
                new_page.wait_for_load_state('networkidle', timeout=15000)
                self.logger.info("✅ 创建活动页面网络请求已完成")
            except Exception as e:
                self.logger.warning(f"等待网络请求完成失败，继续执行: {e}")

            # 获取创建活动页面信息
            current_url = new_page.url
            current_title = new_page.title()
            self.logger.info(f"创建活动页面URL: {current_url}")
            self.logger.info(f"创建活动页面标题: {current_title}")

            # 🔍 保存创建活动页面的调试信息
            self.logger.info("🔍 保存创建活动页面调试信息...")
            self.save_page_debug_info(new_page, "create_activity_page")

            # 检查页面是否成功加载创建活动页面
            self.logger.info("🔍 验证创建活动页面加载状态...")

            page_loaded = False

            # 方法1: 检查URL是否包含create
            if "/create" in current_url or "/flashSale/create" in current_url:
                self.logger.info("✅ 确认已进入创建活动页面 (通过URL检查)")
                page_loaded = True

            # 方法2: 检查页面标题
            if not page_loaded and ("创建活动" in current_title or "创建" in current_title):
                self.logger.info("✅ 确认已进入创建活动页面 (通过标题检查)")
                page_loaded = True

            # 方法3: 检查是否有创建活动页面的特征元素
            if not page_loaded:
                create_indicators = [
                    "//span[contains(text(), '设置基本信息')]",
                    "//div[contains(text(), '设置基本信息')]",
                    "//span[contains(text(), '选择产品类型')]",
                    "//div[contains(text(), '选择产品类型')]",
                    "//span[contains(text(), '完成并提交')]",
                    "//button[contains(text(), '创建活动')]"
                ]

                for indicator in create_indicators:
                    try:
                        new_page.wait_for_selector(f"xpath={indicator}", timeout=2000)
                        self.logger.info("✅ 确认已进入创建活动页面 (通过页面特征元素)")
                        page_loaded = True
                        break
                    except:
                        continue

            # 方法4: 检查面包屑导航
            if not page_loaded:
                try:
                    breadcrumb_xpath = "//div[contains(@class, 'pro-breadcrumb-container') and contains(text(), '创建活动')]"
                    new_page.wait_for_selector(f"xpath={breadcrumb_xpath}", timeout=2000)
                    self.logger.info("✅ 确认已进入创建活动页面 (通过面包屑)")
                    page_loaded = True
                except Exception as e:
                    self.logger.warning(f"面包屑检查失败: {e}")

            # 如果所有方法都失败，但URL正确，可能是页面结构不同
            if not page_loaded:
                if "/create" in current_url:
                    self.logger.warning("⚠️ URL正确但无法确认页面结构，尝试继续执行...")
                    page_loaded = True
                else:
                    self.logger.error("❌ 页面没有正确加载创建活动页面！")
                    self.logger.error("可能的原因:")
                    self.logger.error("1. 页面加载时间过长")
                    self.logger.error("2. 需要登录或权限验证")
                    self.logger.error("3. 网络连接问题")
                    self.logger.error("4. URL路径已变更")
                    return False

            if page_loaded:
                self.logger.info("✅ 创建活动页面加载验证通过，继续执行...")

            # 步骤3: 选择店铺（选择泰国的第一个店铺）
            self.logger.info("步骤3: 开始选择店铺")
            if not self._select_shop(new_page):
                return False

            # 步骤4: 设置产品类别为按单品(SKU)
            self.logger.info("步骤4: 设置产品类别为按单品(SKU)")
            if not self._set_product_category_sku(new_page):
                return False

            # 步骤5: 开启自动延续
            self.logger.info("步骤5: 开启自动延续")
            if not self._enable_auto_extension(new_page):
                return False

            # 步骤6: 点击创建活动
            self.logger.info("步骤6: 点击创建活动")
            if not self._submit_create_activity(new_page):
                return False

            self.logger.info("✅ 限时秒杀活动创建流程完成")
            return True

        except Exception as e:
            self.logger.error(f"创建限时秒杀活动失败: {e}")
            return False

        finally:
            if new_page:
                self.logger.info("🔍 页面保持打开状态，便于检查结果...")
                # 保持页面打开以便检查
                pass

    def _select_shop(self, page) -> bool:
        """选择店铺 - 按照精确步骤：1.点击标签 2.等待下拉菜单 3.选择泰国"""
        try:
            # 等待页面完全稳定
            self.logger.info("等待创建活动页面完全加载...")
            time.sleep(2)

            # 🔍 调试：查看页面上的所有label元素
            self._debug_page_labels(page)

            # ========== 第一步：点击"选择店铺"标签 ==========
            self.logger.info("🎯 第一步：点击'选择店铺'输入框")

            # 先等待一下，模拟真实用户行为
            wait_time = 2 + (time.time() % 3)  # 2-5秒随机等待
            self.logger.info(f"等待 {wait_time:.1f} 秒后开始操作...")
            time.sleep(wait_time)

            # 使用多种策略来定位和点击选择店铺元素
            def try_click_shop_selector():
                """尝试多种方法点击选择店铺下拉框"""

                # 策略1: 直接点击输入框
                selectors_to_try = [
                    #("输入框(placeholder)", "//input[@placeholder='请选择或输入搜索']"),
                    #("输入框(class)", "//input[@class='jx-input__inner' and @placeholder='请选择或输入搜索']"),
                    ("级联选择器容器", "//div[contains(@class, 'jx-cascader')]"),
                    ("输入框包装器", "//div[@class='jx-input__wrapper']"),
                    ("下拉箭头", "//i[contains(@class, 'icon-arrow-down')]")
                ]

                for desc, selector in selectors_to_try:
                    try:
                        self.logger.info(f"  🎯 尝试点击: {desc}")
                        self.logger.info(f"     选择器: {selector}")

                        # 检查元素是否存在
                        elements = page.query_selector_all(f"xpath={selector}")
                        self.logger.info(f"     找到 {len(elements)} 个匹配元素")

                        if elements:
                            element = elements[0]

                            # 检查元素是否可见
                            is_visible = element.is_visible()
                            self.logger.info(f"     元素可见性: {is_visible}")

                            if is_visible:
                                # 滚动到元素位置
                                element.scroll_into_view_if_needed()
                                time.sleep(0.5)

                                # 尝试点击
                                element.click()
                                self.logger.info(f"  ✅ 成功点击: {desc}")

                                # 等待一下看是否有反应
                                time.sleep(2)

                                # 检查下拉菜单是否出现
                                dropdown_elements = page.query_selector_all("xpath=//div[contains(@class, 'jx-cascader__dropdown')]")
                                if dropdown_elements and dropdown_elements[0].is_visible():
                                    self.logger.info("  🎉 下拉菜单已成功弹出！")
                                    return True
                                else:
                                    self.logger.warning(f"  ⚠️ 点击了{desc}但下拉菜单未弹出")
                            else:
                                self.logger.warning(f"     元素不可见: {desc}")
                        else:
                            self.logger.warning(f"     未找到元素: {desc}")

                    except Exception as e:
                        self.logger.warning(f"  ❌ 点击{desc}失败: {e}")
                        continue

                return False

            # 调用点击函数
            if not try_click_shop_selector():
                self.logger.error("❌ 第一步失败：无法点击'选择店铺'输入框")
                return False

            # ========== 第二步：等待下拉菜单出现 ==========
            self.logger.info("🎯 第二步：等待下拉菜单出现")

            # 增加等待时间，模拟真实用户操作
            wait_seconds = 3 + (time.time() % 3)  # 3-6秒随机等待
            self.logger.info(f"  等待下拉菜单加载 {wait_seconds:.1f} 秒...")
            time.sleep(wait_seconds)

            # 检查下拉菜单是否出现
            try:
                dropdown_xpath = "//div[contains(@class, 'jx-cascader__dropdown')]"
                page.wait_for_selector(f"xpath={dropdown_xpath}", timeout=15000)  # 增加到15秒
                self.logger.info("  ✅ 下拉菜单已显示")
            except Exception as e:
                self.logger.warning(f"  ⚠️ 下拉菜单未显示: {e}")
                self.logger.error("❌ 第二步失败：下拉菜单未出现")
                return False

            # ========== 第三步：选择第一个可用的国家/站点 ==========
            self.logger.info("🎯 第三步：选择第一个可用的国家/站点")

            # 等待国家列表完全加载
            wait_seconds = 2 + (time.time() % 2)  # 2-4秒随机等待
            self.logger.info(f"  等待国家列表加载 {wait_seconds:.1f} 秒...")
            time.sleep(wait_seconds)

            # 尝试选择第一个可用的国家
            def click_first_country_checkbox():
                """选择第一个可用的国家复选框"""
                try:
                    # 获取所有国家选项
                    self.logger.info("  🔍 查找所有可用的国家选项...")

                    # 查找所有级联节点
                    country_nodes = page.query_selector_all("xpath=//li[contains(@class, 'jx-cascader-node')]")
                    self.logger.info(f"  找到 {len(country_nodes)} 个级联节点")

                    if not country_nodes:
                        self.logger.warning("  ⚠️ 未找到任何国家选项")
                        return False, None

                    # 遍历所有节点，找到第一个可见的国家
                    for i, node in enumerate(country_nodes):
                        try:
                            # 检查节点是否可见
                            if not node.is_visible():
                                continue

                            # 获取国家名称 - 使用CSS选择器
                            country_text_element = node.query_selector("span.jx-tooltip__trigger")
                            if not country_text_element:
                                # 尝试其他可能的选择器
                                country_text_element = node.query_selector("span")

                            if not country_text_element:
                                continue

                            country_name = country_text_element.inner_text().strip()
                            if not country_name:
                                continue

                            self.logger.info(f"  🌍 找到国家选项 {i+1}: {country_name}")

                            # 查找该国家的复选框 - 使用CSS选择器
                            checkbox_selectors = [
                                # 方法1: 直接查找复选框内部元素
                                "span.jx-checkbox__inner",
                                # 方法2: 查找复选框容器中的内部元素
                                "span.jx-checkbox span.jx-checkbox__inner",
                                # 方法3: 备用方案 - 点击整个复选框区域
                                "span.jx-checkbox"
                            ]

                            checkbox_clicked = False
                            for j, checkbox_selector in enumerate(checkbox_selectors):
                                try:
                                    checkbox = node.query_selector(checkbox_selector)
                                    if checkbox and checkbox.is_visible():
                                        # 滚动到元素
                                        checkbox.scroll_into_view_if_needed()
                                        time.sleep(0.5)

                                        # 点击复选框
                                        checkbox.click()
                                        self.logger.info(f"  ✅ 成功选择国家: {country_name} (方法{j+1})")

                                        # 等待反应
                                        time.sleep(2)
                                        checkbox_clicked = True
                                        break

                                except Exception as e:
                                    self.logger.warning(f"    尝试点击复选框失败 (方法{j+1}): {e}")
                                    continue

                            if checkbox_clicked:
                                return True, country_name

                        except Exception as e:
                            self.logger.warning(f"  处理国家节点 {i+1} 失败: {e}")
                            continue

                    self.logger.error("  ❌ 所有国家选项都无法点击")
                    return False, None

                except Exception as e:
                    self.logger.error(f"  ❌ 查找国家选项失败: {e}")
                    return False, None

            # 执行选择第一个国家
            country_selected, selected_country_name = click_first_country_checkbox()
            if not country_selected:
                self.logger.error("❌ 第三步失败：无法选择任何国家")
                return False

            # 记录选择的国家
            self.selected_country = selected_country_name or "未知国家"
            self.logger.info(f"  🎉 已选择国家: {self.selected_country}")

            return True

        except Exception as e:
            self.logger.error(f"选择店铺失败: {e}")
            return False

    def _set_product_category_sku(self, page) -> bool:
        """设置产品类别为按单品(SKU)"""
        try:
            self.logger.info("设置产品类别: 按单品(SKU)")

            # 根据HTML结构，按单品(SKU)的value是"2"
            sku_selectors = [
                "//input[@value='2' and @type='radio']",
                "//label[contains(., '按单品(SKU)')]//input[@type='radio']",
                "//span[contains(text(), '按单品(SKU)')]/preceding-sibling::span//input"
            ]

            # 先检查是否已经选中
            for selector in sku_selectors:
                try:
                    element = page.locator(f"xpath={selector}").first
                    if element.is_checked():
                        self.logger.info("✅ 按单品(SKU)已经选中")
                        return True
                except:
                    continue

            # 如果没有选中，尝试点击选择
            for i, selector in enumerate(sku_selectors):
                try:
                    self.logger.info(f"尝试选择按单品(SKU): 方法{i+1}")
                    element = page.locator(f"xpath={selector}").first
                    element.click()
                    self.logger.info(f"✅ 成功选择按单品(SKU): 方法{i+1}")
                    return True
                except Exception as e:
                    self.logger.warning(f"选择按单品(SKU)失败: 方法{i+1} - {e}")
                    continue

            self.logger.error("❌ 无法设置产品类别为按单品(SKU)")
            return False

        except Exception as e:
            self.logger.error(f"设置产品类别失败: {e}")
            return False

    def _enable_auto_extension(self, page) -> bool:
        """开启自动延续"""
        try:
            self.logger.info("开启自动延续")

            # 自动延续开关选择器
            switch_selectors = [
                "//div[contains(@class, 'jx-switch')]",
                "//span[contains(@class, 'jx-switch__core')]",
                "//input[@type='checkbox'][@role='switch']"
            ]

            for i, selector in enumerate(switch_selectors):
                try:
                    self.logger.info(f"尝试开启自动延续: 方法{i+1}")
                    element = page.locator(f"xpath={selector}").first

                    # 检查是否已经开启
                    if "is-checked" in element.get_attribute("class", ""):
                        self.logger.info("✅ 自动延续已经开启")
                        return True

                    element.click()
                    self.logger.info(f"✅ 成功开启自动延续: 方法{i+1}")
                    return True
                except Exception as e:
                    self.logger.warning(f"开启自动延续失败: 方法{i+1} - {e}")
                    continue

            self.logger.error("❌ 无法开启自动延续")
            return False

        except Exception as e:
            self.logger.error(f"开启自动延续失败: {e}")
            return False

    def _submit_create_activity(self, page) -> bool:
        """提交创建活动"""
        try:
            self.logger.info("提交创建活动")

            # 创建活动按钮选择器
            submit_selectors = [
                "//button[contains(text(), '创建活动')]",
                "//button[contains(@class, 'jx-button--primary')]",
                "//span[contains(text(), '创建活动')]/parent::button"
            ]

            for i, selector in enumerate(submit_selectors):
                try:
                    self.logger.info(f"尝试点击创建活动按钮: 方法{i+1}")
                    element = page.locator(f"xpath={selector}").first
                    element.click()
                    self.logger.info(f"✅ 成功点击创建活动按钮: 方法{i+1}")

                    # 等待页面响应
                    time.sleep(3)
                    return True
                except Exception as e:
                    self.logger.warning(f"点击创建活动按钮失败: 方法{i+1} - {e}")
                    continue

            self.logger.error("❌ 无法点击创建活动按钮")
            return False

        except Exception as e:
            self.logger.error(f"提交创建活动失败: {e}")
            return False



    def _set_product_category_sku(self, page) -> bool:
        """设置产品类别为按单品(SKU)"""
        try:
            self.logger.info("设置产品类别: 按单品(SKU)")

            # 根据HTML结构，按单品(SKU)的value是"2"
            sku_selectors = [
                "//input[@value='2' and @type='radio']",
                "//label[contains(., '按单品(SKU)')]//input[@type='radio']",
                "//span[contains(text(), '按单品(SKU)')]/preceding-sibling::span//input"
            ]

            # 先检查是否已经选中
            for selector in sku_selectors:
                try:
                    element = page.locator(f"xpath={selector}").first
                    if element.is_checked():
                        self.logger.info("✅ 按单品(SKU)已经被选中")
                        return True
                except:
                    continue

            # 如果没有选中，则点击选择
            for i, selector in enumerate(sku_selectors):
                self.logger.info(f"尝试选择按单品(SKU) (选择器 {i+1})")
                try:
                    # 使用JavaScript点击，确保能够触发事件
                    js_script = f'''
                    (function() {{
                        const xpath = "{selector}";
                        const element = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                        if (element) {{
                            element.click();
                            element.checked = true;
                            // 触发change事件
                            const event = new Event('change', {{ bubbles: true }});
                            element.dispatchEvent(event);
                            return true;
                        }}
                        return false;
                    }})()
                    '''

                    result = page.evaluate(js_script)
                    if result:
                        self.logger.info("✅ 成功选择按单品(SKU)")

                        # 随机等待1-2秒
                        wait_seconds = 1 + (time.time() % 1)
                        self.logger.info(f"等待选择生效 {wait_seconds:.1f} 秒...")
                        time.sleep(wait_seconds)
                        return True

                except Exception as e:
                    self.logger.warning(f"选择器 {i+1} 失败: {e}")
                    continue

            self.logger.error("所有选择器都失败，无法选择按单品(SKU)")
            return False

        except Exception as e:
            self.logger.error(f"设置产品类别失败: {e}")
            return False

    def _enable_auto_extension(self, page) -> bool:
        """开启自动延续"""
        try:
            self.logger.info("检查并开启自动延续开关")

            # 根据HTML结构，自动延续开关的精确选择器
            switch_selectors = [
                "//input[@class='jx-switch__input' and @type='checkbox' and @role='switch']",
                "//div[contains(@class, 'auto-extension-label')]/following-sibling::div//input[@type='checkbox']",
                "//div[contains(@class, 'jx-switch')]//input[@class='jx-switch__input']",
                "//input[@type='checkbox' and @role='switch' and contains(@true-value, '1')]"
            ]

            # 检查当前状态
            for i, selector in enumerate(switch_selectors):
                self.logger.info(f"检查自动延续开关 (选择器 {i+1})")
                try:
                    # 查找开关元素（不等待可见性，因为可能是隐藏的）
                    switch_elements = page.query_selector_all(f"xpath={selector}")

                    if switch_elements:
                        switch_element = switch_elements[0]

                        # 检查当前状态
                        is_checked = switch_element.is_checked()
                        aria_checked = switch_element.get_attribute("aria-checked")

                        self.logger.info(f"自动延续状态: is_checked={is_checked}, aria-checked={aria_checked}")

                        # 如果已经开启，直接返回成功
                        if is_checked or aria_checked == "true":
                            self.logger.info("✅ 自动延续已经开启")
                            return True

                        # 如果未开启，尝试点击开启
                        self.logger.info("尝试开启自动延续")

                        # 尝试点击可见的开关容器
                        switch_container_selectors = [
                            "//div[contains(@class, 'jx-switch')]",
                            "//span[contains(@class, 'jx-switch__core')]"
                        ]

                        for container_selector in switch_container_selectors:
                            try:
                                container_elements = page.query_selector_all(f"xpath={container_selector}")
                                if container_elements:
                                    container = container_elements[0]
                                    if container.is_visible():
                                        container.click()
                                        self.logger.info("✅ 成功点击自动延续开关")
                                        time.sleep(1)
                                        return True
                            except Exception as e:
                                self.logger.warning(f"点击开关容器失败: {e}")
                                continue

                        # 如果点击容器失败，尝试JavaScript
                        try:
                            js_script = '''
                            (function() {
                                const switches = document.querySelectorAll('div.jx-switch');
                                for (let sw of switches) {
                                    if (sw.offsetParent !== null) { // 检查是否可见
                                        sw.click();
                                        return true;
                                    }
                                }
                                return false;
                            })()
                            '''

                            result = page.evaluate(js_script)
                            if result:
                                self.logger.info("✅ JavaScript成功开启自动延续")
                                time.sleep(1)
                                return True
                        except Exception as e:
                            self.logger.warning(f"JavaScript开启失败: {e}")

                        break

                except Exception as e:
                    self.logger.warning(f"检查自动延续选择器 {i+1} 失败: {e}")
                    continue

            # 如果所有方法都失败，但这不应该阻止流程继续
            self.logger.warning("⚠️ 无法确认自动延续状态，但继续执行")
            return True

        except Exception as e:
            self.logger.error(f"开启自动延续失败: {e}")
            return False

    def _submit_create_activity(self, page) -> bool:
        """提交创建活动"""
        try:
            self.logger.info("准备提交创建活动")

            # 根据HTML结构，创建活动按钮的选择器
            create_btn_selectors = [
                # 方法1: 通过按钮class和内部span文本
                "//button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button')]//span[contains(text(), '创建活动')]/..",
                # 方法2: 直接通过按钮class和文本内容
                "//button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button') and contains(., '创建活动') and not(contains(., '取消'))]",
                # 方法3: 通过span文本找到父级button
                "//span[text()='创建活动']/parent::button",
                # 方法4: 更宽泛的匹配
                "//button[contains(@class, 'jx-button--primary')]//span[contains(text(), '创建活动')]/ancestor::button",
                # 方法5: 备用方案
                "//button[@type='button' and contains(@class, 'jx-button--primary') and contains(., '创建活动')]"
            ]

            # 尝试点击创建活动按钮
            clicked = False
            for i, selector in enumerate(create_btn_selectors):
                self.logger.info(f"尝试点击创建活动按钮 (选择器 {i+1})")
                self.logger.info(f"   选择器: {selector}")
                try:
                    # 查找按钮元素
                    elements = page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"   找到 {len(elements)} 个匹配元素")

                    if elements:
                        button = elements[0]

                        # 检查按钮可见性和可用性
                        is_visible = button.is_visible()
                        is_enabled = button.is_enabled()
                        self.logger.info(f"   按钮状态: 可见={is_visible}, 可用={is_enabled}")

                        if is_visible and is_enabled:
                            # 滚动到按钮位置
                            button.scroll_into_view_if_needed()
                            time.sleep(0.5)

                            # 点击按钮
                            button.click()
                            self.logger.info(f"✅ 成功点击创建活动按钮 (选择器 {i+1})")
                            clicked = True
                            break
                        else:
                            self.logger.warning(f"   按钮不可点击: 可见={is_visible}, 可用={is_enabled}")
                    else:
                        self.logger.warning(f"   未找到匹配的按钮元素")

                except Exception as e:
                    self.logger.warning(f"创建按钮选择器 {i+1} 失败: {e}")
                    continue

            if not clicked:
                self.logger.error("❌ 无法点击创建活动按钮")
                # 尝试JavaScript点击作为最后手段
                try:
                    self.logger.info("🔧 尝试JavaScript点击创建活动按钮...")
                    js_script = '''
                    (function() {
                        // 查找创建活动按钮
                        const buttons = document.querySelectorAll('button');
                        for (let btn of buttons) {
                            if (btn.textContent.includes('创建活动') && !btn.textContent.includes('取消')) {
                                btn.click();
                                return true;
                            }
                        }
                        return false;
                    })()
                    '''

                    result = page.evaluate(js_script)
                    if result:
                        self.logger.info("✅ JavaScript成功点击创建活动按钮")
                        clicked = True
                    else:
                        self.logger.error("❌ JavaScript也无法找到创建活动按钮")
                        return False

                except Exception as e:
                    self.logger.error(f"JavaScript点击失败: {e}")
                    return False

            # 等待提交处理并检查结果
            self.logger.info("等待活动创建处理...")

            success_found = False

            # 检查多种成功指标
            for attempt in range(3):  # 尝试3次，每次等待更长时间
                wait_seconds = 3 + attempt * 2  # 3秒、5秒、7秒
                self.logger.info(f"等待活动创建处理 {wait_seconds} 秒... (尝试 {attempt + 1}/3)")
                time.sleep(wait_seconds)

                current_url = page.url
                self.logger.info(f"当前页面URL: {current_url}")

                # 检查1: 页面是否跳转到列表页面
                if "/flashSale" in current_url and "/create" not in current_url:
                    self.logger.info("✅ 页面已跳转到秒杀活动列表，创建成功")
                    success_found = True
                    break

                # 检查2: 是否有成功提示消息
                success_message_selectors = [
                    "//div[contains(@class, 'jx-message') and contains(., '成功')]",
                    "//div[contains(@class, 'jx-message') and contains(., '创建')]",
                    "//div[contains(@class, 'success')]",
                    "//span[contains(text(), '创建成功')]",
                    "//div[contains(text(), '创建成功')]",
                    "//div[contains(@class, 'jx-notification') and contains(., '成功')]"
                ]

                for selector in success_message_selectors:
                    try:
                        elements = page.query_selector_all(f"xpath={selector}")
                        if elements and any(elem.is_visible() for elem in elements):
                            self.logger.info("✅ 检测到成功提示消息")
                            success_found = True
                            break
                    except:
                        continue

                if success_found:
                    break

                # 检查3: 页面是否还在加载
                try:
                    # 检查是否有加载指示器
                    loading_selectors = [
                        "//div[contains(@class, 'loading')]",
                        "//div[contains(@class, 'jx-loading')]",
                        "//span[contains(@class, 'jx-loading')]"
                    ]

                    still_loading = False
                    for selector in loading_selectors:
                        try:
                            elements = page.query_selector_all(f"xpath={selector}")
                            if elements and any(elem.is_visible() for elem in elements):
                                self.logger.info("⏳ 页面仍在加载中...")
                                still_loading = True
                                break
                        except:
                            continue

                    if not still_loading:
                        # 如果没有加载指示器，检查创建按钮是否还存在且可点击
                        create_btn_exists = False
                        try:
                            create_btns = page.query_selector_all("xpath=//button[contains(text(), '创建活动')]")
                            create_btn_exists = any(btn.is_visible() and btn.is_enabled() for btn in create_btns)
                        except:
                            pass

                        if not create_btn_exists:
                            self.logger.info("✅ 创建按钮已消失，可能创建成功")
                            success_found = True
                            break

                except Exception as e:
                    self.logger.warning(f"检查加载状态失败: {e}")

            # 最终检查：如果没有明确的成功指示，但页面状态合理，也认为成功
            if not success_found:
                current_url = page.url
                if "/create" in current_url:
                    # 还在创建页面，检查是否有错误提示
                    error_selectors = [
                        "//div[contains(@class, 'jx-message') and contains(@class, 'error')]",
                        "//div[contains(@class, 'error')]",
                        "//span[contains(@class, 'error')]"
                    ]

                    has_error = False
                    for selector in error_selectors:
                        try:
                            elements = page.query_selector_all(f"xpath={selector}")
                            if elements and any(elem.is_visible() for elem in elements):
                                error_text = elements[0].inner_text()
                                self.logger.error(f"❌ 检测到错误提示: {error_text}")
                                has_error = True
                                break
                        except:
                            continue

                    if not has_error:
                        self.logger.info("✅ 未检测到错误，认为创建请求已成功提交")
                        success_found = True
                else:
                    self.logger.info("✅ 页面已离开创建页面，认为创建成功")
                    success_found = True

            # 记录创建结果
            data = {
                '时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'Container': self.container_code,
                '环境名称': self.env_name,
                '国家': getattr(self, 'selected_country', '未知'),
                '店铺': getattr(self, 'selected_shop', '未知'),
                '活动名称': '系统默认名称',
                '产品类别': '按单品(SKU)',
                '自动延续': '已开启',
                '操作类型': '创建限时秒杀活动',
                '操作结果': '成功' if success_found else '已提交',
                '页面URL': current_url
            }
            write_to_excel_realtime(data, f'FlashSale_Results_{current_date}.xlsx')

            if success_found:
                self.logger.info("✅ 活动创建成功")
            else:
                self.logger.info("✅ 活动创建请求已提交")

            return True

        except Exception as e:
            self.logger.error(f"提交创建活动失败: {e}")
            return False

    def add_products_to_flash_sale(self) -> bool:
        """
        为未开始的秒杀活动添加产品 - 完整流程

        Returns:
            bool: 操作是否成功
        """
        try:
            self.logger.info("🚀 开始为未开始的秒杀活动添加产品")

            # 获取当前页面
            page = self.browser_context.pages[-1]
            current_date = datetime.now().strftime('%Y-%m-%d')

            # ========== 第0步：关闭除第一个标签页外的其他标签页 ==========
            self.logger.info("🎯 第0步：关闭多余的标签页")

            try:
                all_pages = self.browser_context.pages
                self.logger.info(f"当前打开的标签页数量: {len(all_pages)}")

                if len(all_pages) > 1:
                    # 保留第一个标签页，关闭其他的
                    main_page = all_pages[0]
                    for i, page_to_close in enumerate(all_pages[1:], 1):
                        try:
                            page_url = page_to_close.url
                            self.logger.info(f"关闭标签页 {i+1}: {page_url}")
                            page_to_close.close()
                        except Exception as e:
                            self.logger.warning(f"关闭标签页 {i+1} 失败: {e}")

                    # 使用第一个标签页作为主页面
                    page = main_page
                    self.logger.info("✅ 已关闭多余标签页，使用第一个标签页")
                else:
                    self.logger.info("✅ 只有一个标签页，无需关闭")

            except Exception as e:
                self.logger.warning(f"关闭标签页时出错: {e}")

            # ========== 第一步：导航到秒杀活动页面 ==========
            self.logger.info("🎯 第一步：导航到秒杀活动页面")

            flash_sale_url = "https://erp.91miaoshou.com/tiktok/marketing/flashSale"

            try:
                # 使用更宽松的等待条件，避免超时
                page.goto(flash_sale_url, wait_until='domcontentloaded', timeout=60000)
                self.logger.info("✅ 页面DOM加载完成")

                # 等待页面稳定
                time.sleep(5)

                # 检查页面是否正确加载
                current_url = page.url
                if "flashSale" in current_url:
                    self.logger.info("✅ 成功导航到秒杀活动页面")
                else:
                    self.logger.warning(f"页面URL可能不正确: {current_url}")

            except Exception as e:
                self.logger.error(f"导航到秒杀页面失败: {e}")
                return False

            # ========== 第二步：点击未开始标签 ==========
            self.logger.info("🎯 第二步：点击未开始标签")

            # 等待页面稳定
            wait_seconds = 2 + (time.time() % 2)  # 2-4秒随机等待
            self.logger.info(f"等待页面稳定 {wait_seconds:.1f} 秒...")
            time.sleep(wait_seconds)

            # 点击未开始标签的选择器 - 根据您提供的HTML结构优化
            not_started_selectors = [
                # 方法1: 点击包含"未开始"文本的span元素（最直接）
                #"//span[@class='jx-radio-button__inner'][contains(text(), '未开始')]",
                # 方法2: 点击整个label标签
                #"//label[contains(@class, 'jx-radio-button')][.//span[contains(text(), '未开始')]]",
                # 方法3: 点击radio input（value=1对应未开始）
                #"//input[@class='jx-radio-button__original-radio'][@type='radio'][@value='1']",
                # 方法4: 通过更精确的class定位label
                #"//label[@class='jx-radio-button is-active jx-radio-button--default pro-radio-button'][.//span[contains(text(), '未开始')]]",
                # 方法5: 通过文本内容模糊匹配（处理数字变化）
                #"//span[contains(@class, 'jx-radio-button__inner')][contains(text(), '未开始')]",
                # 方法6: 备用方案 - 通过父级label定位
                "//label[contains(@class, 'pro-radio-button')][contains(., '未开始')]"
            ]

            not_started_clicked = False
            for i, selector in enumerate(not_started_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击未开始标签: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    # 查找元素
                    elements = page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个未开始标签")

                    if elements:
                        element = elements[0]

                        # 检查元素可见性
                        is_visible = element.is_visible()
                        self.logger.info(f"     元素可见性: {is_visible}")

                        if is_visible:
                            # 滚动到元素
                            element.scroll_into_view_if_needed()
                            time.sleep(0.5)

                            # 点击未开始标签
                            element.click()
                            self.logger.info(f"  ✅ 成功点击未开始标签: 方法{i+1}")

                            # 等待列表刷新
                            time.sleep(2)
                            not_started_clicked = True
                            break
                        else:
                            self.logger.warning(f"     未开始标签不可见: 方法{i+1}")
                    else:
                        self.logger.warning(f"     未找到未开始标签: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击未开始标签失败: 方法{i+1} - {e}")
                    continue

            if not not_started_clicked:
                self.logger.error("❌ 第二步失败：无法点击未开始标签")
                # 保存失败时的页面状态用于调试
                self.save_page_debug_info(page, "failed_click_not_started")
                return False

            self.logger.info("✅ 成功点击未开始标签")

            # ========== 第三步：点击第一个活动的管理产品 ==========
            self.logger.info("🎯 第三步：点击第一个活动的管理产品")

            # 等待活动列表加载
            wait_seconds = 3 + (time.time() % 2)  # 3-5秒随机等待
            self.logger.info(f"等待活动列表加载 {wait_seconds:.1f} 秒...")
            time.sleep(wait_seconds)

            # 🔍 保存点击未开始后的页面状态用于调试
            self.logger.info("🔍 保存点击未开始后的页面状态...")
            self.save_page_debug_info(page, "after_click_not_started")

            # 🔍 调试：查看页面上的所有按钮和链接
            self.logger.info("🔍 调试：查看页面上的所有按钮和链接...")
            try:
                # 获取所有按钮
                js_script = '''
                (function() {
                    const buttons = document.querySelectorAll('button, a, span');
                    const buttonInfo = [];
                    buttons.forEach((btn, index) => {
                        const text = btn.innerText.trim();
                        if (text && (text.includes('管理') || text.includes('产品') || text.includes('操作') || text.includes('编辑'))) {
                            buttonInfo.push({
                                index: index,
                                tagName: btn.tagName,
                                text: text,
                                className: btn.className,
                                visible: btn.offsetParent !== null
                            });
                        }
                    });
                    return buttonInfo;
                })()
                '''

                buttons = page.evaluate(js_script)
                self.logger.info(f"找到 {len(buttons)} 个可能的操作按钮:")

                for btn in buttons:
                    self.logger.info(f"  {btn['tagName']} {btn['index']}: '{btn['text']}' (可见: {btn['visible']})")

            except Exception as e:
                self.logger.warning(f"调试按钮失败: {e}")

            # 管理产品按钮的选择器 - 根据您提供的HTML结构精确定位
            manage_product_selectors = [
                # 方法1: 精确定位第一个管理产品按钮 - 根据日志分析优化
                "(//button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button')][contains(., '管理产品')])[1]",
                # 方法2: 通过虚拟表格行定位第一个管理产品按钮
                "//div[contains(@class, 'pro-virtual-scroll__row')][1]//button[contains(., '管理产品')]",
                # 方法3: 通过span内的文本定位父级button（第一个）
                "(//span[contains(text(), '管理产品')]/parent::button)[1]",
                # 方法4: 在操作列中查找第一个管理产品按钮
                "(//div[contains(@class, 'operator-col')]//button[contains(., '管理产品')])[1]",
                # 方法5: 在按钮组中查找第一个管理产品按钮
                "(//div[contains(@class, 'jx-button-group')]//button[contains(., '管理产品')])[1]",
                # 方法6: 更精确的类名匹配（第一个）
                "(//button[contains(@class, 'jx-button--primary') and contains(@class, 'jx-button--small') and contains(@class, 'is-text') and contains(@class, 'pro-button')][contains(., '管理产品')])[1]",
                # 方法7: 通过aria-disabled属性定位第一个可点击的管理产品按钮
                "(//button[@aria-disabled='false'][contains(., '管理产品')])[1]",
                # 方法8: 备用方案 - 任何包含"管理产品"文本的第一个按钮
                "(//button[contains(text(), '管理产品')])[1]"
            ]

            manage_product_clicked = False
            for i, selector in enumerate(manage_product_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击管理产品: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    # 查找元素
                    elements = page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个管理产品按钮")

                    if elements:
                        element = elements[0]  # 选择第一个

                        # 检查元素可见性
                        is_visible = element.is_visible()
                        self.logger.info(f"     元素可见性: {is_visible}")

                        if is_visible:
                            # 滚动到元素
                            element.scroll_into_view_if_needed()
                            time.sleep(0.5)

                            # 点击管理产品按钮
                            element.click()
                            self.logger.info(f"  ✅ 成功点击管理产品: 方法{i+1}")

                            # 等待新标签页打开
                            time.sleep(3)
                            manage_product_clicked = True
                            break
                        else:
                            self.logger.warning(f"     管理产品按钮不可见: 方法{i+1}")
                    else:
                        self.logger.warning(f"     未找到管理产品按钮: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击管理产品失败: 方法{i+1} - {e}")
                    continue

            if not manage_product_clicked:
                self.logger.error("❌ 第三步失败：无法点击管理产品")
                # 保存失败时的页面状态用于调试
                self.save_page_debug_info(page, "failed_click_manage_product")
                return False

            self.logger.info("✅ 成功点击管理产品按钮")

            # ========== 第四步：处理新标签页并添加产品 ==========
            self.logger.info("🎯 第四步：处理新标签页并添加产品")

            # 等待新标签页打开 - 增加更多等待时间
            self.logger.info("等待新标签页打开...")
            time.sleep(5)  # 从2秒增加到5秒

            # 检查新标签页是否已打开，如果没有则继续等待
            max_wait_attempts = 15  # 最多等待15次
            wait_attempt = 0

            while wait_attempt < max_wait_attempts:
                # 强制刷新页面列表
                try:
                    # 尝试获取最新的页面列表
                    pages = self.browser_context.pages

                    # 额外检查：尝试通过浏览器对象获取页面
                    if hasattr(self.browser_context, '_browser'):
                        browser_pages = []
                        for context in self.browser_context._browser.contexts:
                            browser_pages.extend(context.pages)
                        if len(browser_pages) != len(pages):
                            self.logger.info(f"发现页面数量不一致: context.pages={len(pages)}, browser.pages={len(browser_pages)}")
                            pages = browser_pages

                except Exception as e:
                    self.logger.warning(f"获取页面列表时出错: {e}")
                    pages = self.browser_context.pages

                self.logger.info(f"等待尝试 {wait_attempt + 1}/{max_wait_attempts}: 当前标签页数量 {len(pages)}")

                # 打印所有页面的URL用于调试
                for i, p in enumerate(pages):
                    try:
                        url = p.url
                        title = p.title()
                        self.logger.info(f"  标签页 {i+1}: URL={url}, 标题={title}")
                    except Exception as e:
                        self.logger.warning(f"  标签页 {i+1}: 无法获取信息 - {e}")

                if len(pages) > 1:
                    self.logger.info("✅ 检测到新标签页已打开")
                    break

                wait_attempt += 1
                time.sleep(2)  # 每次等待2秒

            if wait_attempt >= max_wait_attempts:
                self.logger.warning("⚠️ 等待新标签页超时，但继续尝试处理")

            # 获取所有页面
            pages = self.browser_context.pages
            self.logger.info(f"当前打开的标签页数量: {len(pages)}")

            # 切换到最新的标签页（产品管理页面）
            if len(pages) > 1:
                product_page = pages[-1]  # 最后一个页面
                self.logger.info(f"切换到产品管理页面: {product_page.url}")

                # 等待页面加载 - 增加更多等待时间
                try:
                    self.logger.info("等待产品管理页面加载...")
                    product_page.wait_for_load_state('domcontentloaded', timeout=30000)
                    self.logger.info("DOM内容加载完成，继续等待页面稳定...")

                    # 增加等待时间，确保页面完全加载
                    time.sleep(5)

                    # 等待网络空闲
                    try:
                        product_page.wait_for_load_state('networkidle', timeout=15000)
                        self.logger.info("网络空闲状态达成")
                    except:
                        self.logger.warning("网络空闲等待超时，继续执行")

                    # 再等待一段时间确保所有元素都已渲染
                    time.sleep(3)
                    self.logger.info("✅ 产品管理页面加载完成")

                except Exception as e:
                    self.logger.warning(f"等待页面加载失败: {e}")

                # 🔍 保存产品管理页面的调试信息
                self.logger.info("🔍 保存产品管理页面调试信息...")
                self.save_page_debug_info(product_page, "product_management_page")

                # 🔍 调试：查看页面上的所有按钮
                self.logger.info("🔍 调试：查看产品管理页面上的所有按钮...")
                try:
                    js_script = '''
                    (function() {
                        const buttons = document.querySelectorAll('button, a');
                        const buttonInfo = [];
                        buttons.forEach((btn, index) => {
                            const text = btn.innerText.trim();
                            if (text) {
                                buttonInfo.push({
                                    index: index,
                                    tagName: btn.tagName,
                                    text: text,
                                    className: btn.className,
                                    visible: btn.offsetParent !== null
                                });
                            }
                        });
                        return buttonInfo;
                    })()
                    '''

                    buttons = product_page.evaluate(js_script)
                    self.logger.info(f"找到 {len(buttons)} 个按钮:")

                    for btn in buttons[:15]:  # 显示前15个按钮
                        self.logger.info(f"  {btn['tagName']} {btn['index']}: '{btn['text']}' (可见: {btn['visible']})")

                except Exception as e:
                    self.logger.warning(f"调试按钮失败: {e}")

                # ========== 第五步：点击添加产品按钮 ==========
                self.logger.info("🎯 第五步：点击添加产品按钮")

                # 再等待一下确保按钮完全加载
                time.sleep(2)

                # 添加产品按钮的选择器 - 根据您提供的HTML结构
                add_product_selectors = [
                    # 方法1: 精确定位添加产品按钮
                    #"//button[contains(@class, 'pro-button')][.//span[contains(text(), '添加产品')]]",
                    # 方法2: 通过按钮类名和文本内容定位
                    "//button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button')][contains(., '添加产品')]",
                    # 方法3: 通过span内的文本定位父级button
                    "//span[contains(text(), '添加产品')]/parent::button",
                    # 方法4: 在操作区域中查找添加产品按钮
                    "//div[contains(@class, 'operate-left-box')]//button[contains(., '添加产品')]",
                    # 方法5: 更宽泛的定位
                    "//button[contains(text(), '添加产品')]",
                    # 方法6: 通过aria-disabled属性定位可点击的添加产品按钮
                    "//button[@aria-disabled='false'][contains(., '添加产品')]"
                ]

                add_product_clicked = False
                for i, selector in enumerate(add_product_selectors):
                    try:
                        self.logger.info(f"  🎯 尝试点击添加产品: 方法{i+1}")
                        self.logger.info(f"     选择器: {selector}")

                        # 查找元素
                        elements = product_page.query_selector_all(f"xpath={selector}")
                        self.logger.info(f"     找到 {len(elements)} 个添加产品按钮")

                        if elements:
                            element = elements[0]  # 选择第一个

                            # 检查元素可见性
                            is_visible = element.is_visible()
                            self.logger.info(f"     元素可见性: {is_visible}")

                            if is_visible:
                                # 滚动到元素
                                element.scroll_into_view_if_needed()
                                time.sleep(0.5)

                                # 点击添加产品按钮
                                element.click()
                                self.logger.info(f"  ✅ 成功点击添加产品: 方法{i+1}")

                                # 等待页面响应
                                time.sleep(2)
                                add_product_clicked = True
                                break
                            else:
                                self.logger.warning(f"     添加产品按钮不可见: 方法{i+1}")
                        else:
                            self.logger.warning(f"     未找到添加产品按钮: 方法{i+1}")

                    except Exception as e:
                        self.logger.warning(f"  ❌ 点击添加产品失败: 方法{i+1} - {e}")
                        continue

                if not add_product_clicked:
                    self.logger.error("❌ 第五步失败：无法点击添加产品")
                    # 保存失败时的页面状态用于调试
                    self.save_page_debug_info(product_page, "failed_click_add_product")
                    return False

                self.logger.info("✅ 成功点击添加产品按钮")

                # 等待添加产品弹窗加载
                time.sleep(6)

                # ========== 第六步：处理添加产品弹窗 ==========
                self.logger.info("🎯 第六步：处理添加产品弹窗")

                # 步骤1：勾选"隐藏已参与限时秒杀的产品"
                self.logger.info("📋 步骤1：勾选隐藏已参与限时秒杀的产品")
                time.sleep(2)  # 等待弹窗完全加载

                hide_checkbox_selectors = [
                    # 方法1: 通过复选框标签定位
                    "//label[contains(@class, 'jx-checkbox')][contains(., '隐藏已参与限时秒杀的产品')]",
                    # 方法2: 通过复选框input定位
                    "//input[@type='checkbox'][following-sibling::*[contains(text(), '隐藏已参与限时秒杀的产品')]]",
                    # 方法3: 通过span文本定位父级label
                    "//span[contains(text(), '隐藏已参与限时秒杀的产品')]/parent::label",
                    # 方法4: 更精确的类名匹配
                    "//label[contains(@class, 'jx-checkbox') and contains(@class, 'pro-checkbox')][contains(., '隐藏已参与限时秒杀的产品')]"
                ]

                hide_checkbox_clicked = False
                for i, selector in enumerate(hide_checkbox_selectors):
                    try:
                        self.logger.info(f"  🎯 尝试勾选隐藏复选框: 方法{i+1}")
                        elements = product_page.query_selector_all(f"xpath={selector}")

                        if elements:
                            element = elements[0]
                            if element.is_visible():
                                element.click()
                                self.logger.info(f"  ✅ 成功勾选隐藏复选框: 方法{i+1}")
                                time.sleep(3)  # 等待页面响应
                                hide_checkbox_clicked = True
                                break
                    except Exception as e:
                        self.logger.warning(f"  ❌ 勾选隐藏复选框失败: 方法{i+1} - {e}")

                if not hide_checkbox_clicked:
                    self.logger.warning("⚠️ 未能勾选隐藏复选框，继续执行")

                # 等待复选框操作完成，页面数据更新
                time.sleep(4)

                # 步骤2：获取可添加产品总数量
                self.logger.info("📊 步骤2：获取可添加产品总数量")

                total_count = 0
                total_selectors = [
                    "//div[contains(@class, 'jx-pagination__total')]//span",
                    "//span[contains(text(), '条')]",
                    "//div[contains(@class, 'jx-pagination__total')]",
                    "//div[contains(@class, 'pagination')]//span[contains(text(), '条')]",
                    "//*[contains(text(), '条') and contains(text(), '共')]"
                ]

                # 先尝试获取页面上所有可能包含数量信息的元素
                self.logger.info("🔍 调试：查找所有可能包含数量信息的元素...")
                try:
                    all_text_elements = product_page.query_selector_all("xpath=//*[contains(text(), '条')]")
                    self.logger.info(f"找到 {len(all_text_elements)} 个包含'条'的元素:")
                    for i, elem in enumerate(all_text_elements[:10]):  # 只显示前10个
                        try:
                            text = elem.inner_text().strip()
                            if text:
                                self.logger.info(f"  元素 {i+1}: '{text}'")
                        except:
                            pass
                except Exception as e:
                    self.logger.warning(f"调试查找元素失败: {e}")

                # 尝试获取总数
                for i, selector in enumerate(total_selectors):
                    try:
                        self.logger.info(f"🎯 尝试获取总数: 方法{i+1} - {selector}")
                        elements = product_page.query_selector_all(f"xpath={selector}")
                        self.logger.info(f"     找到 {len(elements)} 个匹配元素")

                        if elements:
                            for j, element in enumerate(elements):
                                try:
                                    text = element.inner_text().strip()
                                    self.logger.info(f"     元素 {j+1} 文本: '{text}'")

                                    # 提取数字
                                    import re
                                    numbers = re.findall(r'\d+', text)
                                    if numbers:
                                        potential_count = int(numbers[0])
                                        self.logger.info(f"     提取到数字: {potential_count}")

                                        # 如果数字看起来合理（不是页码等小数字）
                                        if potential_count > 0:
                                            total_count = potential_count
                                            self.logger.info(f"📊 检测到可添加产品总数: {total_count}")
                                            break
                                except Exception as e:
                                    self.logger.warning(f"     处理元素文本失败: {e}")

                            if total_count > 0:
                                break

                    except Exception as e:
                        self.logger.warning(f"获取产品总数失败: 方法{i+1} - {e}")

                if total_count == 0:
                    self.logger.warning("⚠️ 未能获取产品总数，默认按小于1000处理")
                    total_count = 500

                # 等待数据分析完成
                time.sleep(3)

                # 步骤3：根据产品数量选择不同的操作策略
                if total_count > 1000:
                    self.logger.info(f"📈 产品数量 {total_count} > 1000，执行分页策略")
                    time.sleep(2)  # 策略决策后等待

                    # 3.1: 点击每页显示数量选择器
                    self.logger.info("🔽 点击每页显示数量选择器")

                    # 尝试使用JavaScript直接点击元素，绕过遮挡问题
                    try:
                        self.logger.info("  🎯 尝试使用JavaScript直接点击")
                        # 使用JavaScript定位并点击元素
                        js_click_result = product_page.evaluate("""
                            () => {
                                // 尝试多种选择器
                                const selectors = [
                                    // 精确匹配20条/页文本
                                    "span.jx-pagination__sizes span:contains('20条/页')",
                                    // 分页大小选择器
                                    ".jx-pagination__sizes .jx-select__placeholder span",
                                    // 任何包含"条/页"的span
                                    "span:contains('条/页')"
                                ];

                                // 遍历所有选择器
                                for (const selector of selectors) {
                                    try {
                                        // 使用jQuery选择器查找元素
                                        const elements = document.querySelectorAll(selector);
                                        if (elements && elements.length > 0) {
                                            // 找到元素，模拟点击
                                            elements[0].click();
                                            return { success: true, message: `成功点击: ${selector}` };
                                        }
                                    } catch (e) {
                                        console.error(`选择器 ${selector} 失败:`, e);
                                    }
                                }

                                // 尝试直接通过类名查找
                                try {
                                    const paginationSizes = document.querySelector('.jx-pagination__sizes');
                                    if (paginationSizes) {
                                        const placeholder = paginationSizes.querySelector('.jx-select__placeholder');
                                        if (placeholder) {
                                            placeholder.click();
                                            return { success: true, message: "成功点击分页大小选择器" };
                                        }
                                    }
                                } catch (e) {
                                    console.error("直接查找失败:", e);
                                }

                                return { success: false, message: "未找到匹配的元素" };
                            }
                        """)

                        if js_click_result and js_click_result.get('success'):
                            self.logger.info(f"  ✅ JavaScript点击成功: {js_click_result.get('message')}")
                            time.sleep(2)  # 等待下拉菜单显示
                        else:
                            self.logger.warning(f"  ❌ JavaScript点击失败: {js_click_result.get('message') if js_click_result else '未知错误'}")

                            # 如果JavaScript方法失败，尝试常规方法
                            page_size_selectors = [
                                # 方法1: 根据您提供的最新HTML结构，精确定位
                                "//div[contains(@class, 'jx-select__placeholder')]/span[text()='20条/页']",
                                # 方法2: 通过分页组件定位
                                "//span[contains(@class, 'jx-pagination__sizes')]//span[contains(text(), '条/页')]",
                                # 方法3: 更精确的路径
                                "//div[contains(@class, 'pro-pagination')]//span[contains(text(), '20条/页')]",
                                # 方法4: 通过分页组件和选择器组合定位
                                "//div[contains(@class, 'jx-pagination')]//div[contains(@class, 'jx-select__placeholder')]",
                                # 方法5: 备用方案 - 任何包含20条/页的元素
                                "//span[contains(text(), '20条/页')]"
                            ]
                    except Exception as e:
                        self.logger.warning()

                    page_size_clicked = False
                    for i, selector in enumerate(page_size_selectors):
                        try:
                            elements = product_page.query_selector_all(f"xpath={selector}")
                            if elements:
                                element = elements[0]
                                if element.is_visible():
                                    element.click()
                                    self.logger.info(f"✅ 成功点击每页显示数量选择器: 方法{i+1}")
                                    time.sleep(2)
                                    page_size_clicked = True
                                    break
                        except Exception as e:
                            self.logger.warning(f"点击每页显示数量选择器失败: 方法{i+1} - {e}")

                    if page_size_clicked:
                        # 3.2: 选择500条/页
                        self.logger.info("📄 选择500条/页")
                        option_selectors = [
                            "//li[contains(text(), '500条/页')]",
                            "//div[contains(@class, 'jx-select-dropdown__item')][contains(text(), '500')]",
                            "//span[contains(text(), '500条/页')]"
                        ]

                        for i, selector in enumerate(option_selectors):
                            try:
                                elements = product_page.query_selector_all(f"xpath={selector}")
                                if elements:
                                    element = elements[0]
                                    if element.is_visible():
                                        element.click()
                                        self.logger.info(f"✅ 成功选择500条/页: 方法{i+1}")
                                        time.sleep(8)  # 等待页面加载
                                        break
                            except Exception as e:
                                self.logger.warning(f"选择500条/页失败: 方法{i+1} - {e}")

                    # 3.3: 点击全选当前页
                    self.logger.info("☑️ 点击全选当前页")
                    select_current_page_selectors = [
                        "//button[contains(., '全选当前页')]",
                        "//span[contains(text(), '全选当前页')]/parent::button",
                        "//button[contains(@class, 'jx-button--primary') and contains(@class, 'is-plain')][contains(., '全选当前页')]"
                    ]

                    select_clicked = False
                    for i, selector in enumerate(select_current_page_selectors):
                        try:
                            elements = product_page.query_selector_all(f"xpath={selector}")
                            if elements:
                                element = elements[0]
                                if element.is_visible():
                                    element.click()
                                    self.logger.info(f"✅ 成功点击全选当前页: 方法{i+1}")
                                    time.sleep(3)
                                    select_clicked = True
                                    break
                        except Exception as e:
                            self.logger.warning(f"点击全选当前页失败: 方法{i+1} - {e}")

                else:
                    self.logger.info(f"📉 产品数量 {total_count} <= 1000，执行一键全选策略")
                    time.sleep(2)  # 策略决策后等待

                    # 3.4: 点击一键全选搜索结果产品
                    self.logger.info("☑️ 点击一键全选搜索结果产品")
                    select_all_selectors = [
                        "//button[contains(., '一键全选搜索结果产品')]",
                        "//span[contains(text(), '一键全选搜索结果产品')]/parent::button",
                        "//button[contains(@class, 'jx-button--primary') and contains(@class, 'is-plain')][contains(., '一键全选搜索结果产品')]"
                    ]

                    select_clicked = False
                    for i, selector in enumerate(select_all_selectors):
                        try:
                            elements = product_page.query_selector_all(f"xpath={selector}")
                            if elements:
                                element = elements[0]
                                if element.is_visible():
                                    element.click()
                                    self.logger.info(f"✅ 成功点击一键全选搜索结果产品: 方法{i+1}")
                                    time.sleep(4)  # 等待选择完成
                                    select_clicked = True
                                    break
                        except Exception as e:
                            self.logger.warning(f"点击一键全选搜索结果产品失败: 方法{i+1} - {e}")

                # 步骤4：点击确定按钮
                time.sleep(3)  # 等待选择操作完成
                self.logger.info("✅ 点击确定按钮")
                confirm_selectors = [
                    "//button[contains(., '确定')]",
                    "//span[contains(text(), '确定')]/parent::button",
                    "//button[contains(@class, 'jx-button--primary') and contains(@class, 'jx-button--default')][contains(., '确定')]"
                ]

                confirm_clicked = False
                for i, selector in enumerate(confirm_selectors):
                    try:
                        elements = product_page.query_selector_all(f"xpath={selector}")
                        if elements:
                            element = elements[0]
                            if element.is_visible():
                                element.click()
                                self.logger.info(f"✅ 成功点击确定按钮: 方法{i+1}")
                                time.sleep(5)  # 等待操作完成
                                confirm_clicked = True
                                break
                    except Exception as e:
                        self.logger.warning(f"点击确定按钮失败: 方法{i+1} - {e}")

                if confirm_clicked:
                    self.logger.info("🎉 产品添加操作完成！")
                else:
                    self.logger.warning("⚠️ 确定按钮点击失败，但操作可能已完成")

                # 记录操作结果
                data = {
                    '时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'Container': self.container_code,
                    '环境名称': self.env_name,
                    '操作类型': '添加产品到秒杀活动',
                    '操作结果': '成功',
                    '页面URL': product_page.url
                }
                write_to_excel_realtime(data, f'AddProducts_Results_{current_date}.xlsx')

                return True
            else:
                self.logger.error("❌ 未检测到新标签页")
                return False

        except Exception as e:
            self.logger.error(f"添加产品到秒杀活动失败: {e}")
            return False

# ==================== 环境管理和主程序 ====================

def read_env_from_excel(file_path: str, sheet_index: int = 0) -> Dict[str, str]:
    """从Excel文件读取环境配置"""
    try:
        wb = load_workbook(file_path)
        ws = wb.worksheets[sheet_index]
        env_dict = {}

        for row in ws.iter_rows(min_row=2, max_col=2, values_only=True):
            env_id, env_name = row
            if env_id is not None and env_name is not None:
                env_dict[str(env_id)] = str(env_name)

        return env_dict
    except Exception as e:
        print(f"读取环境配置文件失败: {e}")
        return {}

def run_miaoshou_automation(container_code: str, env_name: str, operation_type: str = "create_activity", **kwargs):
    """运行妙手自动化操作"""
    global open_counter
    current_date = datetime.now().strftime('%Y-%m-%d')

    # 创建日志目录
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # 设置日志 - 同时输出到文件和控制台
    log_filename = os.path.join('logs', f"miaoshou_{container_code}_{current_date}.log")

    # 创建logger
    logger = logging.getLogger(f"Miaoshou-{container_code}")
    logger.setLevel(logging.INFO)

    # 清除已有的处理器，避免重复
    logger.handlers.clear()

    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s: %(message)s')

    # 文件处理器
    file_handler = FileHandler(log_filename, encoding='utf-8')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    automation = None

    try:
        logger.info(f"正在启动指纹浏览器环境: {env_name} ({container_code})")

        # 启动指纹浏览器
        url = 'http://127.0.0.1:6873/api/v1/browser/start'
        open_data = {"containerCode": container_code, "skipSystemResourceCheck": True}

        logger.info(f"发送启动请求到: {url}")
        logger.info(f"请求数据: {open_data}")

        # 先测试连接
        try:
            requests.get('http://127.0.0.1:6873', timeout=5)
            logger.info("指纹浏览器服务连接测试成功")
        except Exception as conn_e:
            logger.error(f"无法连接到指纹浏览器服务: {conn_e}")
            logger.error("请确保指纹浏览器管理程序已启动并监听6873端口")
            return False

        open_res = requests.post(url, json=open_data, timeout=30).json()
        logger.info(f"启动响应: {open_res}")

        if open_res['code'] != 0:
            logger.error(f'环境{container_code}打开失败: {open_res}')
            return False

        debugging_port = open_res['data']['debuggingPort']
        logger.info(f"指纹浏览器启动成功，调试端口: {debugging_port}")

        logger.info("正在初始化自动化实例...")
        automation = MiaoshouAutomation(
            container_code=container_code,
            debugging_port=debugging_port,
            logger=logger,
            env_name=env_name
        )

        logger.info(f"Container code: {container_code} started.")
        logger.info(f"开始执行操作类型: {operation_type}")

        # 根据操作类型执行不同的功能
        if operation_type == "create_activity":
            activity_name = kwargs.get('activity_name', f'限时秒杀活动_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
            duration_hours = kwargs.get('duration_hours', 24)
            logger.info(f"活动名称: {activity_name}")
            logger.info(f"活动持续时间: {duration_hours}小时")
            return automation.create_flash_sale_activity(activity_name, duration_hours)

        elif operation_type == "add_products":
            logger.info("开始为未开始的秒杀活动添加产品")
            return automation.add_products_to_flash_sale()

        else:
            logger.error(f"未知的操作类型: {operation_type}")
            return False

    except Exception as e:
        logger.error(f"运行妙手自动化操作时发生错误: {e}")
        return False

    finally:
        if automation:
            try:
                print("🔄 准备关闭浏览器...")
                # 暂时不关闭浏览器，便于调试
                # current_page = automation.browser_context.pages[-1]
                # automation.close_other_pages(current_page)
            except Exception as e:
                print(f"关闭浏览器时出错: {e}")
        gc.collect()
        open_counter += 1

def run_automation_with_semaphore(container_code: str, env_name: str, operation_type: str = "create_activity", **kwargs):
    """使用信号量控制的自动化运行"""
    with semaphore:
        return run_miaoshou_automation(container_code, env_name, operation_type, **kwargs)

def create_sample_environment_file():
    """创建示例环境配置文件"""
    if not os.path.exists(environment_path):
        print(f"📄 创建示例环境配置文件: {environment_path}")

        # 创建示例数据
        sample_data = {
            '环境ID': ['1254658148', 'env002', 'env003'],
            '环境名称': ['测试环境1', '测试环境2', '生产环境1']
        }

        df = pd.DataFrame(sample_data)
        df.to_excel(environment_path, index=False)
        print("✅ 示例环境配置文件创建完成")
        print("请编辑此文件，添加您的实际环境信息")
    else:
        print(f"✅ 环境配置文件已存在: {environment_path}")

def run_single_cycle() -> int:
    """执行单次创建活动循环"""
    print(f"🚀 开始执行创建活动操作")
    print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 读取环境配置
    env_dict = read_env_from_excel(environment_path)
    if not env_dict:
        print(f"❌ 无法读取环境配置文件: {environment_path}")
        print("请确保文件存在且格式正确（第一列：环境ID，第二列：环境名称）")
        return 0

    print(f"📋 读取到 {len(env_dict)} 个环境配置")
    for env_id, env_name in env_dict.items():
        print(f"  - {env_id}: {env_name}")

    # 开始创建限时秒杀活动
    print("\n🚀 开始创建限时秒杀活动...")
    success_count = 0
    for container_code, env_name in env_dict.items():
        activity_name = f"{env_name}_限时秒杀_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        print(f"\n🎯 处理环境: {env_name} ({container_code})")
        result = run_automation_with_semaphore(
            container_code=container_code,
            env_name=env_name,
            operation_type="create_activity",
            activity_name=activity_name,
            duration_hours=24)

        if result:
            success_count += 1
            print(f"✅ {env_name} 创建活动成功")
        else:
            print(f"❌ {env_name} 创建活动失败")

    print(f"\n📊 本次执行完成: {success_count}/{len(env_dict)} 个环境处理成功")
    print("🔍 请检查浏览器页面和日志文件获取详细信息")

    return success_count

def main():
    """主程序入口"""
    print("=" * 60)
    print("🎯 妙手ERP限时秒杀自动化程序")
    print("=" * 60)

    # 创建必要的目录
    for dir_name in ['logs', 'reports']:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"📁 创建目录: {dir_name}")

    # 创建示例环境配置文件
    create_sample_environment_file()

    # 等待启动时间
    if wait_time > 0:
        print(f"⏳ 等待 {wait_time} 秒后开始执行...")
        time.sleep(wait_time)

    # 直接启动循环模式
    loop_mode = False
    if len(sys.argv) > 1 and sys.argv[1] == "loop":
        loop_mode = True
        print("🔄 启动循环模式：每小时执行一次创建活动操作")
        print("⏰ 按 Ctrl+C 可以停止循环")
    else:
        print("� 单次执行模式")
        print("💡 提示：使用 'python script.py loop' 启动循环模式")

    print("=" * 60)

    # 循环执行
    cycle_count = 0
    total_success = 0

    try:
        while True:
                cycle_count += 1
                print(f"\n🔄 第 {cycle_count} 次循环开始")
                print("=" * 40)

                # 执行单次操作
                success_count = run_single_cycle()
                total_success += success_count

                print("=" * 40)
                print(f"🔄 第 {cycle_count} 次循环完成")
                print(f"📊 累计统计: {total_success} 次成功，共 {cycle_count} 次循环")

                # 等待指定时间
                next_run_time = datetime.now() + timedelta(seconds=loop_interval_seconds)
                print(f"⏰ 下次运行时间: {next_run_time.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"💤 等待 {loop_interval_seconds//60} 分钟后继续运行...\n")
                time.sleep(loop_interval_seconds)

    except KeyboardInterrupt:
        print(f"\n\n🛑 用户中断循环")
        print(f"📊 最终统计: 共执行 {cycle_count} 次循环，累计成功 {total_success} 次")
        print("👋 程序已退出")

    except Exception as e:
        print(f"\n❌ 循环执行出现异常: {e}")
        print(f"📊 异常前统计: 共执行 {cycle_count} 次循环，累计成功 {total_success} 次")

    else:
        # 单次执行模式
        success_count = run_single_cycle()
        print(f"\n📊 程序执行完成")
        print("� 提示：使用 'python script.py loop' 启动循环模式")

if __name__ == '__main__':
    main()
