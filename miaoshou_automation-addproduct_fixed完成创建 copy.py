#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
妙手ERP限时秒杀自动化程序 - 真实点击分页版本
专门实现真实点击操作来改变分页大小

主要功能：
1. 真实模拟用户点击分页选择器
2. 从"20条/页"改为"500条/页"
3. 完善的错误处理和调试信息
4. 支持多种选择器策略确保成功率

作者：基于张丽原程序改编
日期：2025-07-10
版本：真实点击分页版本
"""

import sys
import time
import requests
from playwright import sync_api
import logging
import threading
from threading import Thread
from datetime import datetime
from logging import FileHandler
import pandas as pd
import os
from openpyxl import load_workbook
from typing import Dict, List, Optional
import gc
from concurrent.futures import ThreadPoolExecutor
from playwright.sync_api import sync_playwright
import json
import random

# ==================== 全局配置变量 ====================

# 调试控制
DEBUG_MODE = False  # 设置为False可关闭所有调试信息保存，提高运行速度

# 线程控制
max_threads = 1  # 修改为1个线程便于调试
semaphore = threading.Semaphore(max_threads)
chunk_size = 100

# 时间控制
wait_time = 0  # 程序启动前等待时间（秒）
operation_wait_time = 2  # 操作间隔时间（秒）
page_load_timeout = 30000  # 页面加载超时时间（毫秒）

# 重试配置
try_open_times = 5  # 尝试打开网页的次数
max_open_times = 1000  # 最大打开浏览器次数

# 文件路径配置
environment_path = "miaoshou_environment.xlsx"  # 环境配置文件
config_path = "miaoshou_config.json"  # 配置文件

# 妙手ERP相关URL
MIAOSHOU_BASE_URL = "https://erp.91miaoshou.com"
FLASH_SALE_URL = f"{MIAOSHOU_BASE_URL}/tiktok/marketing/flashSale"

# 浏览器计数器
open_counter = 0

# 当前日期
current_date = datetime.now().strftime('%Y-%m-%d %H-%M-%S')

# ==================== 工具函数 ====================

# 创建全局锁对象用于Excel写入
excel_lock = threading.Lock()

def write_to_excel_realtime(data: Dict, file_name: str):
    """实时写入Excel文件"""
    with excel_lock:
        reports_dir = 'reports'
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        file_path = os.path.join(reports_dir, file_name)

        if os.path.exists(file_path):
            df = pd.read_excel(file_path)
        else:
            df = pd.DataFrame()

        new_row = pd.DataFrame([data])
        df = pd.concat([df, new_row], ignore_index=True)
        df.to_excel(file_path, index=False, engine='openpyxl')

def load_config(config_file: str = config_path) -> Dict:
    """加载配置文件"""
    default_config = {
        "flash_sale": {
            "discount_percentage": 20,  # 默认折扣百分比
            "limit_quantity": 100,      # 默认限购数量
            "activity_duration": 24     # 活动持续时间（小时）
        },
        "selectors": {
            "create_activity_btn": "//button[contains(text(), '创建活动')]",
            "activity_name_input": "//input[@placeholder='请输入活动名称']",
            "add_product_btn": "//button[contains(text(), '添加产品')]",
            "discount_input": "//input[@placeholder='请输入折扣']",
            "quantity_input": "//input[@placeholder='请输入限购数量')]"
        }
    }
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
        except Exception as e:
            print(f"加载配置文件失败，使用默认配置: {e}")
    
    # 创建默认配置文件
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(default_config, f, ensure_ascii=False, indent=2)
    
    return default_config

# ==================== 核心自动化类 ====================

class MiaoshouAutomation:
    """妙手ERP自动化操作类"""
    
    def __init__(self, container_code: str, debugging_port: int, logger: logging.Logger, env_name: str):
        """
        初始化妙手自动化实例
        
        Args:
            container_code: 容器代码
            debugging_port: 调试端口
            logger: 日志记录器
            env_name: 环境名称
        """
        self.container_code = container_code
        self.debugging_port = debugging_port
        self.logger = logger
        self.env_name = env_name
        self.config = load_config()
        
        # 初始化Playwright
        self.playwright = sync_api.sync_playwright().start()
        self.browser, self.browser_context = self.get_browser_context(
            self.playwright, self.debugging_port)
    
    @staticmethod
    def get_browser_context(playwright, port: int):
        """获取浏览器上下文"""
        browser = playwright.chromium.connect_over_cdp(f"http://127.0.0.1:{port}")
        context = browser.contexts[0]
        return browser, context
    
    def close_browser(self):
        """关闭浏览器"""
        url = 'http://127.0.0.1:6873/api/v1/browser/stop'
        data = {"containerCode": self.container_code}
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 200:
                res_json = response.json()
                if res_json.get('code') == 0:
                    self.logger.info(f"成功关闭了容器代码为 {self.container_code} 的浏览器")
                else:
                    self.logger.error(f"关闭容器代码为 {self.container_code} 的浏览器失败。错误：{res_json.get('msg')}")
            else:
                self.logger.error(f"关闭容器代码为 {self.container_code} 的浏览器失败。HTTP状态码：{response.status_code}")
                self.close_browser_with_playwright()
        except Exception as e:
            self.logger.error(f"关闭浏览器时发生异常：{e}")
            self.close_browser_with_playwright()
    
    def close_browser_with_playwright(self):
        """使用Playwright关闭浏览器"""
        try:
            self.browser.close()
            self.logger.info(f"使用Playwright成功关闭了容器代码为 {self.container_code} 的浏览器")
        except Exception as e:
            self.logger.error(f"使用Playwright关闭容器代码为 {self.container_code} 的浏览器失败。异常：{e}")
    
    def close_other_pages(self, current_page):
        """关闭除当前页面外的所有页面"""
        for page in self.browser_context.pages:
            if page != current_page:
                page.close()
        time.sleep(30)
        self.close_browser()
    
    def wait_and_click(self, page, xpath: str, description: str = "", timeout: int = 10000, retries: int = 3) -> bool:
        """等待元素并点击"""
        for attempt in range(retries):
            try:
                page.wait_for_selector(f"xpath={xpath}", timeout=timeout)
                
                js_script = f'''
                (function() {{
                    const xpath = "{xpath}";
                    const element = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                    if (element) {{
                        const clickEvent = new MouseEvent("click", {{
                            bubbles: true,
                            cancelable: true,
                            view: window
                        }});
                        element.dispatchEvent(clickEvent);
                        return true;
                    }}
                    return false;
                }})()
                '''
                
                result = page.evaluate(js_script)
                if result:
                    self.logger.info(f"成功点击元素: {description or xpath}")
                    return True
                else:
                    self.logger.warning(f"元素存在但点击失败: {description or xpath}")
                    
            except Exception as e:
                self.logger.error(f"点击元素时发生异常 (尝试 {attempt + 1}/{retries}): {e}")
                
        self.logger.error(f"点击元素失败，超过重试次数: {description or xpath}")
        return False
    
    def input_text(self, page, xpath: str, text: str, description: str = "", clear_first: bool = True) -> bool:
        """输入文本到指定元素"""
        try:
            page.wait_for_selector(f"xpath={xpath}", timeout=10000)
            element = page.locator(f"xpath={xpath}")
            
            if clear_first:
                element.fill("")
            
            element.fill(text)
            self.logger.info(f"成功输入文本到 {description or xpath}: {text}")
            return True
            
        except Exception as e:
            self.logger.error(f"输入文本失败 {description or xpath}: {e}")
            return False

    def save_page_debug_info(self, page, step_name: str):
        """保存页面调试信息：HTML源码和截图"""
        # 检查调试模式开关
        if not DEBUG_MODE:
            return True  # 调试模式关闭时直接返回成功

        try:
            import os
            from datetime import datetime

            # 创建调试目录
            debug_dir = "debug_pages"
            if not os.path.exists(debug_dir):
                os.makedirs(debug_dir)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存HTML源码
            html_content = page.content()
            html_file = os.path.join(debug_dir, f"{step_name}_{timestamp}.html")
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            self.logger.info(f"📄 已保存HTML源码: {html_file}")

            # 保存截图
            screenshot_file = os.path.join(debug_dir, f"{step_name}_{timestamp}.png")
            page.screenshot(path=screenshot_file, full_page=True)
            self.logger.info(f"📸 已保存页面截图: {screenshot_file}")

            return True

        except Exception as e:
            self.logger.warning(f"保存页面调试信息失败: {e}")
            return False

    def add_products_to_flash_sale(self, target_activity_index: int = 0) -> bool:
        """
        为未开始的秒杀活动添加产品 - 完整流程

        Args:
            target_activity_index: 目标活动的索引（从0开始），用于轮询处理

        Returns:
            bool: 添加是否成功
        """
        try:
            self.logger.info("🚀 开始为未开始的秒杀活动添加产品")

            # 第0步：关闭多余的标签页
            self.logger.info("🎯 第0步：关闭多余的标签页")
            all_pages = self.browser_context.pages
            self.logger.info(f"当前打开的标签页数量: {len(all_pages)}")

            # 关闭除第一个之外的所有标签页
            for i, page in enumerate(all_pages):
                if i > 0:  # 保留第一个标签页
                    try:
                        self.logger.info(f"关闭标签页 {i+1}: {page.url}")
                        page.close()
                    except Exception as e:
                        self.logger.warning(f"关闭标签页 {i+1} 失败: {e}")

            # 使用第一个标签页
            main_page = all_pages[0]
            self.logger.info("✅ 已关闭多余标签页，使用第一个标签页")

            # 第一步：导航到秒杀活动页面
            self.logger.info("🎯 第一步：导航到秒杀活动页面")
            main_page.goto(FLASH_SALE_URL, timeout=page_load_timeout)
            main_page.wait_for_load_state("domcontentloaded", timeout=page_load_timeout)
            self.logger.info("✅ 页面DOM加载完成")

            # 等待页面完全加载
            time.sleep(5)
            self.logger.info("✅ 成功导航到秒杀活动页面")

            # 第二步：点击未开始标签
            self.logger.info("🎯 第二步：点击未开始标签")

            # 等待页面稳定
            wait_seconds = 2 + (time.time() % 2)  # 2-4秒随机等待
            self.logger.info(f"等待页面稳定 {wait_seconds:.1f} 秒...")
            time.sleep(wait_seconds)

            # 点击未开始标签
            not_started_selectors = [
                "//label[contains(@class, 'pro-radio-button')][contains(., '未开始')]",
                "//label[contains(@class, 'jx-radio-button')][contains(., '未开始')]",
                "//span[contains(text(), '未开始')]/parent::label",
                "//label[contains(., '未开始')]"
            ]

            not_started_clicked = False
            for i, selector in enumerate(not_started_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击未开始标签: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    elements = main_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个未开始标签")

                    if elements:
                        element = elements[0]
                        is_visible = element.is_visible()
                        self.logger.info(f"     元素可见性: {is_visible}")

                        if is_visible:
                            element.scroll_into_view_if_needed()
                            time.sleep(0.5)
                            element.click()
                            self.logger.info(f"  ✅ 成功点击未开始标签: 方法{i+1}")
                            time.sleep(2)
                            not_started_clicked = True
                            break
                        else:
                            self.logger.warning(f"     未开始标签不可见: 方法{i+1}")
                    else:
                        self.logger.warning(f"     未找到未开始标签: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击未开始标签失败: 方法{i+1} - {e}")
                    continue

            if not not_started_clicked:
                self.logger.error("❌ 第二步失败：无法点击未开始标签")
                return False

            self.logger.info("✅ 成功点击未开始标签")

            # 第三步：点击第一个活动的管理产品
            self.logger.info("🎯 第三步：点击第一个活动的管理产品")

            # 等待活动列表加载
            wait_seconds = 3 + (time.time() % 1)  # 3-4秒随机等待
            self.logger.info(f"等待活动列表加载 {wait_seconds:.1f} 秒...")
            time.sleep(wait_seconds)

            # 保存点击未开始后的页面状态
            self.logger.info("🔍 保存点击未开始后的页面状态...")
            self.save_page_debug_info(main_page, "after_click_not_started")

            # 调试：查看页面上的所有按钮和链接
            self.logger.info("🔍 调试：查看页面上的所有按钮和链接...")
            try:
                js_script = '''
                (function() {
                    const elements = [];
                    // 获取所有button和span元素
                    document.querySelectorAll('button, span, a').forEach((el, index) => {
                        const text = el.innerText.trim();
                        if (text && (text.includes('管理产品') || text.includes('编辑') || text.includes('产品'))) {
                            elements.push({
                                tagName: el.tagName,
                                index: index,
                                text: text,
                                className: el.className,
                                visible: el.offsetParent !== null
                            });
                        }
                    });
                    return elements;
                })()
                '''

                buttons = main_page.evaluate(js_script)
                self.logger.info(f"找到 {len(buttons)} 个可能的操作按钮:")

                for btn in buttons[:50]:  # 显示前50个
                    self.logger.info(f"  {btn['tagName']} {btn['index']}: '{btn['text']}' (可见: {btn['visible']})")

            except Exception as e:
                self.logger.warning(f"调试按钮失败: {e}")

            # 根据target_activity_index点击对应的管理产品按钮
            self.logger.info(f"🎯 准备点击第 {target_activity_index + 1} 个活动的管理产品按钮")

            # 首先获取所有管理产品按钮
            all_manage_buttons = []
            manage_product_selectors = [
                "//button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button')][contains(., '管理产品')]",
                "//button[contains(., '管理产品')]",
                "//span[contains(text(), '管理产品')]/parent::button"
            ]

            for selector in manage_product_selectors:
                try:
                    elements = main_page.query_selector_all(f"xpath={selector}")
                    for element in elements:
                        if element.is_visible():
                            all_manage_buttons.append(element)
                    if all_manage_buttons:
                        break  # 找到可见按钮就停止
                except Exception as e:
                    continue

            self.logger.info(f"📊 找到 {len(all_manage_buttons)} 个可见的管理产品按钮")

            if not all_manage_buttons:
                self.logger.error("❌ 未找到任何可见的管理产品按钮")
                return False

            # 计算实际要点击的按钮索引（循环轮询）
            actual_index = target_activity_index % len(all_manage_buttons)
            self.logger.info(f"🎯 将点击第 {actual_index + 1} 个管理产品按钮 (目标索引: {target_activity_index}, 总按钮数: {len(all_manage_buttons)})")

            # 点击指定索引的管理产品按钮
            manage_clicked = False
            try:
                target_button = all_manage_buttons[actual_index]
                target_button.scroll_into_view_if_needed()
                time.sleep(0.5)
                target_button.click()
                self.logger.info(f"✅ 成功点击第 {actual_index + 1} 个管理产品按钮")
                time.sleep(3)
                manage_clicked = True
            except Exception as e:
                self.logger.warning(f"❌ 点击第 {actual_index + 1} 个管理产品按钮失败: {e}")

                # 如果指定索引失败，尝试点击第一个作为备选
                try:
                    first_button = all_manage_buttons[0]
                    first_button.scroll_into_view_if_needed()
                    time.sleep(0.5)
                    first_button.click()
                    self.logger.info("✅ 备选方案：成功点击第1个管理产品按钮")
                    time.sleep(3)
                    manage_clicked = True
                except Exception as e2:
                    self.logger.error(f"❌ 备选方案也失败: {e2}")

            if not manage_clicked:
                self.logger.error("❌ 第三步失败：无法点击管理产品")
                return False

            self.logger.info("✅ 成功点击管理产品按钮")

            # 第四步：处理新标签页并添加产品
            self.logger.info("🎯 第四步：处理新标签页并添加产品")

            # 等待新标签页打开
            self.logger.info("等待新标签页打开...")
            product_page = None

            # 等待新标签页出现，最多等待15秒
            for attempt in range(15):
                time.sleep(1)
                all_pages = self.browser_context.pages
                self.logger.info(f"等待尝试 {attempt+1}/15: 当前标签页数量 {len(all_pages)}")

                # 查找新打开的产品管理标签页
                for i, p in enumerate(all_pages):
                    try:
                        page_url = p.url
                        page_title = p.title()
                        self.logger.info(f"  标签页 {i+1}: URL={page_url}, 标题={page_title}")

                        # 检查是否是产品管理页面
                        if ("/create" in page_url and "step=2" in page_url) or "管理活动产品" in page_title:
                            product_page = p
                            self.logger.info("✅ 检测到新标签页已打开")
                            break
                    except Exception as e:
                        self.logger.warning(f"检查标签页 {i+1} 失败: {e}")
                        continue

                if product_page:
                    break

            if not product_page:
                self.logger.error("❌ 第四步失败：未检测到新标签页")
                return False

            # 切换到产品管理页面
            self.logger.info(f"当前打开的标签页数量: {len(self.browser_context.pages)}")
            self.logger.info(f"切换到产品管理页面: {product_page.url}")

            # 等待产品管理页面加载
            self.logger.info("等待产品管理页面加载...")
            try:
                product_page.wait_for_load_state("domcontentloaded", timeout=15000)
                self.logger.info("DOM内容加载完成，继续等待页面稳定...")

                # 等待网络空闲
                try:
                    product_page.wait_for_load_state('networkidle', timeout=15000)
                    self.logger.info("网络空闲状态达成")
                except:
                    self.logger.warning("网络空闲等待超时，继续执行")

                # 再等待一段时间确保所有元素都已渲染
                time.sleep(3)
                self.logger.info("✅ 产品管理页面加载完成")

            except Exception as e:
                self.logger.warning(f"等待页面加载失败: {e}")

            # 保存产品管理页面的调试信息
            self.logger.info("🔍 保存产品管理页面调试信息...")
            self.save_page_debug_info(product_page, "product_management_page")

            result = self._handle_add_products_dialog(product_page)

            # 如果返回"continue"，表示需要继续处理下一个活动
            if result == "continue":
                self.logger.info("🔄 准备处理下一个未开始的活动...")
                return "continue"
            else:
                return result

        except Exception as e:
            self.logger.error(f"添加产品到秒杀活动失败: {e}")
            return False

        finally:
            # 保持页面打开以便检查结果
            self.logger.info("🔍 页面保持打开状态，便于检查结果...")

    def _handle_add_products_dialog(self, product_page) -> bool:
        """处理添加产品对话框的完整流程"""
        try:
            # 调试：查看产品管理页面上的所有按钮
            self.logger.info("🔍 调试：查看产品管理页面上的所有按钮...")
            try:
                js_script = '''
                (function() {
                    const buttons = document.querySelectorAll('button, a');
                    const buttonInfo = [];
                    buttons.forEach((btn, index) => {
                        const text = btn.innerText.trim();
                        if (text) {
                            buttonInfo.push({
                                tagName: btn.tagName,
                                index: index,
                                text: text,
                                className: btn.className,
                                visible: btn.offsetParent !== null
                            });
                        }
                    });
                    return buttonInfo;
                })()
                '''

                buttons = product_page.evaluate(js_script)
                self.logger.info(f"找到 {len(buttons)} 个按钮:")

                for btn in buttons[:12]:  # 显示前12个按钮
                    self.logger.info(f"  {btn['tagName']} {btn['index']}: '{btn['text']}' (可见: {btn['visible']})")

            except Exception as e:
                self.logger.warning(f"调试按钮失败: {e}")

            # 第五步：点击添加产品按钮
            self.logger.info("🎯 第五步：点击添加产品按钮")

            # 再等待一下确保按钮完全加载
            time.sleep(2)

            # 添加产品按钮的选择器
            add_product_selectors = [
                "//button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button')][contains(., '添加产品')]",
                "//span[contains(text(), '添加产品')]/parent::button",
                "//div[contains(@class, 'operate-left-box')]//button[contains(., '添加产品')]",
                "//button[contains(text(), '添加产品')]",
                "//button[@aria-disabled='false'][contains(., '添加产品')]"
            ]

            add_product_clicked = False
            for i, selector in enumerate(add_product_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击添加产品: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    elements = product_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个添加产品按钮")

                    if elements:
                        element = elements[0]
                        is_visible = element.is_visible()
                        self.logger.info(f"     元素可见性: {is_visible}")

                        if is_visible:
                            element.scroll_into_view_if_needed()
                            time.sleep(0.5)
                            element.click()
                            self.logger.info(f"  ✅ 成功点击添加产品: 方法{i+1}")
                            time.sleep(2)
                            add_product_clicked = True
                            break
                        else:
                            self.logger.warning(f"     添加产品按钮不可见: 方法{i+1}")
                    else:
                        self.logger.warning(f"     未找到添加产品按钮: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击添加产品失败: 方法{i+1} - {e}")
                    continue

            if not add_product_clicked:
                self.logger.error("❌ 第五步失败：无法点击添加产品")
                self.save_page_debug_info(product_page, "failed_click_add_product")
                return False

            self.logger.info("✅ 成功点击添加产品按钮")

            # 等待添加产品弹窗加载
            time.sleep(6)

            # 第六步：处理添加产品弹窗
            self.logger.info("🎯 第六步：处理添加产品弹窗")

            # 步骤1：勾选"隐藏已参与限时秒杀的产品"
            self.logger.info("📋 步骤1：勾选隐藏已参与限时秒杀的产品")
            time.sleep(2)

            hide_checkbox_selectors = [
                "//label[contains(@class, 'jx-checkbox')][contains(., '隐藏已参与限时秒杀的产品')]",
                "//input[@type='checkbox'][following-sibling::*[contains(text(), '隐藏已参与限时秒杀的产品')]]",
                "//span[contains(text(), '隐藏已参与限时秒杀的产品')]/parent::label",
                "//label[contains(@class, 'jx-checkbox') and contains(@class, 'pro-checkbox')][contains(., '隐藏已参与限时秒杀的产品')]"
            ]

            hide_checkbox_clicked = False
            for i, selector in enumerate(hide_checkbox_selectors):
                try:
                    self.logger.info(f"  🎯 尝试勾选隐藏复选框: 方法{i+1}")
                    elements = product_page.query_selector_all(f"xpath={selector}")

                    if elements:
                        element = elements[0]
                        if element.is_visible():
                            element.click()
                            self.logger.info(f"  ✅ 成功勾选隐藏复选框: 方法{i+1}")
                            time.sleep(3)
                            hide_checkbox_clicked = True
                            break
                except Exception as e:
                    self.logger.warning(f"  ❌ 勾选隐藏复选框失败: 方法{i+1} - {e}")

            if not hide_checkbox_clicked:
                self.logger.warning("⚠️ 未能勾选隐藏复选框，继续执行")

            # 等待复选框操作完成，页面数据更新
            time.sleep(4)

            # 步骤2：获取可添加产品总数量
            self.logger.info("📊 步骤2：获取可添加产品总数量")

            total_count = 0
            total_selectors = [
                "//div[contains(@class, 'jx-pagination__total')]//span",
                "//span[contains(text(), '条')]",
                "//div[contains(@class, 'jx-pagination__total')]",
                "//div[contains(@class, 'pagination')]//span[contains(text(), '条')]",
                "//*[contains(text(), '条') and contains(text(), '共')]"
            ]

            # 先尝试获取页面上所有可能包含数量信息的元素
            self.logger.info("🔍 调试：查找所有可能包含数量信息的元素...")
            try:
                all_text_elements = product_page.query_selector_all("xpath=//*[contains(text(), '条')]")
                self.logger.info(f"找到 {len(all_text_elements)} 个包含'条'的元素:")
                for i, elem in enumerate(all_text_elements[:10]):
                    try:
                        text = elem.inner_text().strip()
                        if text:
                            self.logger.info(f"  元素 {i+1}: '{text}'")
                    except:
                        pass
            except Exception as e:
                self.logger.warning(f"调试查找元素失败: {e}")

            # 尝试获取总数
            for i, selector in enumerate(total_selectors):
                try:
                    self.logger.info(f"🎯 尝试获取总数: 方法{i+1} - {selector}")
                    elements = product_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个匹配元素")

                    if elements:
                        for j, element in enumerate(elements):
                            try:
                                text = element.inner_text().strip()
                                self.logger.info(f"     元素 {j+1} 文本: '{text}'")

                                # 提取数字
                                import re
                                numbers = re.findall(r'\d+', text)
                                if numbers:
                                    potential_count = int(numbers[0])
                                    self.logger.info(f"     提取到数字: {potential_count}")

                                    # 如果数字看起来合理（不是页码等小数字）
                                    if potential_count > 0:
                                        total_count = potential_count
                                        self.logger.info(f"📊 检测到可添加产品总数: {total_count}")
                                        break
                            except Exception as e:
                                self.logger.warning(f"     处理元素文本失败: {e}")

                        if total_count > 0:
                            break

                except Exception as e:
                    self.logger.warning(f"获取产品总数失败: 方法{i+1} - {e}")

            if total_count == 0:
                self.logger.warning("⚠️ 未能获取产品总数，默认按小于1000处理")
                total_count = 500

            # 等待数据分析完成
            time.sleep(3)

            # 步骤3：根据产品数量选择不同的操作策略
            strategy_result = self._handle_product_selection_strategy(product_page, total_count)

            # 如果返回"continue"，传递给上层
            if strategy_result == "continue":
                return "continue"
            else:
                return strategy_result

        except Exception as e:
            self.logger.error(f"处理添加产品对话框失败: {e}")
            return False

    def _ensure_hide_checkbox_and_set_pagination(self, product_page) -> bool:
        """确保隐藏复选框勾选并通过URL参数设置分页"""
        try:
            self.logger.info("🔧 通过URL参数同时设置隐藏筛选和分页大小")

            # 获取当前URL并分析参数
            current_url = product_page.url
            self.logger.info(f"📍 当前URL: {current_url}")

            # 构建新的URL，包含所有必要参数
            js_result = product_page.evaluate("""
                () => {
                    try {
                        const currentUrl = new URL(window.location.href);

                        // 设置分页参数
                        currentUrl.searchParams.set('pageSize', '500');
                        currentUrl.searchParams.set('page', '1');

                        // 设置隐藏已参与产品的参数（尝试多种可能的参数名）
                        currentUrl.searchParams.set('hideParticipated', 'true');
                        currentUrl.searchParams.set('hideJoined', 'true');
                        currentUrl.searchParams.set('excludeParticipated', 'true');
                        currentUrl.searchParams.set('onlyAvailable', 'true');

                        const newUrl = currentUrl.toString();

                        // 直接跳转到新URL
                        window.location.href = newUrl;

                        return { success: true, newUrl: newUrl };

                    } catch (e) {
                        console.error("URL参数设置失败:", e);
                        return { success: false, message: e.message };
                    }
                }
            """)

            if js_result and js_result.get('success'):
                self.logger.info(f"✅ URL参数设置成功，跳转到: {js_result.get('newUrl')}")

                # 等待页面重新加载
                self.logger.info("⏳ 等待页面重新加载...")
                time.sleep(8)

                # 等待页面加载完成
                try:
                    product_page.wait_for_load_state('networkidle', timeout=30000)
                    self.logger.info("✅ 页面重新加载完成")
                    return True
                except Exception as e:
                    self.logger.warning(f"等待页面加载超时: {e}")
                    return True  # 继续执行，可能页面已经加载完成

            else:
                self.logger.error(f"❌ URL参数设置失败: {js_result.get('message') if js_result else '未知错误'}")
                return False

        except Exception as e:
            self.logger.error(f"URL参数设置异常: {e}")
            return False

    def _handle_product_selection_strategy(self, product_page, total_count: int) -> bool:
        """处理产品选择策略 - 使用真实点击操作改变分页大小"""
        try:
            if total_count > 1000:
                self.logger.info(f"📈 产品数量 {total_count} > 1000，执行分页策略")
                time.sleep(2)

                # 使用真实点击操作设置分页大小为500
                self.logger.info("🖱️ 使用真实点击操作设置分页大小为500条/页")

                # 调用新的真实点击分页方法
                pagination_success = self._click_pagination_selector_to_500(product_page)

                if pagination_success:
                    self.logger.info("✅ 分页设置成功，等待页面数据重新加载...")

                    # 等待数据加载完成（数据量可能比较多）- 使用随机等待时间
                    wait_time = random.randint(20, 30)  # 随机等待8-10秒
                    self.logger.info(f"⏳ 等待大量数据加载完成... ({wait_time}秒)")
                    time.sleep(wait_time)

                    # 验证分页设置是否生效
                    self._verify_pagination_change(product_page)

                else:
                    self.logger.warning("⚠️ 分页设置失败，继续使用默认分页")
                    time.sleep(2)

                self.logger.info("✅ 准备全选当前页面的产品")

            else:
                # 产品数量 <= 1000，执行简单策略
                self.logger.info(f"📊 产品数量 {total_count} <= 1000，执行简单全选策略")
                time.sleep(2)

                # 使用一键全选搜索结果产品的方式选择产品
                if not self._click_select_all_search_results_and_confirm(product_page):
                    self.logger.error("❌ 一键全选搜索结果产品操作失败")
                    return False

                self.logger.info("✅ 产品选择完成，继续后续处理流程...")

            # 通用的全选和确定流程（用于大于1000产品的情况）
            if total_count > 1000:
                select_result = self._select_all_and_confirm(product_page)

                # 如果返回"continue"，传递给上层
                if select_result == "continue":
                    return "continue"
                elif not select_result:
                    return False

            # 小于等于1000和大于1000产品都会执行到这里的后续处理流程
            # 这里应该继续执行和原来_select_all_and_confirm中相同的后续处理
            return self._handle_post_product_selection(product_page)

        except Exception as e:
            self.logger.error(f"处理产品选择策略失败: {e}")
            return False

    def _click_select_all_search_results_and_confirm(self, product_page) -> bool:
        """
        点击"一键全选搜索结果产品"按钮并确认 - 用于小于等于1000产品的情况

        Args:
            product_page: 产品页面对象

        Returns:
            bool: 操作是否成功
        """
        try:
            self.logger.info("🎯 开始执行一键全选搜索结果产品操作...")

            # 步骤1：点击"一键全选搜索结果产品"按钮
            self.logger.info("📋 步骤1：点击一键全选搜索结果产品按钮")

            select_all_selectors = [
                "//button[contains(., '一键全选搜索结果产品')]",
                "//span[contains(text(), '一键全选搜索结果产品')]/parent::button",
                "//button[contains(@class, 'jx-button')][contains(., '一键全选搜索结果产品')]"
            ]

            select_all_clicked = False
            for i, selector in enumerate(select_all_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击一键全选搜索结果产品: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    elements = product_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个一键全选按钮")

                    if elements:
                        element = elements[0]
                        is_visible = element.is_visible()
                        self.logger.info(f"     元素可见性: {is_visible}")

                        if is_visible:
                            element.scroll_into_view_if_needed()
                            time.sleep(0.5)
                            element.click()
                            self.logger.info(f"  ✅ 成功点击一键全选搜索结果产品: 方法{i+1}")
                            select_all_clicked = True
                            break
                        else:
                            self.logger.warning(f"     一键全选按钮不可见: 方法{i+1}")
                    else:
                        self.logger.warning(f"     未找到一键全选按钮: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击一键全选搜索结果产品失败: 方法{i+1} - {e}")
                    continue

            if not select_all_clicked:
                self.logger.error("❌ 无法点击一键全选搜索结果产品按钮")
                return False

            # 步骤2：等待5-10秒随机时间
            wait_time = random.randint(5, 10)
            self.logger.info(f"⏳ 等待产品选择完成... ({wait_time}秒)")
            time.sleep(wait_time)

            # 步骤3：点击确定按钮
            self.logger.info("📋 步骤3：点击确定按钮")

            confirm_selectors = [
                "//button[contains(@class, 'jx-button--primary') and contains(@class, 'jx-button--default')][contains(., '确定')]",
                "//button[contains(., '确定')]",
                "//span[contains(text(), '确定')]/parent::button"
            ]

            confirm_clicked = False
            for i, selector in enumerate(confirm_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击确定按钮: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    elements = product_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个确定按钮")

                    if elements:
                        element = elements[0]
                        is_visible = element.is_visible()
                        self.logger.info(f"     元素可见性: {is_visible}")

                        if is_visible:
                            element.scroll_into_view_if_needed()
                            time.sleep(0.5)
                            element.click()
                            self.logger.info(f"  ✅ 成功点击确定按钮: 方法{i+1}")
                            confirm_clicked = True
                            break
                        else:
                            self.logger.warning(f"     确定按钮不可见: 方法{i+1}")
                    else:
                        self.logger.warning(f"     未找到确定按钮: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击确定按钮失败: 方法{i+1} - {e}")
                    continue

            if not confirm_clicked:
                self.logger.error("❌ 无法点击确定按钮")
                return False

            # 步骤4：等待页面跳转
            self.logger.info("⏳ 等待页面跳转...")
            time.sleep(5)

            self.logger.info("✅ 一键全选搜索结果产品操作完成")
            return True  # 返回True表示产品选择成功

        except Exception as e:
            self.logger.error(f"一键全选搜索结果产品操作失败: {e}")
            return False

    def _handle_post_product_selection(self, product_page) -> bool:
        """
        处理产品选择后的所有后续流程
        包括：确保对话框关闭、管理活动产品页面分页设置、全选、批量价格设置等

        Args:
            product_page: 产品页面对象

        Returns:
            bool: 处理是否成功，或返回"continue"表示需要处理下一个活动
        """
        try:
            self.logger.info("🎯 开始处理产品选择后的后续流程...")

            # 等待操作完成 - 使用随机等待时间
            wait_time = random.randint(3, 5)  # 随机等待3-5秒
            self.logger.info(f"⏳ 等待操作完成... ({wait_time}秒)")
            time.sleep(wait_time)

            # 确保添加产品对话框已关闭
            self.logger.info("🔍 确保添加产品对话框已关闭...")
            self._ensure_dialog_closed(product_page)

            # 处理管理活动产品页面的分页设置
            self.logger.info("🎯 处理管理活动产品页面的分页设置...")
            pagination_result = self._handle_activity_product_pagination(product_page)

            # 如果返回"continue"，传递给上层
            if pagination_result == "continue":
                return "continue"

            self.logger.info("✅ 产品选择后续处理完成")
            return True

        except Exception as e:
            self.logger.error(f"产品选择后续处理失败: {e}")
            return False

    def _click_pagination_selector_to_500(self, product_page) -> bool:
        """真实点击分页选择器，将分页大小改为500条/页"""
        try:
            self.logger.info("🎯 开始真实点击分页选择器操作")

            # 第0步：先定位到正确的表格容器，避免点击到被覆盖的分页器
            self.logger.info("📋 第0步：定位正确的表格容器")

            # 根据您提供的HTML结构，先找到包含分页器的表格容器
            table_container_selectors = [
                "//div[contains(@class, 'pro-table') and contains(@class, 'pro-table--simple')]",
                "//div[@data-v-31cf2696 and contains(@class, 'pro-table')]",
                "//div[contains(@class, 'pro-table')]//div[contains(@class, 'pro-pagination')]/..",
                "//div[contains(@class, 'jx-table--fit')]/.."
            ]

            table_container = None
            for i, selector in enumerate(table_container_selectors):
                try:
                    self.logger.info(f"  🎯 尝试定位表格容器: 方法{i+1}")
                    elements = product_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个表格容器")

                    if elements:
                        # 选择最后一个（通常是最新的/最上层的）
                        table_container = elements[-1]
                        is_visible = table_container.is_visible()
                        self.logger.info(f"     表格容器可见性: {is_visible}")

                        if is_visible:
                            self.logger.info(f"  ✅ 成功定位表格容器: 方法{i+1}")
                            break
                        else:
                            table_container = None

                except Exception as e:
                    self.logger.warning(f"  ❌ 定位表格容器失败: 方法{i+1} - {e}")
                    continue

            if not table_container:
                self.logger.error("❌ 第0步失败：无法定位表格容器")
                return False

            # 第一步：在表格容器范围内找到并点击分页大小选择器
            self.logger.info("📋 第一步：在表格容器内点击分页大小选择器")

            # 在表格容器范围内构建选择器策略
            pagination_selectors = [
                # 方法1：在容器内查找分页大小选择器
                ".//span[contains(@class, 'jx-pagination__sizes')]//div[contains(@class, 'jx-select__wrapper')]",
                # 方法2：直接定位到包含"20条/页"的span
                ".//span[contains(@class, 'jx-pagination__sizes')]//span[contains(text(), '条/页')]",
                # 方法3：通过placeholder定位
                ".//div[contains(@class, 'jx-select__placeholder')]//span[contains(text(), '条/页')]",
                # 方法4：更通用的分页选择器
                ".//div[contains(@class, 'jx-select') and contains(@class, 'jx-select--small')]//div[contains(@class, 'jx-select__wrapper')]",
                # 方法5：通过分页组件定位
                ".//div[contains(@class, 'pro-pagination')]//div[contains(@class, 'jx-select__wrapper')]"
            ]

            selector_clicked = False
            for i, selector in enumerate(pagination_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击分页选择器: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    # 在表格容器范围内查找元素
                    elements = table_container.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     在表格容器内找到 {len(elements)} 个分页选择器")

                    if elements:
                        element = elements[0]
                        is_visible = element.is_visible()
                        self.logger.info(f"     元素可见性: {is_visible}")

                        if is_visible:
                            # 滚动到元素位置
                            element.scroll_into_view_if_needed()
                            time.sleep(0.5)

                            # 点击元素
                            element.click()
                            self.logger.info(f"  ✅ 成功点击分页选择器: 方法{i+1}")
                            time.sleep(2)  # 等待下拉菜单展开

                            # 立即保存下拉菜单展开后的页面状态
                            self.logger.info("🔍 保存下拉菜单展开后的页面状态...")
                            self.save_page_debug_info(product_page, "pagination_dropdown_opened")

                            selector_clicked = True
                            break
                        else:
                            self.logger.warning(f"     分页选择器不可见: 方法{i+1}")
                    else:
                        self.logger.warning(f"     在表格容器内未找到分页选择器: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击分页选择器失败: 方法{i+1} - {e}")
                    continue

            if not selector_clicked:
                self.logger.error("❌ 第一步失败：无法点击分页选择器")
                return False

            # 第二步：等待下拉菜单展开并点击"500条/页"选项
            self.logger.info("📋 第二步：点击500条/页选项")

            # 等待下拉菜单完全展开
            time.sleep(3)

            # 查找500条/页选项的选择器（根据调试信息优化）
            option_500_selectors = [
                # 方法1：直接查找可见的span元素包含"500条/页"
                "//span[contains(text(), '500条/页')]",
                # 方法2：查找li元素包含span子元素的500条/页
                "//li[contains(@class, 'jx-select-dropdown__item')]//span[contains(text(), '500条/页')]",
                # 方法3：通过ID定位（从调试信息看到的具体ID）
                "//li[@id and contains(@class, 'jx-select-dropdown__item')]//span[contains(text(), '500条/页')]",
                # 方法4：查找ul列表中的500条/页选项
                "//ul[contains(@class, 'jx-select-dropdown__list')]//li//span[contains(text(), '500条/页')]",
                # 方法5：通过下拉菜单容器查找
                "//div[contains(@class, 'jx-select-dropdown')]//span[contains(text(), '500条/页')]",
                # 方法6：备用方案 - 直接查找包含500的可见元素
                "//*[contains(text(), '500条/页')]",
                # 方法7：查找li元素本身包含500条/页文本
                "//li[contains(@class, 'jx-select-dropdown__item')][contains(text(), '500条/页')]"
            ]

            option_clicked = False
            for i, selector in enumerate(option_500_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击500条/页选项: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    # 下拉菜单选项通常是全局的，所以用product_page查找
                    elements = product_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个500条/页选项")

                    if elements:
                        # 选择第一个可见的选项
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                element_text = element.inner_text().strip()
                                self.logger.info(f"     选项 {j+1}: 文本='{element_text}', 可见={is_visible}, 启用={is_enabled}")

                                if is_visible and is_enabled and '500条/页' in element_text:
                                    # 滚动到元素位置
                                    element.scroll_into_view_if_needed()
                                    time.sleep(0.5)

                                    # 尝试多种点击方式
                                    try:
                                        # 方式1：普通点击
                                        element.click()
                                        self.logger.info(f"  ✅ 成功点击500条/页选项: 方法{i+1}, 选项{j+1} (普通点击)")
                                    except Exception as e1:
                                        try:
                                            # 方式2：强制点击
                                            element.click(force=True)
                                            self.logger.info(f"  ✅ 成功点击500条/页选项: 方法{i+1}, 选项{j+1} (强制点击)")
                                        except Exception as e2:
                                            try:
                                                # 方式3：JavaScript点击
                                                element.evaluate("el => el.click()")
                                                self.logger.info(f"  ✅ 成功点击500条/页选项: 方法{i+1}, 选项{j+1} (JS点击)")
                                            except Exception as e3:
                                                self.logger.warning(f"     所有点击方式都失败: 普通={e1}, 强制={e2}, JS={e3}")
                                                continue

                                    time.sleep(3)  # 等待页面重新加载
                                    option_clicked = True
                                    break

                            except Exception as e:
                                self.logger.warning(f"     检查选项 {j+1} 失败: {e}")
                                continue

                        if option_clicked:
                            break
                    else:
                        self.logger.warning(f"     未找到500条/页选项: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击500条/页选项失败: 方法{i+1} - {e}")
                    continue

            if not option_clicked:
                self.logger.error("❌ 第二步失败：无法点击500条/页选项")

                # 保存失败时的页面状态用于调试
                self.logger.info("🔍 保存点击500条/页失败时的页面状态...")
                self.save_page_debug_info(product_page, "pagination_500_click_failed")

                return False

            self.logger.info("✅ 分页选择器点击操作完成")
            return True

        except Exception as e:
            self.logger.error(f"真实点击分页选择器失败: {e}")
            return False

    def save_page_debug_info(self, page, debug_name: str):
        """保存页面调试信息（截图和HTML）"""
        # 检查调试模式开关
        if not DEBUG_MODE:
            return True  # 调试模式关闭时直接返回成功

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 创建调试目录
            debug_dir = "debug_pagination"
            if not os.path.exists(debug_dir):
                os.makedirs(debug_dir)

            # 保存截图
            screenshot_path = os.path.join(debug_dir, f"{debug_name}_{timestamp}.png")
            page.screenshot(path=screenshot_path, full_page=True)
            self.logger.info(f"📸 截图已保存: {screenshot_path}")

            # 保存HTML
            html_path = os.path.join(debug_dir, f"{debug_name}_{timestamp}.html")
            html_content = page.content()
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            self.logger.info(f"📄 HTML已保存: {html_path}")

            # 保存所有可能的下拉选项信息
            options_info_path = os.path.join(debug_dir, f"{debug_name}_options_{timestamp}.txt")
            with open(options_info_path, 'w', encoding='utf-8') as f:
                f.write(f"=== 下拉菜单选项调试信息 ===\n")
                f.write(f"时间: {timestamp}\n")
                f.write(f"调试名称: {debug_name}\n\n")

                # 查找所有可能的下拉选项
                debug_selectors = [
                    "//li[contains(@class, 'jx-select-dropdown__item')]",
                    "//div[contains(@class, 'jx-select-dropdown__item')]",
                    "//li[contains(@class, 'dropdown')]",
                    "//div[contains(@class, 'dropdown')]",
                    "//li[contains(@class, 'option')]",
                    "//div[contains(@class, 'option')]",
                    "//*[contains(text(), '条/页')]",
                    "//*[contains(text(), '500')]",
                    "//ul[contains(@class, 'jx-select-dropdown__list')]//li",
                    "//div[contains(@class, 'jx-select-dropdown')]//li",
                    "//div[contains(@class, 'jx-select-dropdown')]//div"
                ]

                for selector in debug_selectors:
                    try:
                        f.write(f"\n--- 选择器: {selector} ---\n")
                        elements = page.query_selector_all(f"xpath={selector}")
                        f.write(f"找到元素数量: {len(elements)}\n")

                        for j, element in enumerate(elements[:10]):  # 最多显示10个
                            try:
                                text = element.inner_text().strip()
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()

                                # 获取元素的HTML
                                outer_html = element.evaluate("el => el.outerHTML")

                                f.write(f"  元素 {j+1}:\n")
                                f.write(f"    文本: '{text}'\n")
                                f.write(f"    可见: {is_visible}\n")
                                f.write(f"    启用: {is_enabled}\n")
                                f.write(f"    HTML: {outer_html[:200]}...\n")

                            except Exception as e:
                                f.write(f"  元素 {j+1}: 获取信息失败 - {e}\n")

                    except Exception as e:
                        f.write(f"选择器失败: {e}\n")

            self.logger.info(f"📋 选项信息已保存: {options_info_path}")

        except Exception as e:
            self.logger.error(f"保存调试信息失败: {e}")

    def _verify_pagination_change(self, product_page) -> bool:
        """验证分页设置是否生效"""
        try:
            self.logger.info("🔍 验证分页设置是否生效...")

            verification_result = product_page.evaluate("""
                () => {
                    try {
                        // 检查当前页面显示的产品数量
                        const productRows = document.querySelectorAll('tbody tr, .product-row, [data-row-key]');
                        const currentPageSize = productRows.length;

                        // 多种方式检查分页显示文本
                        let paginationInfo = '';
                        const paginationSelectors = [
                            '.jx-pagination__total',
                            '.jx-pagination__total span',
                            '.pro-pagination .jx-pagination__total',
                            '.pro-pagination .jx-pagination__total span'
                        ];

                        for (const selector of paginationSelectors) {
                            const element = document.querySelector(selector);
                            if (element && element.textContent.trim()) {
                                paginationInfo = element.textContent.trim();
                                break;
                            }
                        }

                        // 多种方式检查页面大小选择器的当前值
                        let currentSizeText = '';
                        const sizeSelectors = [
                            '.jx-pagination__sizes .jx-select__placeholder span',
                            '.jx-pagination__sizes span',
                            '.pro-pagination .jx-select__placeholder span',
                            '.jx-select__selected-item span',
                            '.jx-select__placeholder span'
                        ];

                        for (const selector of sizeSelectors) {
                            const element = document.querySelector(selector);
                            if (element && element.textContent.includes('条/页')) {
                                currentSizeText = element.textContent.trim();
                                break;
                            }
                        }

                        return {
                            currentPageSize: currentPageSize,
                            paginationInfo: paginationInfo,
                            currentSizeText: currentSizeText
                        };
                    } catch (e) {
                        console.error("验证分页失败:", e);
                        return { error: e.message };
                    }
                }
            """)

            if verification_result and not verification_result.get('error'):
                current_page_size = verification_result.get('currentPageSize', 0)
                pagination_info = verification_result.get('paginationInfo', '')
                current_size_text = verification_result.get('currentSizeText', '')

                self.logger.info(f"📊 分页验证结果:")
                self.logger.info(f"   当前页面产品数量: {current_page_size}")
                self.logger.info(f"   分页信息: {pagination_info}")
                self.logger.info(f"   页面大小选择器: {current_size_text}")

                # 验证页面加载状态（不强制要求500个产品）
                # 目标：确认页面已经重新加载完成，可以进行后续操作
                self.logger.info(f"📊 页面加载状态检查:")
                self.logger.info(f"   当前页面产品数量: {current_page_size}")
                self.logger.info(f"   分页信息: {pagination_info}")
                self.logger.info(f"   页面大小选择器: {current_size_text}")

                # 我们已经成功点击了500条/页选项，并等待了足够时间
                # 现在只需要确认页面加载完成，继续执行后续操作
                self.logger.info("✅ 分页点击操作已完成，页面加载等待已结束")
                self.logger.info(f"🔄 继续执行全选操作（当前页面有 {current_page_size} 个产品）")
                return True
            else:
                self.logger.error(f"❌ 分页验证失败: {verification_result.get('error') if verification_result else '未知错误'}")
                return False

        except Exception as e:
            self.logger.error(f"验证分页设置异常: {e}")
            return False

    def _select_all_and_confirm(self, product_page) -> bool:
        """全选产品并确定 - 优化版本（使用全选当前页按钮）"""
        try:
            # 第一步：点击"全选当前页"按钮
            self.logger.info("🎯 第一步：点击'全选当前页'按钮")

            select_all_selectors = [
                # 方法1：根据您提供的HTML结构精确定位
                #"//button[@data-v-31cf2696 and contains(@class, 'jx-button--primary') and contains(@class, 'jx-button--small') and contains(@class, 'is-plain')]//span[contains(text(), '全选当前页')]/..",
                # 方法2：通过按钮文本直接查找
                #"//button[contains(text(), '全选当前页')]",
                # 方法3：通过span文本查找父级button
                #"//span[contains(text(), '全选当前页')]/parent::button",
                # 方法4：更通用的查找方式
                #"//button[contains(@class, 'jx-button') and contains(@class, 'is-plain')]//span[contains(text(), '全选当前页')]/..",
                # 方法5：通过完整的class组合查找
                "//button[contains(@class, 'jx-button--primary') and contains(@class, 'jx-button--small') and contains(@class, 'is-plain')]"
            ]

            select_all_clicked = False
            for i, selector in enumerate(select_all_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击全选当前页按钮: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    elements = product_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个全选按钮")

                    if elements:
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                element_text = element.inner_text().strip()
                                self.logger.info(f"     按钮 {j+1}: 文本='{element_text}', 可见={is_visible}, 启用={is_enabled}")

                                if is_visible and is_enabled and '全选当前页' in element_text:
                                    # 滚动到元素位置
                                    element.scroll_into_view_if_needed()

                                    # 随机延迟1-2秒，模拟真实用户操作
                                    delay = random.uniform(1.0, 2.0)
                                    self.logger.info(f"     ⏳ 随机延迟 {delay:.1f}秒...")
                                    time.sleep(delay)

                                    # 点击按钮
                                    element.click()
                                    self.logger.info(f"  ✅ 成功点击全选当前页按钮: 方法{i+1}, 按钮{j+1}")

                                    # 立即保存点击全选当前页后的页面状态
                                    self.logger.info("🔍 保存点击全选当前页后的页面状态...")
                                    self.save_page_debug_info(product_page, "after_select_all_current_page")

                                    select_all_clicked = True
                                    break

                            except Exception as e:
                                self.logger.warning(f"     点击按钮 {j+1} 失败: {e}")
                                continue

                        if select_all_clicked:
                            break
                    else:
                        self.logger.warning(f"     未找到全选当前页按钮: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 查找全选当前页按钮失败: 方法{i+1} - {e}")
                    continue

            if not select_all_clicked:
                self.logger.error("❌ 第一步失败：无法点击全选当前页按钮")
                return False

            # 等待全选操作生效 - 使用随机等待时间
            wait_time = random.randint(2, 5)  # 随机等待2-5秒
            self.logger.info(f"⏳ 等待全选操作生效... ({wait_time}秒)")
            time.sleep(wait_time)

            # 第二步：点击"确定"按钮
            self.logger.info("🎯 第二步：点击'确定'按钮")

            confirm_selectors = [
                # 方法1：根据您提供的HTML结构精确定位
                #"//button[@aria-disabled='false' and @type='button' and contains(@class, 'jx-button--primary') and contains(@class, 'jx-button--default') and contains(@class, 'pro-button')]//span[contains(text(), '确定')]/..",
                # 方法2：通过完整的class组合查找
                "//button[contains(@class, 'jx-button') and contains(@class, 'jx-button--primary') and contains(@class, 'jx-button--default') and contains(@class, 'pro-button')]",
                # 方法3：通过span文本查找父级button
                "//span[contains(text(), '确定')]/parent::button[@type='button']",
                # 方法4：查找包含确定文本的span的父级button
                "//button[@type='button' and contains(@class, 'jx-button')]//span[contains(text(), '确定')]/..",
                # 方法5：更通用的确定按钮查找
                "//button[@aria-disabled='false' and contains(@class, 'jx-button--primary')]//span[contains(text(), '确定')]/..",
                # 方法6：直接查找包含确定文本的按钮
                "//button[contains(text(), '确定')]",
                # 方法7：查找pro-button类的确定按钮
                "//button[contains(@class, 'pro-button')]//span[contains(text(), '确定')]/.."
            ]

            confirm_clicked = False
            for i, selector in enumerate(confirm_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击确定按钮: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    elements = product_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个确定按钮")

                    if elements:
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                element_text = element.inner_text().strip()
                                self.logger.info(f"     按钮 {j+1}: 文本='{element_text}', 可见={is_visible}, 启用={is_enabled}")

                                if is_visible and is_enabled and '确定' in element_text:
                                    # 滚动到元素位置
                                    element.scroll_into_view_if_needed()

                                    # 随机延迟1-2秒，模拟真实用户操作
                                    delay = random.uniform(1.0, 2.0)
                                    self.logger.info(f"     ⏳ 随机延迟 {delay:.1f}秒...")
                                    time.sleep(delay)

                                    # 点击按钮
                                    element.click()
                                    self.logger.info(f"  ✅ 成功点击确定按钮: 方法{i+1}, 按钮{j+1}")
                                    confirm_clicked = True
                                    # 立即保存点击确定按钮后的页面状态
                                    self.logger.info("🔍 保存点击确定按钮后的页面状态...")
                                    self.save_page_debug_info(product_page, "after_confirm_clicked_page")
                                    break

                            except Exception as e:
                                self.logger.warning(f"     点击按钮 {j+1} 失败: {e}")
                                continue

                        if confirm_clicked:
                            break
                    else:
                        self.logger.warning(f"     未找到确定按钮: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 查找确定按钮失败: 方法{i+1} - {e}")
                    continue

            if not confirm_clicked:
                self.logger.error("❌ 第二步失败：无法点击确定按钮")
                return False

            # 等待操作完成 - 使用随机等待时间
            wait_time = random.randint(3, 5)  # 随机等待3-5秒
            self.logger.info(f"⏳ 等待操作完成... ({wait_time}秒)")
            time.sleep(wait_time)

            # 确保添加产品对话框已关闭
            self.logger.info("🔍 确保添加产品对话框已关闭...")
            self._ensure_dialog_closed(product_page)

            # 处理管理活动产品页面的分页设置
            self.logger.info("🎯 处理管理活动产品页面的分页设置...")
            pagination_result = self._handle_activity_product_pagination(product_page)

            # 如果返回"continue"，传递给上层
            if pagination_result == "continue":
                return "continue"

            self.logger.info("✅ 产品选择和确认完成")
            return True

        except Exception as e:
            self.logger.error(f"全选产品并确定失败: {e}")
            return False

    def _ensure_dialog_closed(self, product_page) -> bool:
        """确保添加产品对话框已关闭"""
        try:
            # 检查是否还有对话框存在
            dialog_selectors = [
                "//div[@class='jx-overlay-dialog']",
                "//div[contains(@class, 'jx-overlay-dialog')]",
                "//div[@role='dialog']",
                "//div[contains(@aria-label, '添加产品')]"
            ]

            dialog_found = False
            for selector in dialog_selectors:
                try:
                    dialogs = product_page.query_selector_all(f"xpath={selector}")
                    if dialogs:
                        for dialog in dialogs:
                            if dialog.is_visible():
                                self.logger.warning(f"⚠️ 发现未关闭的对话框，尝试关闭...")
                                dialog_found = True

                                # 尝试点击关闭按钮
                                close_selectors = [
                                    ".//button[contains(@class, 'jx-dialog__headerbtn')]",
                                    ".//button[contains(@aria-label, '关闭')]",
                                    ".//button[contains(@class, 'jx-dialog__close')]",
                                    ".//span[contains(@class, 'jx-dialog__close')]"
                                ]

                                closed = False
                                for close_selector in close_selectors:
                                    try:
                                        close_btn = dialog.query_selector(f"xpath={close_selector}")
                                        if close_btn and close_btn.is_visible():
                                            close_btn.click()
                                            self.logger.info("✅ 成功关闭对话框")
                                            time.sleep(1)
                                            closed = True
                                            break
                                    except Exception as e:
                                        continue

                                # 如果关闭按钮不起作用，尝试按ESC键
                                if not closed:
                                    try:
                                        product_page.keyboard.press("Escape")
                                        self.logger.info("✅ 使用ESC键关闭对话框")
                                        time.sleep(1)
                                    except Exception as e:
                                        self.logger.warning(f"ESC键关闭失败: {e}")

                                break
                except Exception as e:
                    continue

            if not dialog_found:
                self.logger.info("✅ 没有发现未关闭的对话框")

            return True

        except Exception as e:
            self.logger.error(f"确保对话框关闭失败: {e}")
            return False

    def _handle_activity_product_pagination(self, product_page) -> bool:
        """处理管理活动产品页面的分页设置"""
        try:
            self.logger.info("🎯 开始处理管理活动产品页面分页设置...")

            # 等待页面完全加载
            time.sleep(2)

            # 保存点击分页前的页面状态
            self.logger.info("🔍 保存管理活动产品页面分页前状态...")
            self.save_page_debug_info(product_page, "before_activity_product_pagination")

            # 第一步：点击分页选择器（右下角的20条/页）
            self.logger.info("📋 第一步：点击管理活动产品页面的分页选择器")

            pagination_selectors = [
                # 方法1：精准定位 - 在pro-pagination容器内查找分页选择器
                "//div[contains(@class, 'pro-pagination')]//span[contains(@class, 'jx-pagination__sizes')]//div[contains(@class, 'jx-select')]",
                # 方法2：更精准 - 直接定位到20条/页的选择器容器
                "//div[contains(@class, 'pro-pagination')]//span[contains(text(), '20条/页')]/ancestor::div[contains(@class, 'jx-select__wrapper')]",
                # 方法3：通过pro-pagination容器定位
                "//div[contains(@class, 'pro-pagination')]//div[contains(@class, 'jx-select__wrapper')]",
                # 方法4：最精准 - 直接定位到包含20条/页的div
                "//div[contains(@class, 'pro-pagination')]//div[contains(@class, 'jx-select__selected-item')]//span[contains(text(), '20条/页')]/parent::div/parent::div/parent::div",
                # 方法5：备用方案 - 通过jx-pagination__sizes类定位
                "//div[contains(@class, 'pro-pagination')]//span[contains(@class, 'jx-pagination__sizes')]//div[contains(@class, 'jx-select__wrapper')]"
            ]

            selector_clicked = False
            for i, selector in enumerate(pagination_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击分页选择器: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    elements = product_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个分页选择器")

                    if elements:
                        # 检查每个元素的可见性和可点击性
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                self.logger.info(f"     元素 {j+1}: 可见={is_visible}, 启用={is_enabled}")

                                if is_visible and is_enabled:
                                    # 滚动到元素位置
                                    element.scroll_into_view_if_needed()
                                    time.sleep(1)

                                    # 点击元素
                                    element.click()
                                    self.logger.info(f"  ✅ 成功点击分页选择器: 方法{i+1}, 元素{j+1}")
                                    time.sleep(2)  # 等待下拉菜单展开

                                    # 立即保存下拉菜单展开后的页面状态
                                    self.logger.info("🔍 保存下拉菜单展开后的页面状态...")
                                    self.save_page_debug_info(product_page, "activity_product_pagination_dropdown_opened")

                                    selector_clicked = True
                                    break

                            except Exception as e:
                                self.logger.warning(f"     检查元素 {j+1} 失败: {e}")
                                continue

                        if selector_clicked:
                            break
                    else:
                        self.logger.warning(f"     未找到分页选择器: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击分页选择器失败: 方法{i+1} - {e}")
                    continue

            if not selector_clicked:
                self.logger.warning("⚠️ 第一步失败：无法点击分页选择器")
                return False

            # 第二步：点击500条/页选项
            self.logger.info("📋 第二步：点击500条/页选项")

            # 查找500条/页选项的选择器（复用之前成功的策略）
            option_500_selectors = [
                # 方法1：直接查找可见的span元素包含"500条/页"
                "//span[contains(text(), '500条/页')]",
                # 方法2：查找li元素包含span子元素的500条/页
                "//li[contains(@class, 'jx-select-dropdown__item')]//span[contains(text(), '500条/页')]",
                # 方法3：通过下拉菜单容器查找
                "//div[contains(@class, 'jx-select-dropdown')]//span[contains(text(), '500条/页')]",
                # 方法4：查找ul列表中的500条/页选项
                "//ul[contains(@class, 'jx-select-dropdown__list')]//li//span[contains(text(), '500条/页')]",
                # 方法5：备用方案 - 直接查找包含500的可见元素
                "//*[contains(text(), '500条/页')]"
            ]

            option_clicked = False
            for i, selector in enumerate(option_500_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击500条/页选项: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    elements = product_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个500条/页选项")

                    if elements:
                        # 选择第一个可见的选项
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                element_text = element.inner_text().strip()
                                self.logger.info(f"     选项 {j+1}: 文本='{element_text}', 可见={is_visible}, 启用={is_enabled}")

                                if is_visible and is_enabled and '500条/页' in element_text:
                                    # 滚动到元素位置
                                    element.scroll_into_view_if_needed()

                                    # 随机延迟1-2秒，模拟真实用户操作
                                    delay = random.uniform(1.0, 2.0)
                                    self.logger.info(f"     ⏳ 随机延迟 {delay:.1f}秒...")
                                    time.sleep(delay)

                                    # 尝试多种点击方式
                                    try:
                                        # 方式1：普通点击
                                        element.click()
                                        self.logger.info(f"  ✅ 成功点击500条/页选项: 方法{i+1}, 选项{j+1} (普通点击)")
                                    except Exception as e1:
                                        try:
                                            # 方式2：强制点击
                                            element.click(force=True)
                                            self.logger.info(f"  ✅ 成功点击500条/页选项: 方法{i+1}, 选项{j+1} (强制点击)")
                                        except Exception as e2:
                                            try:
                                                # 方式3：JavaScript点击
                                                element.evaluate("el => el.click()")
                                                self.logger.info(f"  ✅ 成功点击500条/页选项: 方法{i+1}, 选项{j+1} (JS点击)")
                                            except Exception as e3:
                                                self.logger.warning(f"     所有点击方式都失败: 普通={e1}, 强制={e2}, JS={e3}")
                                                continue

                                    time.sleep(3)  # 等待页面重新加载
                                    option_clicked = True
                                    break

                            except Exception as e:
                                self.logger.warning(f"     检查选项 {j+1} 失败: {e}")
                                continue

                        if option_clicked:
                            break
                    else:
                        self.logger.warning(f"     未找到500条/页选项: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 查找500条/页选项失败: 方法{i+1} - {e}")
                    continue

            if not option_clicked:
                self.logger.error("❌ 第二步失败：无法点击500条/页选项")

                # 保存失败时的页面状态用于调试
                self.logger.info("🔍 保存点击500条/页失败时的页面状态...")
                self.save_page_debug_info(product_page, "activity_product_pagination_500_click_failed")

                return False

            # 等待数据加载完成（数据量可能比较多）- 使用随机等待时间
            wait_time = random.randint(8, 10)  # 随机等待8-10秒
            self.logger.info(f"⏳ 等待管理活动产品页面数据加载完成... ({wait_time}秒)")
            time.sleep(wait_time)

            # 保存分页设置完成后的页面状态
            self.logger.info("🔍 保存分页设置完成后的页面状态...")
            self.save_page_debug_info(product_page, "after_activity_product_pagination")

            # 继续执行全选和批量秒杀价格操作
            self.logger.info("🎯 继续执行全选和批量秒杀价格操作...")
            batch_result = self._handle_select_all_and_batch_price(product_page)

            # 如果返回"continue"，传递给上层
            if batch_result == "continue":
                return "continue"

            self.logger.info("✅ 管理活动产品页面分页设置完成")
            return True

        except Exception as e:
            self.logger.error(f"处理管理活动产品页面分页设置失败: {e}")
            return False

    def _handle_select_all_and_batch_price(self, product_page) -> bool:
        """处理全选产品和批量秒杀价格操作"""
        try:
            self.logger.info("🎯 开始处理全选产品和批量秒杀价格操作...")

            # 等待页面稳定
            time.sleep(2)

            # 第一步：点击表头的全选复选框
            self.logger.info("📋 第一步：点击表头的全选复选框")

            # 保存点击全选前的页面状态
            self.logger.info("🔍 保存点击全选前的页面状态...")
            self.save_page_debug_info(product_page, "before_select_all_products")

            # 全选复选框的选择器（基于您提供的HTML结构）
            select_all_selectors = [
                # 方法1：精准定位表头的全选复选框
                "//div[contains(@class, 'pro-virtual-table__header-cell') and contains(@class, 'is-selection-column')]//input[contains(@class, 'jx-checkbox__original')]",
                # 方法2：通过label定位
                "//div[contains(@class, 'pro-virtual-table__header-cell') and contains(@class, 'is-selection-column')]//label[contains(@class, 'jx-checkbox')]",
                # 方法3：通过checkbox容器定位
                "//div[contains(@class, 'pro-virtual-table__header-cell') and contains(@class, 'is-selection-column')]//span[contains(@class, 'jx-checkbox__inner')]",
                # 方法4：更通用的表头复选框定位
                "//div[contains(@class, 'is-selection-column')]//input[@type='checkbox']",
                # 方法5：备用方案 - 通过pro-checkbox类定位
                "//label[contains(@class, 'pro-checkbox')]//input[@type='checkbox']"
            ]

            select_all_clicked = False
            for i, selector in enumerate(select_all_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击全选复选框: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    elements = product_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个全选复选框")

                    if elements:
                        # 检查每个元素的可见性和可点击性
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                self.logger.info(f"     复选框 {j+1}: 可见={is_visible}, 启用={is_enabled}")

                                if is_visible and is_enabled:
                                    # 滚动到元素位置
                                    element.scroll_into_view_if_needed()

                                    # 随机延迟1-2秒，模拟真实用户操作
                                    delay = random.uniform(1.0, 2.0)
                                    self.logger.info(f"     ⏳ 随机延迟 {delay:.1f}秒...")
                                    time.sleep(delay)

                                    # 尝试多种点击方式
                                    try:
                                        # 方式1：普通点击
                                        element.click()
                                        self.logger.info(f"  ✅ 成功点击全选复选框: 方法{i+1}, 复选框{j+1} (普通点击)")
                                    except Exception as e1:
                                        try:
                                            # 方式2：强制点击
                                            element.click(force=True)
                                            self.logger.info(f"  ✅ 成功点击全选复选框: 方法{i+1}, 复选框{j+1} (强制点击)")
                                        except Exception as e2:
                                            try:
                                                # 方式3：JavaScript点击
                                                element.evaluate("el => el.click()")
                                                self.logger.info(f"  ✅ 成功点击全选复选框: 方法{i+1}, 复选框{j+1} (JS点击)")
                                            except Exception as e3:
                                                self.logger.warning(f"     所有点击方式都失败: 普通={e1}, 强制={e2}, JS={e3}")
                                                continue

                                    # 等待全选操作生效
                                    wait_time = random.randint(3, 5)  # 随机等待3-5秒
                                    self.logger.info(f"⏳ 等待全选操作生效... ({wait_time}秒)")
                                    time.sleep(wait_time)

                                    # 保存全选后的页面状态
                                    self.logger.info("🔍 保存全选后的页面状态...")
                                    self.save_page_debug_info(product_page, "after_select_all_products")

                                    select_all_clicked = True
                                    break

                            except Exception as e:
                                self.logger.warning(f"     检查复选框 {j+1} 失败: {e}")
                                continue

                        if select_all_clicked:
                            break
                    else:
                        self.logger.warning(f"     未找到全选复选框: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击全选复选框失败: 方法{i+1} - {e}")
                    continue

            if not select_all_clicked:
                self.logger.error("❌ 第一步失败：无法点击全选复选框")

                # 保存失败时的页面状态用于调试
                self.logger.info("🔍 保存点击全选失败时的页面状态...")
                self.save_page_debug_info(product_page, "select_all_products_failed")

                return False

            # 第二步：点击"批量秒杀价格"按钮
            self.logger.info("📋 第二步：点击'批量秒杀价格'按钮")

            # 批量秒杀价格按钮的选择器（考虑HTML注释的影响）
            batch_price_selectors = [
                # 方法1：通过按钮的data-v属性和类定位，然后检查文本
                "//button[contains(@class, 'jx-button--primary') and contains(@class, 'jx-button--small') and contains(@class, 'pro-button')]",
                # 方法2：查找operate-left-box容器内的按钮
                "//div[contains(@class, 'operate-left-box')]//button[contains(@class, 'pro-button')]",
                # 方法3：通过sticky-operate-box容器定位
                "//div[contains(@class, 'sticky-operate-box')]//button[contains(@class, 'jx-button--primary')]",
                # 方法4：更宽泛的按钮查找
                "//button[contains(@class, 'jx-button') and contains(@class, 'pro-button')]",
                # 方法5：查找所有主要按钮
                "//button[contains(@class, 'jx-button--primary')]"
            ]

            batch_price_clicked = False
            for i, selector in enumerate(batch_price_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击批量秒杀价格按钮: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    elements = product_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个批量秒杀价格按钮")

                    if elements:
                        # 检查每个元素的可见性和可点击性
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                element_text = element.inner_text().strip()
                                self.logger.info(f"     按钮 {j+1}: 文本='{element_text}', 可见={is_visible}, 启用={is_enabled}")

                                # 检查按钮文本是否包含"批量秒杀价格"（忽略HTML注释和空白字符）
                                if is_visible and is_enabled and ('批量秒杀价格' in element_text or '批量秒杀价格' in element.text_content()):
                                    # 滚动到元素位置
                                    element.scroll_into_view_if_needed()

                                    # 随机延迟1-2秒，模拟真实用户操作
                                    delay = random.uniform(1.0, 2.0)
                                    self.logger.info(f"     ⏳ 随机延迟 {delay:.1f}秒...")
                                    time.sleep(delay)

                                    # 尝试多种点击方式
                                    try:
                                        # 方式1：普通点击
                                        element.click()
                                        self.logger.info(f"  ✅ 成功点击批量秒杀价格按钮: 方法{i+1}, 按钮{j+1} (普通点击)")
                                    except Exception as e1:
                                        try:
                                            # 方式2：强制点击
                                            element.click(force=True)
                                            self.logger.info(f"  ✅ 成功点击批量秒杀价格按钮: 方法{i+1}, 按钮{j+1} (强制点击)")
                                        except Exception as e2:
                                            try:
                                                # 方式3：JavaScript点击
                                                element.evaluate("el => el.click()")
                                                self.logger.info(f"  ✅ 成功点击批量秒杀价格按钮: 方法{i+1}, 按钮{j+1} (JS点击)")
                                            except Exception as e3:
                                                self.logger.warning(f"     所有点击方式都失败: 普通={e1}, 强制={e2}, JS={e3}")
                                                continue

                                    # 等待页面响应
                                    wait_time = random.randint(3, 5)  # 随机等待3-5秒
                                    self.logger.info(f"⏳ 等待批量秒杀价格页面加载... ({wait_time}秒)")
                                    time.sleep(wait_time)

                                    # 保存点击批量秒杀价格后的页面状态
                                    self.logger.info("🔍 保存点击批量秒杀价格后的页面状态...")
                                    self.save_page_debug_info(product_page, "after_batch_price_clicked")

                                    # 处理批量秒杀价格对话框
                                    self.logger.info("🎯 处理批量秒杀价格对话框...")
                                    if self._handle_batch_price_dialog(product_page):
                                        self.logger.info("✅ 批量秒杀价格设置完成")

                                        # 继续处理批量限购总量
                                        self.logger.info("🎯 开始处理批量限购总量...")
                                        if self._handle_batch_purchase_limit(product_page):
                                            self.logger.info("✅ 批量限购总量设置完成")

                                            # 继续处理批量单用户限购量
                                            self.logger.info("🎯 开始处理批量单用户限购量...")
                                            if self._handle_batch_user_purchase_limit(product_page):
                                                self.logger.info("✅ 批量单用户限购量设置完成")

                                                # 点击提交按钮，让系统检测问题
                                                self.logger.info("🎯 开始点击提交按钮...")
                                                submit_result = self._handle_submit_and_fix_errors(product_page)

                                                # 如果返回"continue"，传递给上层
                                                if submit_result == "continue":
                                                    return "continue"

                                    batch_price_clicked = True
                                    break

                            except Exception as e:
                                self.logger.warning(f"     检查按钮 {j+1} 失败: {e}")
                                continue

                        if batch_price_clicked:
                            break
                    else:
                        self.logger.warning(f"     未找到批量秒杀价格按钮: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击批量秒杀价格按钮失败: 方法{i+1} - {e}")
                    continue

            if not batch_price_clicked:
                self.logger.error("❌ 第二步失败：无法点击批量秒杀价格按钮")

                # 保存失败时的页面状态用于调试
                self.logger.info("🔍 保存点击批量秒杀价格失败时的页面状态...")
                self.save_page_debug_info(product_page, "batch_price_click_failed")

                return False

            self.logger.info("✅ 全选产品和批量秒杀价格操作完成")
            return submit_result if submit_result == "continue" else True

        except Exception as e:
            self.logger.error(f"处理全选产品和批量秒杀价格操作失败: {e}")
            return False

    def _handle_batch_price_dialog(self, product_page) -> bool:
        """处理批量秒杀价格对话框"""
        try:
            self.logger.info("🎯 开始处理批量秒杀价格对话框...")

            # 等待对话框出现
            time.sleep(2)

            # 保存对话框出现后的页面状态
            self.logger.info("🔍 保存对话框出现后的页面状态...")
            self.save_page_debug_info(product_page, "batch_price_dialog_opened")

            # 首先定位对话框容器，限制操作范围
            dialog_container = None
            dialog_selectors = [
                "//div[@class='jx-overlay-dialog']",
                "//div[contains(@class, 'jx-overlay-dialog')]",
                "//div[@role='dialog' and @aria-label='批量设置秒杀价']"
            ]

            for selector in dialog_selectors:
                try:
                    elements = product_page.query_selector_all(f"xpath={selector}")
                    if elements:
                        for element in elements:
                            if element.is_visible():
                                dialog_container = element
                                self.logger.info(f"✅ 找到对话框容器: {selector}")
                                break
                        if dialog_container:
                            break
                except Exception as e:
                    continue

            if not dialog_container:
                self.logger.error("❌ 无法找到批量设置秒杀价对话框容器")
                return False

            # 第一步：点击"统一折扣"选项（限制在对话框内）
            self.logger.info("📋 第一步：点击'统一折扣'选项")

            # 统一折扣选项的选择器（相对于对话框容器）
            discount_selectors = [
                # 方法1：直接点击radio按钮（我们已经找到了）
                ".//input[@type='radio' and @value='discountRatio']",
                # 方法2：通过radio按钮的value定位到其父级label
                ".//input[@type='radio' and @value='discountRatio']/parent::*/parent::label",
                # 方法3：直接定位包含统一折扣radio按钮的label
                ".//label[.//input[@type='radio' and @value='discountRatio']]",
                # 方法4：通过统一折扣文本定位label
                ".//label[contains(.//span[@class='jx-radio__label'], '统一折扣')]",
                # 方法5：通过span文本定位到label
                ".//span[contains(text(), '统一折扣：')]/parent::label",
                # 方法6：更宽泛的查找包含统一折扣的label
                ".//label[contains(., '统一折扣')]"
            ]

            discount_clicked = False
            for i, selector in enumerate(discount_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击统一折扣选项: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    # 在对话框容器内查找元素
                    elements = dialog_container.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     在对话框内找到 {len(elements)} 个统一折扣选项")

                    if elements:
                        # 检查每个元素的可见性和可点击性
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                element_text = element.inner_text().strip()
                                tag_name = element.evaluate("el => el.tagName.toLowerCase()")
                                self.logger.info(f"     选项 {j+1}: 标签={tag_name}, 文本='{element_text}', 可见={is_visible}, 启用={is_enabled}")

                                # 对于radio按钮，我们需要点击它或其label
                                if is_visible and is_enabled:
                                    # 如果是input元素，尝试找到其label或直接点击
                                    if tag_name == 'input':
                                        # 尝试找到关联的label
                                        try:
                                            label_element = element.evaluate("el => el.closest('label')")
                                            if label_element:
                                                element = label_element
                                                element_text = element.inner_text().strip()
                                                self.logger.info(f"     找到关联的label: 文本='{element_text}'")
                                        except:
                                            pass

                                    # 检查是否包含统一折扣相关文本，或者是radio按钮
                                    should_click = (
                                        '统一折扣' in element_text or
                                        '折扣' in element_text or
                                        tag_name == 'input' or
                                        tag_name == 'label'
                                    )

                                    if should_click:
                                        # 滚动到元素位置
                                        element.scroll_into_view_if_needed()

                                        # 随机延迟1-2秒，模拟真实用户操作
                                        delay = random.uniform(1.0, 2.0)
                                        self.logger.info(f"     ⏳ 随机延迟 {delay:.1f}秒...")
                                        time.sleep(delay)

                                        # 尝试多种点击方式
                                        try:
                                            # 方式1：普通点击
                                            element.click()
                                            self.logger.info(f"  ✅ 成功点击统一折扣选项: 方法{i+1}, 选项{j+1} (普通点击)")
                                        except Exception as e1:
                                            try:
                                                # 方式2：强制点击
                                                element.click(force=True)
                                                self.logger.info(f"  ✅ 成功点击统一折扣选项: 方法{i+1}, 选项{j+1} (强制点击)")
                                            except Exception as e2:
                                                try:
                                                    # 方式3：JavaScript点击
                                                    element.evaluate("el => el.click()")
                                                    self.logger.info(f"  ✅ 成功点击统一折扣选项: 方法{i+1}, 选项{j+1} (JS点击)")
                                                except Exception as e3:
                                                    self.logger.warning(f"     所有点击方式都失败: 普通={e1}, 强制={e2}, JS={e3}")
                                                    continue

                                        time.sleep(2)  # 等待选项生效

                                        # 保存点击统一折扣后的页面状态
                                        self.logger.info("🔍 保存点击统一折扣后的页面状态...")
                                        self.save_page_debug_info(product_page, "after_discount_option_clicked")

                                        discount_clicked = True
                                        break

                            except Exception as e:
                                self.logger.warning(f"     检查选项 {j+1} 失败: {e}")
                                continue

                        if discount_clicked:
                            break
                    else:
                        self.logger.warning(f"     未找到统一折扣选项: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击统一折扣选项失败: 方法{i+1} - {e}")
                    continue

            if not discount_clicked:
                self.logger.error("❌ 第一步失败：无法点击统一折扣选项")

                # 保存失败时的页面状态用于调试
                self.logger.info("🔍 保存点击统一折扣失败时的页面状态...")
                self.save_page_debug_info(product_page, "discount_option_click_failed")

                return False

            self.logger.info("✅ 统一折扣选项点击完成")

            # 第二步：输入折扣值35
            self.logger.info("📋 第二步：输入折扣值35")

            # 折扣输入框的选择器（限制在对话框内）
            discount_input_selectors = [
                # 方法1：通过统一折扣后面的输入框定位（最精确）
                ".//input[@type='radio' and @value='discountRatio']/parent::*/parent::*/following-sibling::div//input[@type='text']",
                # 方法2：通过ID定位（从HTML看到的具体ID）
                ".//input[@id='jx-id-3583-661']",
                # 方法3：通过统一折扣文本后的输入框
                ".//span[contains(text(), '统一折扣')]/parent::*/parent::*/following-sibling::div//input[@type='text']",
                # 方法4：通过%OFF后缀的输入框定位
                ".//span[contains(text(), '%OFF')]/parent::*/parent::*/input[@type='text']",
                # 方法5：通过输入框类型和位置定位
                ".//input[@type='text' and not(@disabled)]"
            ]

            discount_input_filled = False
            for i, selector in enumerate(discount_input_selectors):
                try:
                    self.logger.info(f"  🎯 尝试输入折扣值: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    # 在对话框容器内查找输入框
                    elements = dialog_container.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     在对话框内找到 {len(elements)} 个输入框")

                    if elements:
                        # 检查每个输入框的可见性和可编辑性
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                is_editable = element.is_editable()
                                placeholder = element.get_attribute('placeholder') or ''
                                value = element.get_attribute('value') or ''
                                self.logger.info(f"     输入框 {j+1}: 可见={is_visible}, 启用={is_enabled}, 可编辑={is_editable}, placeholder='{placeholder}', value='{value}'")

                                if is_visible and is_enabled and is_editable:
                                    # 滚动到元素位置
                                    element.scroll_into_view_if_needed()

                                    # 随机延迟1-2秒，模拟真实用户操作
                                    delay = random.uniform(1.0, 2.0)
                                    self.logger.info(f"     ⏳ 随机延迟 {delay:.1f}秒...")
                                    time.sleep(delay)

                                    try:
                                        # 清空输入框并输入35
                                        element.click()  # 先点击获得焦点
                                        time.sleep(0.5)
                                        element.fill('')  # 清空
                                        time.sleep(0.5)
                                        element.type('35')  # 输入35
                                        time.sleep(1)

                                        # 验证输入是否成功
                                        new_value = element.get_attribute('value') or element.input_value()
                                        self.logger.info(f"  ✅ 成功输入折扣值: 方法{i+1}, 输入框{j+1}, 值='{new_value}'")

                                        # 保存输入折扣值后的页面状态
                                        self.logger.info("🔍 保存输入折扣值后的页面状态...")
                                        self.save_page_debug_info(product_page, "after_discount_value_input")

                                        discount_input_filled = True
                                        break

                                    except Exception as e:
                                        self.logger.warning(f"     输入折扣值失败: {e}")
                                        continue

                            except Exception as e:
                                self.logger.warning(f"     检查输入框 {j+1} 失败: {e}")
                                continue

                        if discount_input_filled:
                            break
                    else:
                        self.logger.warning(f"     未找到输入框: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 输入折扣值失败: 方法{i+1} - {e}")
                    continue

            if not discount_input_filled:
                self.logger.error("❌ 第二步失败：无法输入折扣值35")

                # 保存失败时的页面状态用于调试
                self.logger.info("🔍 保存输入折扣值失败时的页面状态...")
                self.save_page_debug_info(product_page, "discount_input_failed")

                return False

            self.logger.info("✅ 折扣值输入完成")

            # 第三步：点击"应用至选中"按钮
            self.logger.info("📋 第三步：点击'应用至选中'按钮")

            # 应用至选中按钮的选择器（限制在对话框内）
            apply_button_selectors = [
                # 方法1：通过footer内的按钮文本定位（最精确）
                ".//footer[@class='jx-dialog__footer']//button[contains(.//span, '应用至选中')]",
                # 方法2：通过按钮文本直接定位
                ".//button[contains(.//span, '应用至选中')]",
                ".//button[contains(text(), '应用至选中')]",
                # 方法3：通过按钮类型和文本定位
                ".//button[@type='button' and contains(.//span, '应用至选中')]",
                # 方法4：通过按钮样式类定位
                ".//button[contains(@class, 'jx-button') and contains(.//span, '应用至选中')]",
                # 方法5：更宽泛的查找
                ".//*[contains(text(), '应用至选中')]"
            ]

            apply_button_clicked = False
            for i, selector in enumerate(apply_button_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击应用至选中按钮: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    # 在对话框容器内查找按钮
                    elements = dialog_container.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     在对话框内找到 {len(elements)} 个应用至选中按钮")

                    if elements:
                        # 检查每个按钮的可见性和可点击性
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                element_text = element.inner_text().strip()
                                self.logger.info(f"     按钮 {j+1}: 文本='{element_text}', 可见={is_visible}, 启用={is_enabled}")

                                if is_visible and is_enabled and ('应用至选中' in element_text or ('应用' in element_text and '选中' in element_text)):
                                    # 滚动到元素位置
                                    element.scroll_into_view_if_needed()

                                    # 随机延迟1-2秒，模拟真实用户操作
                                    delay = random.uniform(1.0, 2.0)
                                    self.logger.info(f"     ⏳ 随机延迟 {delay:.1f}秒...")
                                    time.sleep(delay)

                                    # 尝试多种点击方式
                                    try:
                                        # 方式1：普通点击
                                        element.click()
                                        self.logger.info(f"  ✅ 成功点击应用至选中按钮: 方法{i+1}, 按钮{j+1} (普通点击)")
                                    except Exception as e1:
                                        try:
                                            # 方式2：强制点击
                                            element.click(force=True)
                                            self.logger.info(f"  ✅ 成功点击应用至选中按钮: 方法{i+1}, 按钮{j+1} (强制点击)")
                                        except Exception as e2:
                                            try:
                                                # 方式3：JavaScript点击
                                                element.evaluate("el => el.click()")
                                                self.logger.info(f"  ✅ 成功点击应用至选中按钮: 方法{i+1}, 按钮{j+1} (JS点击)")
                                            except Exception as e3:
                                                self.logger.warning(f"     所有点击方式都失败: 普通={e1}, 强制={e2}, JS={e3}")
                                                continue

                                    time.sleep(3)  # 等待应用操作完成

                                    # 保存点击应用至选中后的页面状态
                                    self.logger.info("🔍 保存点击应用至选中后的页面状态...")
                                    self.save_page_debug_info(product_page, "after_apply_to_selected_clicked")

                                    apply_button_clicked = True
                                    break

                            except Exception as e:
                                self.logger.warning(f"     检查按钮 {j+1} 失败: {e}")
                                continue

                        if apply_button_clicked:
                            break
                    else:
                        self.logger.warning(f"     未找到应用至选中按钮: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击应用至选中按钮失败: 方法{i+1} - {e}")
                    continue

            if not apply_button_clicked:
                self.logger.error("❌ 第三步失败：无法点击应用至选中按钮")

                # 保存失败时的页面状态用于调试
                self.logger.info("🔍 保存点击应用至选中失败时的页面状态...")
                self.save_page_debug_info(product_page, "apply_button_click_failed")

                return False

            self.logger.info("✅ 应用至选中按钮点击完成")

            # 等待操作完成
            time.sleep(5)

            # 保存最终的页面状态
            self.logger.info("🔍 保存批量秒杀价格设置完成后的页面状态...")
            self.save_page_debug_info(product_page, "batch_price_dialog_completed")

            self.logger.info("🎉 批量秒杀价格对话框处理完成！")
            return True

        except Exception as e:
            self.logger.error(f"处理批量秒杀价格对话框失败: {e}")
            return False

    def _handle_batch_purchase_limit(self, product_page) -> bool:
        """处理批量限购总量"""
        try:
            self.logger.info("🎯 开始处理批量限购总量...")

            # 等待前一个对话框关闭
            time.sleep(3)

            # 第一步：点击"批量限购总量"按钮
            self.logger.info("📋 第一步：点击'批量限购总量'按钮")

            # 批量限购总量按钮的选择器
            purchase_limit_button_selectors = [
                # 方法1：通过按钮文本直接定位
                "//button[contains(text(), '批量限购总量')]",
                "//button[contains(.//span, '批量限购总量')]",
                # 方法2：通过按钮类型和文本定位
                "//button[@type='button' and contains(text(), '批量限购总量')]",
                # 方法3：更宽泛的查找
                "//*[contains(text(), '批量限购总量')]",
                "//*[contains(text(), '限购总量')]"
            ]

            purchase_limit_button_clicked = False
            for i, selector in enumerate(purchase_limit_button_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击批量限购总量按钮: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    elements = product_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个批量限购总量按钮")

                    if elements:
                        # 检查每个按钮的可见性和可点击性
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                element_text = element.inner_text().strip()
                                self.logger.info(f"     按钮 {j+1}: 文本='{element_text}', 可见={is_visible}, 启用={is_enabled}")

                                if is_visible and is_enabled and ('批量限购总量' in element_text or '限购总量' in element_text):
                                    # 滚动到元素位置
                                    element.scroll_into_view_if_needed()

                                    # 随机延迟1-2秒，模拟真实用户操作
                                    delay = random.uniform(1.0, 2.0)
                                    self.logger.info(f"     ⏳ 随机延迟 {delay:.1f}秒...")
                                    time.sleep(delay)

                                    # 尝试多种点击方式
                                    try:
                                        # 方式1：普通点击
                                        element.click()
                                        self.logger.info(f"  ✅ 成功点击批量限购总量按钮: 方法{i+1}, 按钮{j+1} (普通点击)")
                                    except Exception as e1:
                                        try:
                                            # 方式2：强制点击
                                            element.click(force=True)
                                            self.logger.info(f"  ✅ 成功点击批量限购总量按钮: 方法{i+1}, 按钮{j+1} (强制点击)")
                                        except Exception as e2:
                                            try:
                                                # 方式3：JavaScript点击
                                                element.evaluate("el => el.click()")
                                                self.logger.info(f"  ✅ 成功点击批量限购总量按钮: 方法{i+1}, 按钮{j+1} (JS点击)")
                                            except Exception as e3:
                                                self.logger.warning(f"     所有点击方式都失败: 普通={e1}, 强制={e2}, JS={e3}")
                                                continue

                                    time.sleep(3)  # 等待对话框出现

                                    # 保存点击批量限购总量后的页面状态
                                    self.logger.info("🔍 保存点击批量限购总量后的页面状态...")
                                    self.save_page_debug_info(product_page, "after_purchase_limit_button_clicked")

                                    purchase_limit_button_clicked = True
                                    break

                            except Exception as e:
                                self.logger.warning(f"     检查按钮 {j+1} 失败: {e}")
                                continue

                        if purchase_limit_button_clicked:
                            break
                    else:
                        self.logger.warning(f"     未找到批量限购总量按钮: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击批量限购总量按钮失败: 方法{i+1} - {e}")
                    continue

            if not purchase_limit_button_clicked:
                self.logger.error("❌ 第一步失败：无法点击批量限购总量按钮")
                return False

            # 处理批量限购总量对话框
            self.logger.info("🎯 处理批量限购总量对话框...")
            return self._handle_purchase_limit_dialog(product_page)

        except Exception as e:
            self.logger.error(f"处理批量限购总量失败: {e}")
            return False

    def _handle_purchase_limit_dialog(self, product_page) -> bool:
        """处理批量限购总量对话框"""
        try:
            self.logger.info("🎯 开始处理批量限购总量对话框...")

            # 等待对话框出现
            time.sleep(2)

            # 保存对话框出现后的页面状态
            self.logger.info("🔍 保存限购总量对话框出现后的页面状态...")
            self.save_page_debug_info(product_page, "purchase_limit_dialog_opened")

            # 首先定位对话框容器，限制操作范围
            dialog_container = None
            dialog_selectors = [
                "//div[@class='jx-overlay-dialog']",
                "//div[contains(@class, 'jx-overlay-dialog')]",
                "//div[@role='dialog' and @aria-label='批量设置限购总量']"
            ]

            for selector in dialog_selectors:
                try:
                    elements = product_page.query_selector_all(f"xpath={selector}")
                    if elements:
                        for element in elements:
                            if element.is_visible():
                                dialog_container = element
                                self.logger.info(f"✅ 找到限购总量对话框容器: {selector}")
                                break
                        if dialog_container:
                            break
                except Exception as e:
                    continue

            if not dialog_container:
                self.logger.error("❌ 无法找到批量限购总量对话框容器")
                return False

            # 第一步：选择"设定限制"选项
            self.logger.info("📋 第一步：选择'设定限制'选项")

            # 设定限制选项的选择器（相对于对话框容器）
            limit_option_selectors = [
                # 方法1：通过radio按钮直接定位
                ".//input[@type='radio']",
                # 方法2：通过包含设定限制文本的label定位
                ".//label[contains(., '设定限制')]",
                # 方法3：通过文本定位
                ".//*[contains(text(), '设定限制')]",
                # 方法4：通过radio按钮和文本组合定位
                ".//input[@type='radio']/following-sibling::*[contains(text(), '设定限制')]/parent::*"
            ]

            limit_option_clicked = False
            for i, selector in enumerate(limit_option_selectors):
                try:
                    self.logger.info(f"  🎯 尝试选择设定限制选项: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    # 在对话框容器内查找元素
                    elements = dialog_container.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     在对话框内找到 {len(elements)} 个设定限制选项")

                    if elements:
                        # 检查每个元素的可见性和可点击性
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                element_text = element.inner_text().strip()
                                tag_name = element.evaluate("el => el.tagName.toLowerCase()")
                                self.logger.info(f"     选项 {j+1}: 标签={tag_name}, 文本='{element_text}', 可见={is_visible}, 启用={is_enabled}")

                                if is_visible and is_enabled:
                                    # 如果是input元素，尝试找到其label或直接点击
                                    if tag_name == 'input':
                                        try:
                                            label_element = element.evaluate("el => el.closest('label')")
                                            if label_element:
                                                element = label_element
                                                element_text = element.inner_text().strip()
                                                self.logger.info(f"     找到关联的label: 文本='{element_text}'")
                                        except:
                                            pass

                                    # 检查是否应该点击（设定限制相关或radio按钮）
                                    should_click = (
                                        '设定限制' in element_text or
                                        tag_name == 'input' or
                                        tag_name == 'label'
                                    )

                                    if should_click:
                                        # 滚动到元素位置
                                        element.scroll_into_view_if_needed()

                                        # 随机延迟1-2秒，模拟真实用户操作
                                        delay = random.uniform(1.0, 2.0)
                                        self.logger.info(f"     ⏳ 随机延迟 {delay:.1f}秒...")
                                        time.sleep(delay)

                                        # 尝试多种点击方式
                                        try:
                                            # 方式1：普通点击
                                            element.click()
                                            self.logger.info(f"  ✅ 成功选择设定限制选项: 方法{i+1}, 选项{j+1} (普通点击)")
                                        except Exception as e1:
                                            try:
                                                # 方式2：强制点击
                                                element.click(force=True)
                                                self.logger.info(f"  ✅ 成功选择设定限制选项: 方法{i+1}, 选项{j+1} (强制点击)")
                                            except Exception as e2:
                                                try:
                                                    # 方式3：JavaScript点击
                                                    element.evaluate("el => el.click()")
                                                    self.logger.info(f"  ✅ 成功选择设定限制选项: 方法{i+1}, 选项{j+1} (JS点击)")
                                                except Exception as e3:
                                                    self.logger.warning(f"     所有点击方式都失败: 普通={e1}, 强制={e2}, JS={e3}")
                                                    continue

                                        time.sleep(2)  # 等待选项生效

                                        # 保存选择设定限制后的页面状态
                                        self.logger.info("🔍 保存选择设定限制后的页面状态...")
                                        self.save_page_debug_info(product_page, "after_limit_option_selected")

                                        limit_option_clicked = True
                                        break

                            except Exception as e:
                                self.logger.warning(f"     检查选项 {j+1} 失败: {e}")
                                continue

                        if limit_option_clicked:
                            break
                    else:
                        self.logger.warning(f"     未找到设定限制选项: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 选择设定限制选项失败: 方法{i+1} - {e}")
                    continue

            if not limit_option_clicked:
                self.logger.error("❌ 第一步失败：无法选择设定限制选项")

                # 保存失败时的页面状态用于调试
                self.logger.info("🔍 保存选择设定限制失败时的页面状态...")
                self.save_page_debug_info(product_page, "limit_option_select_failed")

                return False

            self.logger.info("✅ 设定限制选项选择完成")

            # 第二步：输入限购数量3
            self.logger.info("📋 第二步：输入限购数量3")

            # 限购数量输入框的选择器（限制在对话框内）
            quantity_input_selectors = [
                # 方法1：通过输入框类型定位
                ".//input[@type='text' and not(@disabled)]",
                ".//input[@type='number' and not(@disabled)]",
                # 方法2：通过设定限制后面的输入框定位
                ".//input[@type='radio']/parent::*/parent::*/following-sibling::*//input[@type='text']",
                # 方法3：通过placeholder或其他属性定位
                ".//input[contains(@placeholder, '请输入') or @placeholder='']",
                # 方法4：更宽泛的输入框查找
                ".//input[not(@type='radio') and not(@type='checkbox') and not(@disabled)]"
            ]

            quantity_input_filled = False
            for i, selector in enumerate(quantity_input_selectors):
                try:
                    self.logger.info(f"  🎯 尝试输入限购数量: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    # 在对话框容器内查找输入框
                    elements = dialog_container.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     在对话框内找到 {len(elements)} 个输入框")

                    if elements:
                        # 检查每个输入框的可见性和可编辑性
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                is_editable = element.is_editable()
                                placeholder = element.get_attribute('placeholder') or ''
                                value = element.get_attribute('value') or ''
                                input_type = element.get_attribute('type') or ''
                                self.logger.info(f"     输入框 {j+1}: 类型={input_type}, 可见={is_visible}, 启用={is_enabled}, 可编辑={is_editable}, placeholder='{placeholder}', value='{value}'")

                                if is_visible and is_enabled and is_editable:
                                    # 滚动到元素位置
                                    element.scroll_into_view_if_needed()

                                    # 随机延迟1-2秒，模拟真实用户操作
                                    delay = random.uniform(1.0, 2.0)
                                    self.logger.info(f"     ⏳ 随机延迟 {delay:.1f}秒...")
                                    time.sleep(delay)

                                    try:
                                        # 清空输入框并输入3
                                        element.click()  # 先点击获得焦点
                                        time.sleep(0.5)
                                        element.fill('')  # 清空
                                        time.sleep(0.5)
                                        element.type('5')  # 输入3
                                        time.sleep(1)

                                        # 验证输入是否成功
                                        new_value = element.get_attribute('value') or element.input_value()
                                        self.logger.info(f"  ✅ 成功输入限购数量: 方法{i+1}, 输入框{j+1}, 值='{new_value}'")

                                        # 保存输入限购数量后的页面状态
                                        self.logger.info("🔍 保存输入限购数量后的页面状态...")
                                        self.save_page_debug_info(product_page, "after_quantity_input")

                                        quantity_input_filled = True
                                        break

                                    except Exception as e:
                                        self.logger.warning(f"     输入限购数量失败: {e}")
                                        continue

                            except Exception as e:
                                self.logger.warning(f"     检查输入框 {j+1} 失败: {e}")
                                continue

                        if quantity_input_filled:
                            break
                    else:
                        self.logger.warning(f"     未找到输入框: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 输入限购数量失败: 方法{i+1} - {e}")
                    continue

            if not quantity_input_filled:
                self.logger.error("❌ 第二步失败：无法输入限购数量3")

                # 保存失败时的页面状态用于调试
                self.logger.info("🔍 保存输入限购数量失败时的页面状态...")
                self.save_page_debug_info(product_page, "quantity_input_failed")

                return False

            self.logger.info("✅ 限购数量输入完成")

            # 第三步：点击"应用至选中"按钮
            self.logger.info("📋 第三步：点击'应用至选中'按钮")

            # 应用至选中按钮的选择器（限制在对话框内）
            apply_button_selectors = [
                # 方法1：通过footer内的按钮文本定位（最精确）
                ".//footer[@class='jx-dialog__footer']//button[contains(.//span, '应用至选中')]",
                # 方法2：通过按钮文本直接定位
                ".//button[contains(.//span, '应用至选中')]",
                ".//button[contains(text(), '应用至选中')]",
                # 方法3：通过按钮类型和文本定位
                ".//button[@type='button' and contains(.//span, '应用至选中')]",
                # 方法4：通过按钮样式类定位
                ".//button[contains(@class, 'jx-button') and contains(.//span, '应用至选中')]",
                # 方法5：更宽泛的查找
                ".//*[contains(text(), '应用至选中')]"
            ]

            apply_button_clicked = False
            for i, selector in enumerate(apply_button_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击应用至选中按钮: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    # 在对话框容器内查找按钮
                    elements = dialog_container.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     在对话框内找到 {len(elements)} 个应用至选中按钮")

                    if elements:
                        # 检查每个按钮的可见性和可点击性
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                element_text = element.inner_text().strip()
                                self.logger.info(f"     按钮 {j+1}: 文本='{element_text}', 可见={is_visible}, 启用={is_enabled}")

                                if is_visible and is_enabled and ('应用至选中' in element_text or ('应用' in element_text and '选中' in element_text)):
                                    # 滚动到元素位置
                                    element.scroll_into_view_if_needed()

                                    # 随机延迟1-2秒，模拟真实用户操作
                                    delay = random.uniform(1.0, 2.0)
                                    self.logger.info(f"     ⏳ 随机延迟 {delay:.1f}秒...")
                                    time.sleep(delay)

                                    # 尝试多种点击方式
                                    try:
                                        # 方式1：普通点击
                                        element.click()
                                        self.logger.info(f"  ✅ 成功点击应用至选中按钮: 方法{i+1}, 按钮{j+1} (普通点击)")
                                    except Exception as e1:
                                        try:
                                            # 方式2：强制点击
                                            element.click(force=True)
                                            self.logger.info(f"  ✅ 成功点击应用至选中按钮: 方法{i+1}, 按钮{j+1} (强制点击)")
                                        except Exception as e2:
                                            try:
                                                # 方式3：JavaScript点击
                                                element.evaluate("el => el.click()")
                                                self.logger.info(f"  ✅ 成功点击应用至选中按钮: 方法{i+1}, 按钮{j+1} (JS点击)")
                                            except Exception as e3:
                                                self.logger.warning(f"     所有点击方式都失败: 普通={e1}, 强制={e2}, JS={e3}")
                                                continue

                                    time.sleep(3)  # 等待应用操作完成

                                    # 保存点击应用至选中后的页面状态
                                    self.logger.info("🔍 保存点击应用至选中后的页面状态...")
                                    self.save_page_debug_info(product_page, "after_purchase_limit_apply_clicked")

                                    apply_button_clicked = True
                                    break

                            except Exception as e:
                                self.logger.warning(f"     检查按钮 {j+1} 失败: {e}")
                                continue

                        if apply_button_clicked:
                            break
                    else:
                        self.logger.warning(f"     未找到应用至选中按钮: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击应用至选中按钮失败: 方法{i+1} - {e}")
                    continue

            if not apply_button_clicked:
                self.logger.error("❌ 第三步失败：无法点击应用至选中按钮")

                # 保存失败时的页面状态用于调试
                self.logger.info("🔍 保存点击应用至选中失败时的页面状态...")
                self.save_page_debug_info(product_page, "purchase_limit_apply_button_click_failed")

                return False

            self.logger.info("✅ 应用至选中按钮点击完成")

            # 等待操作完成
            time.sleep(5)

            # 保存最终的页面状态
            self.logger.info("🔍 保存批量限购总量设置完成后的页面状态...")
            self.save_page_debug_info(product_page, "purchase_limit_dialog_completed")

            self.logger.info("🎉 批量限购总量对话框处理完成！")
            return True

        except Exception as e:
            self.logger.error(f"处理批量限购总量对话框失败: {e}")
            return False

    def _handle_batch_user_purchase_limit(self, product_page) -> bool:
        """处理批量单用户限购量"""
        try:
            self.logger.info("🎯 开始处理批量单用户限购量...")

            # 等待前一个对话框关闭
            time.sleep(3)

            # 第一步：点击"批量单用户限购量"按钮
            self.logger.info("📋 第一步：点击'批量单用户限购量'按钮")

            # 批量单用户限购量按钮的选择器
            user_limit_button_selectors = [
                # 方法1：通过按钮文本直接定位
                "//button[contains(text(), '批量单用户限购量')]",
                "//button[contains(.//span, '批量单用户限购量')]",
                # 方法2：通过按钮类型和文本定位
                "//button[@type='button' and contains(text(), '批量单用户限购量')]",
                # 方法3：更宽泛的查找
                "//*[contains(text(), '批量单用户限购量')]",
                "//*[contains(text(), '单用户限购量')]",
                "//*[contains(text(), '用户限购量')]"
            ]

            user_limit_button_clicked = False
            for i, selector in enumerate(user_limit_button_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击批量单用户限购量按钮: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    elements = product_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个批量单用户限购量按钮")

                    if elements:
                        # 检查每个按钮的可见性和可点击性
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                element_text = element.inner_text().strip()
                                self.logger.info(f"     按钮 {j+1}: 文本='{element_text}', 可见={is_visible}, 启用={is_enabled}")

                                if is_visible and is_enabled and ('批量单用户限购量' in element_text or '单用户限购量' in element_text or '用户限购量' in element_text):
                                    # 滚动到元素位置
                                    element.scroll_into_view_if_needed()

                                    # 随机延迟1-2秒，模拟真实用户操作
                                    delay = random.uniform(1.0, 2.0)
                                    self.logger.info(f"     ⏳ 随机延迟 {delay:.1f}秒...")
                                    time.sleep(delay)

                                    # 尝试多种点击方式
                                    try:
                                        # 方式1：普通点击
                                        element.click()
                                        self.logger.info(f"  ✅ 成功点击批量单用户限购量按钮: 方法{i+1}, 按钮{j+1} (普通点击)")
                                    except Exception as e1:
                                        try:
                                            # 方式2：强制点击
                                            element.click(force=True)
                                            self.logger.info(f"  ✅ 成功点击批量单用户限购量按钮: 方法{i+1}, 按钮{j+1} (强制点击)")
                                        except Exception as e2:
                                            try:
                                                # 方式3：JavaScript点击
                                                element.evaluate("el => el.click()")
                                                self.logger.info(f"  ✅ 成功点击批量单用户限购量按钮: 方法{i+1}, 按钮{j+1} (JS点击)")
                                            except Exception as e3:
                                                self.logger.warning(f"     所有点击方式都失败: 普通={e1}, 强制={e2}, JS={e3}")
                                                continue

                                    time.sleep(3)  # 等待对话框出现

                                    # 保存点击批量单用户限购量后的页面状态
                                    self.logger.info("🔍 保存点击批量单用户限购量后的页面状态...")
                                    self.save_page_debug_info(product_page, "after_user_limit_button_clicked")

                                    user_limit_button_clicked = True
                                    break

                            except Exception as e:
                                self.logger.warning(f"     检查按钮 {j+1} 失败: {e}")
                                continue

                        if user_limit_button_clicked:
                            break
                    else:
                        self.logger.warning(f"     未找到批量单用户限购量按钮: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击批量单用户限购量按钮失败: 方法{i+1} - {e}")
                    continue

            if not user_limit_button_clicked:
                self.logger.error("❌ 第一步失败：无法点击批量单用户限购量按钮")
                return False

            # 处理批量单用户限购量对话框
            self.logger.info("🎯 处理批量单用户限购量对话框...")
            return self._handle_user_purchase_limit_dialog(product_page)

        except Exception as e:
            self.logger.error(f"处理批量单用户限购量失败: {e}")
            return False

    def _handle_user_purchase_limit_dialog(self, product_page) -> bool:
        """处理批量单用户限购量对话框"""
        try:
            self.logger.info("🎯 开始处理批量单用户限购量对话框...")

            # 等待对话框出现
            time.sleep(2)

            # 保存对话框出现后的页面状态
            self.logger.info("🔍 保存单用户限购量对话框出现后的页面状态...")
            self.save_page_debug_info(product_page, "user_limit_dialog_opened")

            # 首先定位对话框容器，限制操作范围
            dialog_container = None
            dialog_selectors = [
                "//div[@class='jx-overlay-dialog']",
                "//div[contains(@class, 'jx-overlay-dialog')]",
                "//div[@role='dialog' and @aria-label='批量设置单用户限购量']"
            ]

            for selector in dialog_selectors:
                try:
                    elements = product_page.query_selector_all(f"xpath={selector}")
                    if elements:
                        for element in elements:
                            if element.is_visible():
                                dialog_container = element
                                self.logger.info(f"✅ 找到单用户限购量对话框容器: {selector}")
                                break
                        if dialog_container:
                            break
                except Exception as e:
                    continue

            if not dialog_container:
                self.logger.error("❌ 无法找到批量单用户限购量对话框容器")
                return False

            # 第一步：选择"设定限制"选项
            self.logger.info("📋 第一步：选择'设定限制'选项")

            # 设定限制选项的选择器（相对于对话框容器）
            limit_option_selectors = [
                # 方法1：通过radio按钮直接定位
                ".//input[@type='radio']",
                # 方法2：通过包含设定限制文本的label定位
                ".//label[contains(., '设定限制')]",
                # 方法3：通过文本定位
                ".//*[contains(text(), '设定限制')]",
                # 方法4：通过radio按钮和文本组合定位
                ".//input[@type='radio']/following-sibling::*[contains(text(), '设定限制')]/parent::*"
            ]

            limit_option_clicked = False
            for i, selector in enumerate(limit_option_selectors):
                try:
                    self.logger.info(f"  🎯 尝试选择设定限制选项: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    # 在对话框容器内查找元素
                    elements = dialog_container.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     在对话框内找到 {len(elements)} 个设定限制选项")

                    if elements:
                        # 检查每个元素的可见性和可点击性
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                element_text = element.inner_text().strip()
                                tag_name = element.evaluate("el => el.tagName.toLowerCase()")
                                self.logger.info(f"     选项 {j+1}: 标签={tag_name}, 文本='{element_text}', 可见={is_visible}, 启用={is_enabled}")

                                if is_visible and is_enabled:
                                    # 如果是input元素，尝试找到其label或直接点击
                                    if tag_name == 'input':
                                        try:
                                            label_element = element.evaluate("el => el.closest('label')")
                                            if label_element:
                                                element = label_element
                                                element_text = element.inner_text().strip()
                                                self.logger.info(f"     找到关联的label: 文本='{element_text}'")
                                        except:
                                            pass

                                    # 检查是否应该点击（设定限制相关或radio按钮）
                                    should_click = (
                                        '设定限制' in element_text or
                                        tag_name == 'input' or
                                        tag_name == 'label'
                                    )

                                    if should_click:
                                        # 滚动到元素位置
                                        element.scroll_into_view_if_needed()

                                        # 随机延迟1-2秒，模拟真实用户操作
                                        delay = random.uniform(1.0, 2.0)
                                        self.logger.info(f"     ⏳ 随机延迟 {delay:.1f}秒...")
                                        time.sleep(delay)

                                        # 尝试多种点击方式
                                        try:
                                            # 方式1：普通点击
                                            element.click()
                                            self.logger.info(f"  ✅ 成功选择设定限制选项: 方法{i+1}, 选项{j+1} (普通点击)")
                                        except Exception as e1:
                                            try:
                                                # 方式2：强制点击
                                                element.click(force=True)
                                                self.logger.info(f"  ✅ 成功选择设定限制选项: 方法{i+1}, 选项{j+1} (强制点击)")
                                            except Exception as e2:
                                                try:
                                                    # 方式3：JavaScript点击
                                                    element.evaluate("el => el.click()")
                                                    self.logger.info(f"  ✅ 成功选择设定限制选项: 方法{i+1}, 选项{j+1} (JS点击)")
                                                except Exception as e3:
                                                    self.logger.warning(f"     所有点击方式都失败: 普通={e1}, 强制={e2}, JS={e3}")
                                                    continue

                                        time.sleep(2)  # 等待选项生效

                                        # 保存选择设定限制后的页面状态
                                        self.logger.info("🔍 保存选择设定限制后的页面状态...")
                                        self.save_page_debug_info(product_page, "after_user_limit_option_selected")

                                        limit_option_clicked = True
                                        break

                            except Exception as e:
                                self.logger.warning(f"     检查选项 {j+1} 失败: {e}")
                                continue

                        if limit_option_clicked:
                            break
                    else:
                        self.logger.warning(f"     未找到设定限制选项: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 选择设定限制选项失败: 方法{i+1} - {e}")
                    continue

            if not limit_option_clicked:
                self.logger.error("❌ 第一步失败：无法选择设定限制选项")

                # 保存失败时的页面状态用于调试
                self.logger.info("🔍 保存选择设定限制失败时的页面状态...")
                self.save_page_debug_info(product_page, "user_limit_option_select_failed")

                return False

            self.logger.info("✅ 设定限制选项选择完成")

            # 第二步：输入数量"3"
            self.logger.info("📋 第二步：输入数量'3'")

            # 数量输入框的选择器（相对于对话框容器）
            quantity_input_selectors = [
                # 方法1：通过placeholder定位
                ".//input[@placeholder='请输入']",
                # 方法2：通过input类型定位
                ".//input[@type='text']",
                ".//input[@type='number']",
                # 方法3：通过class定位
                ".//input[contains(@class, 'jx-input__inner')]",
                # 方法4：更宽泛的input定位
                ".//input"
            ]

            quantity_input_filled = False
            for i, selector in enumerate(quantity_input_selectors):
                try:
                    self.logger.info(f"  🎯 尝试输入数量: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    # 在对话框容器内查找元素
                    elements = dialog_container.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     在对话框内找到 {len(elements)} 个输入框")

                    if elements:
                        # 检查每个输入框的可见性和可用性
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                placeholder = element.get_attribute("placeholder") or ""
                                input_type = element.get_attribute("type") or ""
                                current_value = element.input_value() or ""
                                self.logger.info(f"     输入框 {j+1}: 类型={input_type}, placeholder='{placeholder}', 当前值='{current_value}', 可见={is_visible}, 启用={is_enabled}")

                                if is_visible and is_enabled:
                                    # 滚动到元素位置
                                    element.scroll_into_view_if_needed()

                                    # 随机延迟1-2秒，模拟真实用户操作
                                    delay = random.uniform(1.0, 2.0)
                                    self.logger.info(f"     ⏳ 随机延迟 {delay:.1f}秒...")
                                    time.sleep(delay)

                                    # 清空输入框并输入数量
                                    try:
                                        # 先点击输入框获得焦点
                                        element.click()
                                        time.sleep(0.5)

                                        # 清空现有内容
                                        element.fill("")
                                        time.sleep(0.5)

                                        # 输入数量"3"
                                        element.fill("3")
                                        time.sleep(1)

                                        # 验证输入是否成功
                                        new_value = element.input_value()
                                        if new_value == "3":
                                            self.logger.info(f"  ✅ 成功输入数量'3': 方法{i+1}, 输入框{j+1}")

                                            # 保存输入数量后的页面状态
                                            self.logger.info("🔍 保存输入数量后的页面状态...")
                                            self.save_page_debug_info(product_page, "after_user_limit_quantity_input")

                                            quantity_input_filled = True
                                            break
                                        else:
                                            self.logger.warning(f"     输入验证失败: 期望='3', 实际='{new_value}'")

                                    except Exception as e:
                                        self.logger.warning(f"     输入数量失败: {e}")
                                        continue

                            except Exception as e:
                                self.logger.warning(f"     检查输入框 {j+1} 失败: {e}")
                                continue

                        if quantity_input_filled:
                            break
                    else:
                        self.logger.warning(f"     未找到数量输入框: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 输入数量失败: 方法{i+1} - {e}")
                    continue

            if not quantity_input_filled:
                self.logger.error("❌ 第二步失败：无法输入数量")

                # 保存失败时的页面状态用于调试
                self.logger.info("🔍 保存输入数量失败时的页面状态...")
                self.save_page_debug_info(product_page, "user_limit_quantity_input_failed")

                return False

            self.logger.info("✅ 数量输入完成")

            # 第三步：点击"应用至选中"按钮
            self.logger.info("📋 第三步：点击'应用至选中'按钮")

            # 应用至选中按钮的选择器（相对于对话框容器）
            apply_button_selectors = [
                # 方法1：通过按钮文本直接定位
                ".//button[contains(text(), '应用至选中')]",
                ".//button[contains(.//span, '应用至选中')]",
                # 方法2：通过按钮类型和文本定位
                ".//button[@type='button' and contains(text(), '应用至选中')]",
                # 方法3：通过class和文本定位
                ".//button[contains(@class, 'jx-button') and contains(text(), '应用至选中')]",
                # 方法4：更宽泛的查找
                ".//*[contains(text(), '应用至选中')]"
            ]

            apply_button_clicked = False
            for i, selector in enumerate(apply_button_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击应用至选中按钮: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    # 在对话框容器内查找元素
                    elements = dialog_container.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     在对话框内找到 {len(elements)} 个应用至选中按钮")

                    if elements:
                        # 检查每个按钮的可见性和可点击性
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                element_text = element.inner_text().strip()
                                self.logger.info(f"     按钮 {j+1}: 文本='{element_text}', 可见={is_visible}, 启用={is_enabled}")

                                if is_visible and is_enabled and '应用至选中' in element_text:
                                    # 滚动到元素位置
                                    element.scroll_into_view_if_needed()

                                    # 随机延迟1-2秒，模拟真实用户操作
                                    delay = random.uniform(1.0, 2.0)
                                    self.logger.info(f"     ⏳ 随机延迟 {delay:.1f}秒...")
                                    time.sleep(delay)

                                    # 尝试多种点击方式
                                    try:
                                        # 方式1：普通点击
                                        element.click()
                                        self.logger.info(f"  ✅ 成功点击应用至选中按钮: 方法{i+1}, 按钮{j+1} (普通点击)")
                                    except Exception as e1:
                                        try:
                                            # 方式2：强制点击
                                            element.click(force=True)
                                            self.logger.info(f"  ✅ 成功点击应用至选中按钮: 方法{i+1}, 按钮{j+1} (强制点击)")
                                        except Exception as e2:
                                            try:
                                                # 方式3：JavaScript点击
                                                element.evaluate("el => el.click()")
                                                self.logger.info(f"  ✅ 成功点击应用至选中按钮: 方法{i+1}, 按钮{j+1} (JS点击)")
                                            except Exception as e3:
                                                self.logger.warning(f"     所有点击方式都失败: 普通={e1}, 强制={e2}, JS={e3}")
                                                continue

                                    time.sleep(3)  # 等待操作完成

                                    # 保存点击应用至选中后的页面状态
                                    self.logger.info("🔍 保存点击应用至选中后的页面状态...")
                                    self.save_page_debug_info(product_page, "after_user_limit_apply_clicked")

                                    apply_button_clicked = True
                                    break

                            except Exception as e:
                                self.logger.warning(f"     检查按钮 {j+1} 失败: {e}")
                                continue

                        if apply_button_clicked:
                            break
                    else:
                        self.logger.warning(f"     未找到应用至选中按钮: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击应用至选中按钮失败: 方法{i+1} - {e}")
                    continue

            if not apply_button_clicked:
                self.logger.error("❌ 第三步失败：无法点击应用至选中按钮")

                # 保存失败时的页面状态用于调试
                self.logger.info("🔍 保存点击应用至选中失败时的页面状态...")
                self.save_page_debug_info(product_page, "user_limit_apply_button_failed")

                return False

            self.logger.info("✅ 应用至选中按钮点击完成")

            # 等待对话框关闭和操作完成
            time.sleep(5)

            # 保存最终状态
            self.logger.info("🔍 保存批量单用户限购量完成后的页面状态...")
            self.save_page_debug_info(product_page, "user_limit_completed")

            self.logger.info("🎉 批量单用户限购量设置完成！")
            return True

        except Exception as e:
            self.logger.error(f"处理批量单用户限购量对话框失败: {e}")
            return False

    def _check_and_adjust_stock_limits(self, product_page) -> bool:
        """检查库存和限购量，如果库存不足则调整限购量"""
        try:
            self.logger.info("🎯 开始检查库存和限购量...")

            # 等待页面稳定
            time.sleep(3)

            # 保存检查前的页面状态
            self.logger.info("🔍 保存检查前的页面状态...")
            self.save_page_debug_info(product_page, "before_stock_check")

            # 查找虚拟表格容器
            table_container_selectors = [
                "//div[@class='vue-recycle-scroller ready page-mode direction-vertical pro-virtual-table__body pro-virtual-scroll pro-virtual-table__body']",
                "//div[contains(@class, 'pro-virtual-table__body') and contains(@class, 'vue-recycle-scroller')]",
                "//div[contains(@class, 'pro-virtual-table__body')]"
            ]

            table_container = None
            for selector in table_container_selectors:
                try:
                    elements = product_page.query_selector_all(f"xpath={selector}")
                    if elements:
                        for element in elements:
                            if element.is_visible():
                                table_container = element
                                self.logger.info(f"✅ 找到表格容器: {selector}")
                                break
                        if table_container:
                            break
                except Exception as e:
                    continue

            if not table_container:
                self.logger.error("❌ 无法找到表格容器")
                return False

            # 查找所有产品行
            row_selectors = [
                ".//div[@class='pro-virtual-table__row-body pro-virtual-table__row-body--level-1']",
                ".//div[contains(@class, 'pro-virtual-table__row-body')]"
            ]

            product_rows = []
            for selector in row_selectors:
                try:
                    rows = table_container.query_selector_all(f"xpath={selector}")
                    if rows:
                        product_rows = rows
                        self.logger.info(f"✅ 找到 {len(product_rows)} 个产品行")
                        break
                except Exception as e:
                    continue

            if not product_rows:
                self.logger.error("❌ 无法找到产品行")
                return False

            # 检查每个产品行
            adjusted_count = 0
            for i, row in enumerate(product_rows):
                try:
                    self.logger.info(f"🔍 检查第 {i+1} 个产品...")

                    # 获取产品信息
                    product_info = self._extract_product_info(row, i+1)
                    if not product_info:
                        continue

                    # 检查是否需要调整
                    if self._need_adjustment(product_info):
                        self.logger.info(f"⚠️ 产品 {i+1} 需要调整限购量")
                        if self._adjust_product_limits(row, product_info, i+1):
                            adjusted_count += 1
                        else:
                            self.logger.warning(f"❌ 产品 {i+1} 调整失败")
                    else:
                        self.logger.info(f"✅ 产品 {i+1} 库存充足，无需调整")

                except Exception as e:
                    self.logger.warning(f"检查产品 {i+1} 失败: {e}")
                    continue

            self.logger.info(f"🎉 库存检查完成！共调整了 {adjusted_count} 个产品的限购量")

            # 保存检查完成后的页面状态
            self.logger.info("🔍 保存检查完成后的页面状态...")
            self.save_page_debug_info(product_page, "after_stock_check")

            return True

        except Exception as e:
            self.logger.error(f"检查库存和限购量失败: {e}")
            return False

    def _extract_product_info(self, row, row_index) -> dict:
        """从产品行中提取库存和限购信息"""
        try:
            product_info = {
                'row_index': row_index,
                'stock': 0,
                'total_limit': 0,
                'user_limit': 0,
                'stock_element': None,
                'total_limit_element': None,
                'user_limit_element': None
            }

            # 查找所有单元格
            cells = row.query_selector_all("xpath=.//div[contains(@class, 'pro-virtual-table__row-cell')]")
            self.logger.info(f"  产品 {row_index}: 找到 {len(cells)} 个单元格")

            for cell in cells:
                try:
                    cell_text = cell.inner_text().strip()

                    # 检查是否是库存列（通常是纯数字）
                    if cell_text.isdigit() and int(cell_text) > 0:
                        # 进一步确认是库存列（查找span元素）
                        stock_spans = cell.query_selector_all("xpath=.//span")
                        if stock_spans and len(stock_spans) == 1:
                            stock_text = stock_spans[0].inner_text().strip()
                            if stock_text.isdigit():
                                product_info['stock'] = int(stock_text)
                                self.logger.info(f"    找到库存: {product_info['stock']}")
                                continue

                    # 检查是否是限购总量列（包含输入框，max属性较大）
                    total_limit_inputs = cell.query_selector_all("xpath=.//input[@max='1000']")
                    if total_limit_inputs:
                        input_element = total_limit_inputs[0]
                        current_value = input_element.input_value() or "0"
                        try:
                            product_info['total_limit'] = int(current_value) if current_value.isdigit() else 0
                            product_info['total_limit_element'] = input_element
                            self.logger.info(f"    找到限购总量: {product_info['total_limit']}")
                        except:
                            pass
                        continue

                    # 检查是否是单用户限购量列（包含输入框，max属性较小）
                    user_limit_inputs = cell.query_selector_all("xpath=.//input[@max='99']")
                    if user_limit_inputs:
                        input_element = user_limit_inputs[0]
                        current_value = input_element.input_value() or "0"
                        try:
                            product_info['user_limit'] = int(current_value) if current_value.isdigit() else 0
                            product_info['user_limit_element'] = input_element
                            self.logger.info(f"    找到单用户限购量: {product_info['user_limit']}")
                        except:
                            pass
                        continue

                except Exception as e:
                    continue

            # 验证是否获取到了必要信息
            if product_info['stock'] > 0:
                self.logger.info(f"  产品 {row_index} 信息: 库存={product_info['stock']}, 限购总量={product_info['total_limit']}, 单用户限购量={product_info['user_limit']}")
                return product_info
            else:
                self.logger.warning(f"  产品 {row_index}: 未能获取到有效的库存信息")
                return None

        except Exception as e:
            self.logger.warning(f"提取产品 {row_index} 信息失败: {e}")
            return None

    def _need_adjustment(self, product_info) -> bool:
        """检查是否需要调整限购量"""
        stock = product_info['stock']
        total_limit = product_info['total_limit']
        user_limit = product_info['user_limit']

        # 检查限购总量是否超过库存
        total_limit_exceeds = total_limit > stock if total_limit > 0 else False

        # 检查单用户限购量是否超过库存
        user_limit_exceeds = user_limit > stock if user_limit > 0 else False

        if total_limit_exceeds or user_limit_exceeds:
            self.logger.warning(f"  产品 {product_info['row_index']}: 库存={stock}, 限购总量={total_limit}, 单用户限购量={user_limit}")
            if total_limit_exceeds:
                self.logger.warning(f"    ⚠️ 限购总量({total_limit})超过库存({stock})")
            if user_limit_exceeds:
                self.logger.warning(f"    ⚠️ 单用户限购量({user_limit})超过库存({stock})")
            return True

        return False

    def _adjust_product_limits(self, row, product_info, row_index) -> bool:
        """调整产品的限购量以匹配库存"""
        try:
            stock = product_info['stock']
            total_limit = product_info['total_limit']
            user_limit = product_info['user_limit']
            adjusted = False

            self.logger.info(f"🔧 开始调整产品 {row_index} 的限购量...")

            # 调整限购总量
            if total_limit > stock and product_info['total_limit_element']:
                try:
                    self.logger.info(f"  📝 调整限购总量: {total_limit} -> {stock}")

                    element = product_info['total_limit_element']

                    # 滚动到元素位置
                    element.scroll_into_view_if_needed()
                    time.sleep(1)

                    # 点击输入框获得焦点
                    element.click()
                    time.sleep(0.5)

                    # 清空并输入新值
                    element.fill("")
                    time.sleep(0.5)
                    element.fill(str(stock))
                    time.sleep(1)

                    # 验证输入
                    new_value = element.input_value()
                    if new_value == str(stock):
                        self.logger.info(f"  ✅ 限购总量调整成功: {new_value}")
                        adjusted = True
                    else:
                        self.logger.warning(f"  ❌ 限购总量调整失败: 期望={stock}, 实际={new_value}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 调整限购总量失败: {e}")

            # 调整单用户限购量
            if user_limit > stock and product_info['user_limit_element']:
                try:
                    self.logger.info(f"  📝 调整单用户限购量: {user_limit} -> {stock}")

                    element = product_info['user_limit_element']

                    # 滚动到元素位置
                    element.scroll_into_view_if_needed()
                    time.sleep(1)

                    # 点击输入框获得焦点
                    element.click()
                    time.sleep(0.5)

                    # 清空并输入新值
                    element.fill("")
                    time.sleep(0.5)
                    element.fill(str(stock))
                    time.sleep(1)

                    # 验证输入
                    new_value = element.input_value()
                    if new_value == str(stock):
                        self.logger.info(f"  ✅ 单用户限购量调整成功: {new_value}")
                        adjusted = True
                    else:
                        self.logger.warning(f"  ❌ 单用户限购量调整失败: 期望={stock}, 实际={new_value}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 调整单用户限购量失败: {e}")

            if adjusted:
                self.logger.info(f"🎉 产品 {row_index} 限购量调整完成")
                # 随机延迟，模拟真实用户操作
                delay = random.uniform(1.0, 2.0)
                time.sleep(delay)

            return adjusted

        except Exception as e:
            self.logger.error(f"调整产品 {row_index} 限购量失败: {e}")
            return False

    def _handle_submit_and_fix_errors(self, product_page) -> bool:
        """点击提交按钮并处理系统提示的错误"""
        try:
            self.logger.info("🎯 开始点击提交按钮并处理可能的错误...")

            # 先尝试点击提交按钮
            submit_result = self._click_submit_button(product_page)
            if submit_result == "continue":
                self.logger.info("🔄 提交完成，准备处理下一个活动")
                return "continue"
            elif not submit_result:
                return False

            # 等待系统检测并可能显示错误
            time.sleep(3)

            # 检查是否有标红的错误字段
            if self._has_error_fields(product_page):
                self.logger.info("⚠️ 发现标红字段，开始修复...")
                if self._fix_error_fields(product_page):
                    self.logger.info("✅ 错误字段修复完成，等待3秒后重新提交...")
                    time.sleep(3)

                    # 重新点击提交按钮
                    resubmit_result = self._click_submit_button(product_page)
                    if resubmit_result == "continue":
                        self.logger.info("🔄 修复后重新提交完成，准备处理下一个活动")
                        return "continue"
                    elif resubmit_result:
                        self.logger.info("🎉 修复后重新提交成功！")
                        return True
                    else:
                        self.logger.warning("❌ 修复后重新提交失败")
                        return False
                else:
                    self.logger.error("❌ 错误字段修复失败")
                    return False
            else:
                self.logger.info("✅ 没有发现错误，提交成功！")
                return True

        except Exception as e:
            self.logger.error(f"处理提交和错误修复失败: {e}")
            return False

    def _click_submit_button(self, product_page) -> bool:
        """处理提交按钮"""
        try:
            self.logger.info("🎯 开始处理提交按钮...")

            # 等待前面的操作完成
            time.sleep(3)

            # 保存点击提交前的页面状态
            self.logger.info("🔍 保存点击提交前的页面状态...")
            self.save_page_debug_info(product_page, "before_submit_button")

            # 提交按钮的选择器
            submit_button_selectors = [
                # 方法1：通过具体的class组合定位
                #"//button[contains(@class, 'jx-button--primary') and contains(@class, 'jx-button--large') and contains(@class, 'pro-button')]//span[contains(text(), '提交')]",
                # 方法2：通过按钮文本直接定位
                "//button[contains(.//span, '提交')]",
                "//button[contains(text(), '提交')]",
                # 方法3：通过父容器定位
                "//div[@class='product-footer']//button[contains(@class, 'jx-button--primary')]//span[contains(text(), '提交')]",
                "//div[contains(@class, 'product-footer')]//button[contains(@class, 'jx-button--primary')]",
                # 方法4：通过data-v属性定位
                "//div[@data-v-8562a278 and @class='product-footer']//button[contains(@class, 'jx-button--primary')]",
                # 方法5：更宽泛的查找
                "//*[contains(text(), '提交')]",
                "//button[contains(@class, 'jx-button--primary')]",
                # 方法6：通过完整的class匹配
                "//button[@class='jx-button jx-button--primary jx-button--large pro-button']"
            ]

            submit_button_clicked = False
            for i, selector in enumerate(submit_button_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击提交按钮: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    elements = product_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个提交按钮")

                    if elements:
                        # 检查每个按钮的可见性和可点击性
                        for j, element in enumerate(elements):
                            try:
                                is_visible = element.is_visible()
                                is_enabled = element.is_enabled()
                                element_text = element.inner_text().strip()
                                class_name = element.get_attribute("class") or ""
                                aria_disabled = element.get_attribute("aria-disabled") or ""
                                self.logger.info(f"     按钮 {j+1}: 文本='{element_text}', class='{class_name}', aria-disabled='{aria_disabled}', 可见={is_visible}, 启用={is_enabled}")

                                # 检查是否是提交按钮（包含提交文本且是主要按钮样式）
                                is_submit_button = (
                                    ('提交' in element_text or 'jx-button--primary' in class_name) and
                                    is_visible and is_enabled and aria_disabled != "true"
                                )

                                if is_submit_button:
                                    # 滚动到元素位置
                                    element.scroll_into_view_if_needed()

                                    # 随机延迟2-3秒，模拟真实用户操作
                                    delay = random.uniform(2.0, 3.0)
                                    self.logger.info(f"     ⏳ 随机延迟 {delay:.1f}秒...")
                                    time.sleep(delay)

                                    # 尝试多种点击方式
                                    try:
                                        # 方式1：普通点击
                                        element.click()
                                        self.logger.info(f"  ✅ 成功点击提交按钮: 方法{i+1}, 按钮{j+1} (普通点击)")
                                    except Exception as e1:
                                        try:
                                            # 方式2：强制点击
                                            element.click(force=True)
                                            self.logger.info(f"  ✅ 成功点击提交按钮: 方法{i+1}, 按钮{j+1} (强制点击)")
                                        except Exception as e2:
                                            try:
                                                # 方式3：JavaScript点击
                                                element.evaluate("el => el.click()")
                                                self.logger.info(f"  ✅ 成功点击提交按钮: 方法{i+1}, 按钮{j+1} (JS点击)")
                                            except Exception as e3:
                                                self.logger.warning(f"     所有点击方式都失败: 普通={e1}, 强制={e2}, JS={e3}")
                                                continue

                                    # 等待提交操作完成
                                    time.sleep(5)

                                    # 保存点击提交后的页面状态
                                    self.logger.info("🔍 保存点击提交后的页面状态...")
                                    self.save_page_debug_info(product_page, "after_submit_button_clicked")

                                    submit_button_clicked = True
                                    break
                                else:
                                    self.logger.info(f"     跳过按钮 {j+1}: 不是提交按钮或不可点击")

                            except Exception as e:
                                self.logger.warning(f"     检查按钮 {j+1} 失败: {e}")
                                continue

                        if submit_button_clicked:
                            break
                    else:
                        self.logger.warning(f"     未找到提交按钮: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击提交按钮失败: 方法{i+1} - {e}")
                    continue

            if not submit_button_clicked:
                self.logger.error("❌ 无法点击提交按钮")

                # 保存失败时的页面状态用于调试
                self.logger.info("🔍 保存点击提交失败时的页面状态...")
                self.save_page_debug_info(product_page, "submit_button_click_failed")

                return False

            # 等待提交完成，监控进度对话框
            self.logger.info("⏳ 等待提交完成...")
            time.sleep(3)

            # 监控提交进度对话框
            self.logger.info("🔍 监控提交进度对话框...")
            if self._monitor_submit_progress(product_page):
                self.logger.info("✅ 提交进度完成！")

                # 返回到限时秒杀主页面继续处理下一个活动
                self.logger.info("🔄 返回限时秒杀主页面，准备处理下一个未开始的活动...")
                if self._return_to_flash_sale_main_page():
                    self.logger.info("✅ 已返回主页面，可以继续处理下一个活动")
                    return "continue"  # 返回特殊值表示需要继续处理
                else:
                    self.logger.warning("⚠️ 返回主页面失败")
                    return True
            else:
                self.logger.warning("⚠️ 提交进度监控异常，继续执行...")

            # 保存提交完成后的最终状态
            self.logger.info("🔍 保存提交完成后的最终状态...")
            self.save_page_debug_info(product_page, "submit_completed")

            self.logger.info("🎉 提交按钮点击完成！整个流程执行完毕！")
            return True

        except Exception as e:
            self.logger.error(f"处理提交按钮失败: {e}")
            return False

    def _monitor_submit_progress(self, product_page) -> bool:
        """监控提交进度对话框"""
        try:
            self.logger.info("🔍 开始监控提交进度对话框...")

            # 等待进度对话框出现
            progress_dialog_found = False
            for attempt in range(10):  # 最多等待10次，每次3秒
                try:
                    # 查找进度对话框
                    dialog_selectors = [
                        "//div[contains(@class, 'flash-sale-batch-operate-progress-dialog')]",
                        "//div[contains(@class, 'jx-dialog') and contains(.//span, '提示')]",
                        "//div[@class='jx-dialog pro-dialog pro-dialog-footer-align-right  has-header batch-delete-dialog flash-sale-batch-operate-progress-dialog']"
                    ]

                    for selector in dialog_selectors:
                        dialogs = product_page.query_selector_all(f"xpath={selector}")
                        if dialogs:
                            for dialog in dialogs:
                                if dialog.is_visible():
                                    self.logger.info(f"✅ 找到进度对话框 (尝试 {attempt + 1}/10)")
                                    progress_dialog_found = True
                                    break
                        if progress_dialog_found:
                            break

                    if progress_dialog_found:
                        break

                    self.logger.info(f"⏳ 等待进度对话框出现... (尝试 {attempt + 1}/10)")
                    time.sleep(3)

                except Exception as e:
                    self.logger.warning(f"查找进度对话框失败 (尝试 {attempt + 1}): {e}")
                    time.sleep(3)
                    continue

            if not progress_dialog_found:
                self.logger.warning("⚠️ 未找到进度对话框，可能已经完成或出现异常")
                return False

            # 监控进度条
            self.logger.info("📊 开始监控进度条...")
            max_wait_minutes = 30  # 最多等待30分钟
            check_interval = 30    # 每30秒检查一次
            max_checks = (max_wait_minutes * 60) // check_interval

            for check_count in range(max_checks):
                try:
                    # 查找进度条
                    progress_selectors = [
                        "//div[contains(@class, 'jx-progress-bar__inner')]//span",
                        "//div[contains(@class, 'jx-progress-bar__innerText')]//span",
                        "//span[contains(text(), '%')]"
                    ]

                    progress_found = False
                    current_progress = "0%"

                    for selector in progress_selectors:
                        try:
                            progress_elements = product_page.query_selector_all(f"xpath={selector}")
                            for element in progress_elements:
                                if element.is_visible():
                                    text = element.inner_text().strip()
                                    if '%' in text:
                                        current_progress = text
                                        progress_found = True
                                        break
                            if progress_found:
                                break
                        except Exception as e:
                            continue

                    if progress_found:
                        self.logger.info(f"📊 当前进度: {current_progress} (检查 {check_count + 1}/{max_checks})")

                        # 检查是否达到100%
                        if "100%" in current_progress:
                            self.logger.info("🎉 进度达到100%！")

                            # 等待一下确保完成
                            time.sleep(5)

                            # 尝试关闭进度对话框
                            self._close_progress_dialog(product_page)

                            return True
                    else:
                        self.logger.warning(f"⚠️ 未找到进度信息 (检查 {check_count + 1}/{max_checks})")

                    # 等待下次检查
                    if check_count < max_checks - 1:  # 不是最后一次检查
                        time.sleep(check_interval)

                except Exception as e:
                    self.logger.warning(f"检查进度失败 (检查 {check_count + 1}): {e}")
                    time.sleep(check_interval)
                    continue

            self.logger.warning(f"⚠️ 进度监控超时 ({max_wait_minutes}分钟)")
            return False

        except Exception as e:
            self.logger.error(f"监控提交进度失败: {e}")
            return False

    def _close_progress_dialog(self, product_page) -> bool:
        """关闭进度对话框"""
        try:
            self.logger.info("🔍 尝试关闭进度对话框...")

            # 查找关闭按钮
            close_selectors = [
                "//button[contains(.//span, '确认')]",
                "//button[contains(.//span, '关闭')]",
                "//button[contains(@class, 'jx-dialog__headerbtn')]",
                "//button[contains(@aria-label, '关闭')]"
            ]

            for selector in close_selectors:
                try:
                    buttons = product_page.query_selector_all(f"xpath={selector}")
                    for button in buttons:
                        if button.is_visible():
                            button.click()
                            self.logger.info(f"✅ 成功点击关闭按钮: {selector}")
                            time.sleep(2)
                            return True
                except Exception as e:
                    continue

            # 如果没有找到关闭按钮，尝试按ESC键
            try:
                product_page.keyboard.press("Escape")
                self.logger.info("✅ 使用ESC键关闭对话框")
                time.sleep(2)
                return True
            except Exception as e:
                self.logger.warning(f"ESC键关闭失败: {e}")

            return False

        except Exception as e:
            self.logger.error(f"关闭进度对话框失败: {e}")
            return False

    def _return_to_flash_sale_main_page(self) -> bool:
        """返回到限时秒杀主页面"""
        try:
            self.logger.info("🔄 准备返回限时秒杀主页面...")

            # 获取所有标签页
            pages = self.browser_context.pages
            self.logger.info(f"当前打开的标签页数量: {len(pages)}")

            # 查找限时秒杀主页面标签页
            flash_sale_page = None
            for i, page in enumerate(pages):
                try:
                    page_url = page.url
                    self.logger.info(f"检查标签页 {i + 1}: {page_url}")

                    # 检查是否是限时秒杀主页面
                    if "flashSale" in page_url or "限时秒杀" in page.title():
                        flash_sale_page = page
                        self.logger.info(f"✅ 找到限时秒杀主页面: 标签页 {i + 1}")
                        break
                except Exception as e:
                    self.logger.warning(f"检查标签页 {i + 1} 失败: {e}")
                    continue

            # 如果找到了限时秒杀主页面
            if flash_sale_page:
                # 关闭其他标签页
                for i, page in enumerate(pages):
                    if page != flash_sale_page:
                        try:
                            page_url = page.url
                            self.logger.info(f"关闭标签页 {i + 1}: {page_url}")
                            page.close()
                        except Exception as e:
                            self.logger.warning(f"关闭标签页 {i + 1} 失败: {e}")

                # 切换到限时秒杀主页面
                try:
                    flash_sale_page.bring_to_front()
                    self.logger.info("✅ 已切换到限时秒杀主页面")

                    # 等待页面稳定
                    time.sleep(3)

                    # 刷新页面以获取最新状态
                    flash_sale_page.reload()
                    self.logger.info("🔄 刷新页面以获取最新状态...")
                    time.sleep(5)

                    return True
                except Exception as e:
                    self.logger.error(f"切换到限时秒杀主页面失败: {e}")
            else:
                self.logger.warning("⚠️ 未找到限时秒杀主页面，尝试导航到主页面...")

                # 如果没有找到，尝试在第一个标签页中导航到限时秒杀页面
                if pages:
                    try:
                        first_page = pages[0]
                        first_page.bring_to_front()

                        # 导航到限时秒杀页面
                        flash_sale_url = "https://erp.3.miaoshou.com/mkt/marketing/flashSale"
                        first_page.goto(flash_sale_url)
                        self.logger.info(f"✅ 已导航到限时秒杀页面: {flash_sale_url}")

                        time.sleep(5)
                        return True
                    except Exception as e:
                        self.logger.error(f"导航到限时秒杀页面失败: {e}")

            return False

        except Exception as e:
            self.logger.error(f"返回限时秒杀主页面失败: {e}")
            return False

    def _has_error_fields(self, product_page) -> bool:
        """检查页面是否有标红的错误字段"""
        try:
            self.logger.info("🔍 检查是否有标红的错误字段...")

            # 保存检查前的页面状态
            self.save_page_debug_info(product_page, "checking_error_fields")

            # 查找标红的输入框（通常有特定的错误样式类）
            error_selectors = [
                # 方法1：查找包含错误文本的元素
                "//*[contains(text(), '库存数量不足')]",
                "//*[contains(text(), '不能大于库存')]",
                "//*[contains(text(), '超过库存')]",
                # 方法2：查找红色边框或错误样式的输入框
                "//input[contains(@class, 'error')]",
                "//input[contains(@class, 'is-error')]",
                "//div[contains(@class, 'error')]//input",
                "//div[contains(@class, 'is-error')]//input",
                # 方法3：查找红色文本提示
                "//*[contains(@style, 'color: red')]",
                "//*[contains(@style, 'color:#ff')]",
                "//*[contains(@class, 'text-color-danger')]",
                "//*[contains(@class, 'error-text')]"
            ]

            has_errors = False
            for i, selector in enumerate(error_selectors):
                try:
                    elements = product_page.query_selector_all(f"xpath={selector}")
                    if elements:
                        visible_errors = [el for el in elements if el.is_visible()]
                        if visible_errors:
                            self.logger.info(f"  ⚠️ 发现 {len(visible_errors)} 个错误字段 (方法{i+1})")
                            for j, error_el in enumerate(visible_errors):
                                try:
                                    error_text = error_el.inner_text().strip()
                                    self.logger.info(f"    错误 {j+1}: {error_text}")
                                except:
                                    pass
                            has_errors = True
                            break
                except Exception as e:
                    continue

            if has_errors:
                self.logger.warning("⚠️ 发现标红错误字段，需要修复")
            else:
                self.logger.info("✅ 未发现错误字段")

            return has_errors

        except Exception as e:
            self.logger.error(f"检查错误字段失败: {e}")
            return False

    def _fix_error_fields(self, product_page) -> bool:
        """修复标红的错误字段"""
        try:
            self.logger.info("🔧 开始修复标红的错误字段...")

            # 首先统计页面上的错误情况
            all_error_elements = product_page.query_selector_all("div.is-error")
            stock_error_count = 0
            error_details = []

            for error_elem in all_error_elements:
                try:
                    error_text = error_elem.inner_text().strip()
                    error_details.append(error_text)
                    if "限购总量不能大于库存" in error_text:
                        stock_error_count += 1
                except:
                    continue

            self.logger.info(f"📊 错误统计:")
            self.logger.info(f"  - 总错误元素: {len(all_error_elements)} 个")
            self.logger.info(f"  - 库存相关错误: {stock_error_count} 个")

            if stock_error_count == 0:
                self.logger.info("✅ 没有发现需要修复的库存错误")
                return True

            # 查找虚拟表格容器
            table_container = self._find_table_container(product_page)
            if not table_container:
                return False

            # 查找所有产品行
            product_rows = self._find_product_rows(table_container)
            if not product_rows:
                return False

            self.logger.info(f"📋 找到 {len(product_rows)} 个产品行，开始逐行检查和修复...")

            fixed_count = 0
            processed_count = 0

            for i, row in enumerate(product_rows):
                try:
                    processed_count += 1

                    # 根据HTML结构精确检查：查找包含is-error类的单元格
                    error_cell = row.query_selector("div.pro-virtual-table__row-cell.is-error")

                    if error_cell:
                        # 检查错误单元格中是否包含"限购总量不能大于库存"错误文本
                        error_text_element = error_cell.query_selector("div.pro-virtual-table__row-cell--error")
                        if error_text_element:
                            error_text = error_text_element.inner_text().strip()
                            if "限购总量不能大于库存" in error_text:
                                self.logger.info(f"🎯 产品 {i+1}: 发现库存错误 - {error_text}")

                                # 修复该行的错误
                                if self._set_product_limits_to_zero(row, i+1):
                                    fixed_count += 1
                                    self.logger.info(f"  ✅ 产品 {i+1}: 库存错误修复成功 ({fixed_count}/{stock_error_count})")
                                else:
                                    self.logger.warning(f"  ❌ 产品 {i+1}: 库存错误修复失败")
                            else:
                                self.logger.info(f"  产品 {i+1}: 发现其他类型错误 - {error_text}")
                        else:
                            self.logger.info(f"  产品 {i+1}: 发现is-error单元格但无错误文本")
                    else:
                        self.logger.debug(f"  产品 {i+1}: 无错误")

                except Exception as e:
                    self.logger.warning(f"检查产品 {i+1} 失败: {e}")
                    continue

            self.logger.info(f"🎉 错误修复完成:")
            self.logger.info(f"  - 处理了 {processed_count} 个产品行")
            self.logger.info(f"  - 需要修复 {stock_error_count} 个库存错误")
            self.logger.info(f"  - 成功修复 {fixed_count} 个库存错误")

            # 保存修复完成后的页面状态
            self.save_page_debug_info(product_page, "after_error_fix")

            if fixed_count > 0:
                # 等待修改生效
                self.logger.info("⏳ 等待修改生效...")
                time.sleep(3)

                # 再次检查是否还有错误
                remaining_errors = product_page.query_selector_all("div.is-error")
                remaining_stock_errors = 0
                for error_elem in remaining_errors:
                    try:
                        if "限购总量不能大于库存" in error_elem.inner_text():
                            remaining_stock_errors += 1
                    except:
                        continue

                self.logger.info(f"🔍 修复后检查: 还剩余 {remaining_stock_errors} 个库存错误")
                return remaining_stock_errors == 0
            else:
                self.logger.warning("❌ 没有成功修复任何错误")
                return False

        except Exception as e:
            self.logger.error(f"修复错误字段失败: {e}")
            return False

    def _find_table_container(self, product_page):
        """查找表格容器"""
        table_container_selectors = [
            "//div[@class='vue-recycle-scroller ready page-mode direction-vertical pro-virtual-table__body pro-virtual-scroll pro-virtual-table__body']",
            "//div[contains(@class, 'pro-virtual-table__body') and contains(@class, 'vue-recycle-scroller')]",
            "//div[contains(@class, 'pro-virtual-table__body')]"
        ]

        for selector in table_container_selectors:
            try:
                elements = product_page.query_selector_all(f"xpath={selector}")
                if elements:
                    for element in elements:
                        if element.is_visible():
                            self.logger.info(f"✅ 找到表格容器")
                            return element
            except Exception:
                continue

        self.logger.error("❌ 无法找到表格容器")
        return None

    def _find_product_rows(self, table_container):
        """查找产品行（包括SKU行）"""
        # 查找所有行，包括主产品行和SKU子行
        row_selectors = [
            ".//div[contains(@class, 'pro-virtual-table__row-body')]",  # 所有行
            ".//div[@class='pro-virtual-table__row-body pro-virtual-table__row-body--level-0']",  # 主产品行
            ".//div[@class='pro-virtual-table__row-body pro-virtual-table__row-body--level-1']"   # SKU子行
        ]

        all_rows = []
        for selector in row_selectors:
            try:
                rows = table_container.query_selector_all(f"xpath={selector}")
                if rows:
                    for row in rows:
                        if row not in all_rows:  # 避免重复
                            all_rows.append(row)
            except Exception:
                continue

        if all_rows:
            self.logger.info(f"✅ 找到 {len(all_rows)} 个产品行（包括SKU行）")
            return all_rows

        self.logger.error("❌ 无法找到产品行")
        return None

    def _row_has_errors(self, row) -> bool:
        """检查产品行是否有错误"""
        try:
            # 首先检查是否包含错误文本
            error_text_selectors = [
                ".//*[contains(text(), '限购总量不能大于库存')]",
                ".//*[contains(text(), '库存数量不足')]",
                ".//*[contains(text(), '不能大于库存')]"
            ]

            for selector in error_text_selectors:
                try:
                    elements = row.query_selector_all(f"xpath={selector}")
                    if elements:
                        visible_errors = [el for el in elements if el.is_visible()]
                        if visible_errors:
                            self.logger.info(f"    找到错误文本: {visible_errors[0].inner_text().strip()}")
                            return True
                except Exception:
                    continue

            # 然后检查是否有错误样式
            error_style_selectors = [
                ".//input[contains(@class, 'error')]",
                ".//input[contains(@class, 'is-error')]",
                ".//*[contains(@class, 'error')]",
                ".//*[contains(@class, 'text-color-danger')]",
                ".//*[contains(@style, 'color: red')]"
            ]

            for selector in error_style_selectors:
                try:
                    elements = row.query_selector_all(f"xpath={selector}")
                    if elements:
                        visible_errors = [el for el in elements if el.is_visible()]
                        if visible_errors:
                            self.logger.info(f"    找到错误样式元素")
                            return True
                except Exception:
                    continue

            return False

        except Exception as e:
            self.logger.warning(f"检查行错误失败: {e}")
            return False

    def _fix_row_errors(self, row, row_index) -> bool:
        """修复单行的错误"""
        try:
            self.logger.info(f"🔧 修复产品 {row_index} 的错误...")

            # 检查是否有错误提示
            error_elements = row.query_selector_all("xpath=.//div[contains(text(), '限购总量不能大于库存')]")
            if not error_elements:
                self.logger.info(f"  产品 {row_index}: 没有错误提示，跳过")
                return True

            self.logger.info(f"  产品 {row_index}: 发现'限购总量不能大于库存'错误提示")

            # 新的解决方案：将限购总量和单用户限购量都设置为0
            # 这样可以避免库存验证错误，让产品能够正常提交
            self.logger.info(f"  产品 {row_index}: 将限购总量和单用户限购量设置为0来解决问题")
            return self._set_product_limits_to_zero(row, row_index)

        except Exception as e:
            self.logger.error(f"修复产品 {row_index} 错误失败: {e}")
            return False

    def _set_product_limits_to_zero(self, row, row_index) -> bool:
        """将产品的限购总量和单用户限购量都设置为0"""
        try:
            self.logger.info(f"  🔧 产品 {row_index}: 开始设置限购量为0...")

            success_count = 0

            # 根据提供的HTML结构，精确定位错误单元格和输入框
            self.logger.info(f"    🎯 使用精确选择器定位错误单元格...")

            # 1. 精确查找包含is-error类的单元格
            error_cell = row.query_selector("div.pro-virtual-table__row-cell.is-error")
            if not error_cell:
                self.logger.warning(f"      未找到is-error单元格")
                return False

            self.logger.info(f"      ✅ 找到is-error单元格")

            # 2. 在错误单元格中查找限购总量输入框 (max="0")
            purchase_limit_input = error_cell.query_selector("input.jx-input__inner[max='0'][type='text']")
            if purchase_limit_input:
                try:
                    current_value = purchase_limit_input.get_attribute("value") or purchase_limit_input.input_value()
                    self.logger.info(f"        限购总量输入框: 当前值='{current_value}'")

                    # 使用重试机制处理DOM分离问题
                    for retry in range(3):
                        try:
                            # 清空并设置为0
                            purchase_limit_input.fill("")
                            time.sleep(0.2)
                            purchase_limit_input.fill("0")
                            time.sleep(0.2)

                            # 触发事件
                            purchase_limit_input.dispatch_event("input")
                            purchase_limit_input.dispatch_event("change")
                            purchase_limit_input.dispatch_event("blur")
                            time.sleep(0.3)
                            break  # 成功则跳出重试循环
                        except Exception as retry_e:
                            if "not attached to the DOM" in str(retry_e) and retry < 2:
                                self.logger.info(f"        重试 {retry+1}/3: 元素已分离，重新查找...")
                                # 重新查找元素
                                purchase_limit_input = error_cell.query_selector("input.jx-input__inner[max='0'][type='text']")
                                if not purchase_limit_input:
                                    break
                                time.sleep(0.5)
                            else:
                                raise retry_e

                    # 验证设置
                    new_value = purchase_limit_input.get_attribute("value") or purchase_limit_input.input_value()
                    self.logger.info(f"        限购总量输入框: 设置后值='{new_value}'")

                    if new_value == "0":
                        self.logger.info(f"        ✅ 限购总量设置为0成功")
                        success_count += 1
                    else:
                        self.logger.warning(f"        ❌ 限购总量设置失败，值为'{new_value}'")

                except Exception as e:
                    self.logger.warning(f"        设置限购总量失败: {e}")
            else:
                self.logger.warning(f"        未找到限购总量输入框 (max='0')")

            # 3. 查找下一个单元格中的单用户限购量输入框 (max="99")
            # 根据HTML结构，单用户限购量在错误单元格的下一个单元格中
            next_cell = error_cell.query_selector("xpath=following-sibling::div[contains(@class, 'pro-virtual-table__row-cell')][1]")
            if next_cell:
                user_limit_input = next_cell.query_selector("input.jx-input__inner[max='99'][type='text']")
                if user_limit_input:
                    try:
                        current_value = user_limit_input.get_attribute("value") or user_limit_input.input_value()
                        self.logger.info(f"        单用户限购量输入框: 当前值='{current_value}'")

                        # 使用重试机制处理DOM分离问题
                        for retry in range(3):
                            try:
                                # 清空并设置为0
                                user_limit_input.fill("")
                                time.sleep(0.2)
                                user_limit_input.fill("0")
                                time.sleep(0.2)

                                # 触发事件
                                user_limit_input.dispatch_event("input")
                                user_limit_input.dispatch_event("change")
                                user_limit_input.dispatch_event("blur")
                                time.sleep(0.3)
                                break  # 成功则跳出重试循环
                            except Exception as retry_e:
                                if "not attached to the DOM" in str(retry_e) and retry < 2:
                                    self.logger.info(f"        重试 {retry+1}/3: 元素已分离，重新查找...")
                                    # 重新查找元素
                                    user_limit_input = next_cell.query_selector("input.jx-input__inner[max='99'][type='text']")
                                    if not user_limit_input:
                                        break
                                    time.sleep(0.5)
                                else:
                                    raise retry_e

                        # 验证设置
                        new_value = user_limit_input.get_attribute("value") or user_limit_input.input_value()
                        self.logger.info(f"        单用户限购量输入框: 设置后值='{new_value}'")

                        if new_value == "0":
                            self.logger.info(f"        ✅ 单用户限购量设置为0成功")
                            success_count += 1
                        else:
                            self.logger.warning(f"        ❌ 单用户限购量设置失败，值为'{new_value}'")

                    except Exception as e:
                        self.logger.warning(f"        设置单用户限购量失败: {e}")
                else:
                    self.logger.warning(f"        未找到单用户限购量输入框 (max='99')")
            else:
                self.logger.warning(f"        未找到下一个单元格")

            # 4. 兜底方案：在整行中查找所有限购相关输入框
            if success_count == 0:
                self.logger.info(f"    🔄 兜底方案: 在整行中查找限购输入框...")

                # 查找max="0"的输入框（限购总量）
                purchase_inputs = row.query_selector_all("input.jx-input__inner[max='0'][type='text']")
                self.logger.info(f"        找到 {len(purchase_inputs)} 个限购总量输入框 (max='0')")

                for i, inp in enumerate(purchase_inputs):
                    try:
                        current_value = inp.get_attribute("value") or inp.input_value()
                        self.logger.info(f"          限购总量输入框{i+1}: 当前值='{current_value}'")

                        # 使用重试机制
                        for retry in range(2):
                            try:
                                inp.fill("")
                                time.sleep(0.2)
                                inp.fill("0")
                                time.sleep(0.2)
                                inp.dispatch_event("input")
                                inp.dispatch_event("change")
                                inp.dispatch_event("blur")
                                time.sleep(0.3)
                                break
                            except Exception as retry_e:
                                if "not attached to the DOM" in str(retry_e) and retry < 1:
                                    self.logger.info(f"            重试: 元素已分离")
                                    time.sleep(0.5)
                                else:
                                    raise retry_e

                        new_value = inp.get_attribute("value") or inp.input_value()
                        if new_value == "0":
                            self.logger.info(f"          ✅ 限购总量输入框{i+1}设置成功")
                            success_count += 1

                    except Exception as e:
                        self.logger.warning(f"          设置限购总量输入框{i+1}失败: {e}")
                        continue

                # 查找max="99"的输入框（单用户限购量）
                user_inputs = row.query_selector_all("input.jx-input__inner[max='99'][type='text']")
                self.logger.info(f"        找到 {len(user_inputs)} 个单用户限购量输入框 (max='99')")

                for i, inp in enumerate(user_inputs):
                    try:
                        current_value = inp.get_attribute("value") or inp.input_value()
                        self.logger.info(f"          单用户限购量输入框{i+1}: 当前值='{current_value}'")

                        # 使用重试机制
                        for retry in range(2):
                            try:
                                inp.fill("")
                                time.sleep(0.2)
                                inp.fill("0")
                                time.sleep(0.2)
                                inp.dispatch_event("input")
                                inp.dispatch_event("change")
                                inp.dispatch_event("blur")
                                time.sleep(0.3)
                                break
                            except Exception as retry_e:
                                if "not attached to the DOM" in str(retry_e) and retry < 1:
                                    self.logger.info(f"            重试: 元素已分离")
                                    time.sleep(0.5)
                                else:
                                    raise retry_e

                        new_value = inp.get_attribute("value") or inp.input_value()
                        if new_value == "0":
                            self.logger.info(f"          ✅ 单用户限购量输入框{i+1}设置成功")
                            success_count += 1

                    except Exception as e:
                        self.logger.warning(f"          设置单用户限购量输入框{i+1}失败: {e}")
                        continue

            # 返回结果
            if success_count >= 1:
                self.logger.info(f"  ✅ 产品 {row_index}: 成功设置 {success_count} 个限购量为0")
                return True
            else:
                self.logger.warning(f"  ❌ 产品 {row_index}: 未能设置任何限购量")
                return False

        except Exception as e:
            self.logger.error(f"设置产品 {row_index} 限购量为0失败: {e}")
            return False

    def _turn_off_activity_switch(self, row, row_index) -> bool:
        """关闭参加活动的开关"""
        try:
            self.logger.info(f"  🔧 产品 {row_index}: 查找参加活动开关...")

            # 直接查找开关输入框 - 根据你提供的HTML结构
            switch_selectors = [
                ".//input[@type='checkbox' and @role='switch' and @aria-checked='true']",  # 精确匹配
                ".//input[@class='jx-switch__input' and @aria-checked='true']",           # 根据class匹配
                ".//div[contains(@class, 'jx-switch') and contains(@class, 'is-checked')]//input[@type='checkbox']"  # 通过父元素查找
            ]

            for i, selector in enumerate(switch_selectors):
                try:
                    switches = row.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"    选择器{i+1} '{selector}': 找到 {len(switches)} 个开关")

                    for j, switch_element in enumerate(switches):
                        try:
                            # 获取当前状态
                            aria_checked = switch_element.get_attribute("aria-checked")
                            self.logger.info(f"      开关{j+1}: aria-checked = {aria_checked}")

                            if aria_checked == "true":
                                self.logger.info(f"  📝 产品 {row_index}: 找到开启的开关，准备点击关闭")

                                # 先滚动到开关位置，确保可见
                                switch_element.scroll_into_view_if_needed()
                                time.sleep(1)

                                # 尝试多种点击方式
                                clicked = False

                                # 方法1: 直接点击input元素
                                try:
                                    self.logger.info(f"      尝试直接点击input元素...")
                                    switch_element.click(timeout=5000)
                                    clicked = True
                                    self.logger.info(f"      ✅ 直接点击成功")
                                except Exception as e:
                                    self.logger.warning(f"      直接点击失败: {e}")

                                # 方法2: 点击父级开关容器
                                if not clicked:
                                    try:
                                        self.logger.info(f"      尝试点击父级开关容器...")
                                        switch_container = switch_element.query_selector("xpath=..")  # 父元素
                                        if switch_container:
                                            switch_container.click(timeout=5000)
                                            clicked = True
                                            self.logger.info(f"      ✅ 点击父容器成功")
                                    except Exception as e:
                                        self.logger.warning(f"      点击父容器失败: {e}")

                                # 方法3: 强制点击
                                if not clicked:
                                    try:
                                        self.logger.info(f"      尝试强制点击...")
                                        switch_element.click(force=True, timeout=5000)
                                        clicked = True
                                        self.logger.info(f"      ✅ 强制点击成功")
                                    except Exception as e:
                                        self.logger.warning(f"      强制点击失败: {e}")

                                if clicked:
                                    time.sleep(2)

                                    # 验证开关状态
                                    new_aria_checked = switch_element.get_attribute("aria-checked")
                                    self.logger.info(f"      点击后 aria-checked = {new_aria_checked}")

                                    if new_aria_checked == "false":
                                        self.logger.info(f"  ✅ 产品 {row_index}: 成功关闭参加活动开关")
                                        return True
                                    else:
                                        self.logger.warning(f"  ❌ 产品 {row_index}: 开关状态未改变")
                                else:
                                    self.logger.warning(f"  ❌ 产品 {row_index}: 所有点击方法都失败")
                            else:
                                self.logger.info(f"      开关{j+1}: 已经是关闭状态")
                                return True  # 已经关闭，算成功

                        except Exception as e:
                            self.logger.warning(f"      处理开关{j+1}失败: {e}")
                            continue

                except Exception as e:
                    self.logger.warning(f"  选择器{i+1}失败: {e}")
                    continue

            self.logger.warning(f"  ❌ 产品 {row_index}: 未找到可操作的参加活动开关")
            return False

        except Exception as e:
            self.logger.warning(f"关闭产品 {row_index} 活动开关失败: {e}")
            return False

    def _adjust_purchase_limits(self, row, stock, row_index) -> bool:
        """调整限购量"""
        try:
            # 首先找到包含错误提示的单元格
            error_cells = row.query_selector_all("xpath=.//div[contains(@class, 'pro-virtual-table__row-cell') and contains(@class, 'is-error')]")
            self.logger.info(f"  找到 {len(error_cells)} 个错误单元格")

            fixed = False

            # 方法1: 直接在错误单元格中查找输入框
            for error_cell in error_cells:
                try:
                    inputs_in_error_cell = error_cell.query_selector_all("xpath=.//input[@type='text']")
                    self.logger.info(f"    错误单元格中找到 {len(inputs_in_error_cell)} 个输入框")

                    for input_element in inputs_in_error_cell:
                        if input_element.is_visible():
                            if self._fix_input_value(input_element, stock, "限购总量（错误单元格）"):
                                fixed = True

                except Exception as e:
                    continue

            # 方法2: 查找所有表格单元格，通过位置判断
            all_cells = row.query_selector_all("xpath=.//div[contains(@class, 'pro-virtual-table__row-cell')]")
            self.logger.info(f"  该行共有 {len(all_cells)} 个单元格")

            for i, cell in enumerate(all_cells):
                try:
                    # 查找单元格中的输入框
                    inputs_in_cell = cell.query_selector_all("xpath=.//input[@type='text']")
                    if not inputs_in_cell:
                        continue

                    # 检查单元格是否包含错误信息
                    has_error = len(cell.query_selector_all("xpath=.//*[contains(text(), '限购总量不能大于库存')]")) > 0
                    is_error_cell = "is-error" in (cell.get_attribute("class") or "")

                    for input_element in inputs_in_cell:
                        if not input_element.is_visible():
                            continue

                        max_attr = input_element.get_attribute("max") or ""
                        current_value = input_element.input_value() or "0"

                        # 判断字段类型
                        if max_attr == "99":
                            field_name = f"单用户限购量（单元格{i+1}）"
                        elif has_error or is_error_cell:
                            field_name = f"限购总量（错误单元格{i+1}）"
                        elif i >= 6:  # 通常限购相关字段在第7列之后
                            field_name = f"限购字段（单元格{i+1}，max={max_attr}）"
                        else:
                            continue  # 跳过前面的非限购字段

                        self.logger.info(f"    单元格{i+1}: {field_name}, 当前值={current_value}, max={max_attr}")

                        if self._fix_input_value(input_element, stock, field_name):
                            fixed = True

                except Exception as e:
                    continue

            return fixed

        except Exception as e:
            self.logger.warning(f"调整产品 {row_index} 限购量失败: {e}")
            return False

    def _fix_input_value(self, input_element, stock, field_name) -> bool:
        """修复单个输入框的值"""
        try:
            current_value = input_element.input_value() or "0"
            current_limit = int(current_value) if current_value.isdigit() else 0

            # 计算目标值
            if stock == 0:
                target_value = 0
            elif current_limit > stock:
                target_value = stock
            else:
                # 当前值合理，无需调整
                self.logger.info(f"    ✅ {field_name} 无需调整: {current_limit}")
                return True

            self.logger.info(f"    📝 {field_name}: {current_limit} -> {target_value}")

            # 执行调整
            input_element.scroll_into_view_if_needed()
            time.sleep(0.5)
            input_element.click()
            time.sleep(0.5)
            input_element.fill("")
            time.sleep(0.5)
            input_element.fill(str(target_value))
            time.sleep(1)

            # 验证
            new_value = input_element.input_value()
            if new_value == str(target_value):
                self.logger.info(f"    ✅ {field_name} 调整成功: {target_value}")
                return True
            else:
                self.logger.warning(f"    ❌ {field_name} 调整失败，期望={target_value}, 实际={new_value}")
                return False

        except Exception as e:
            self.logger.warning(f"修复{field_name}失败: {e}")
            return False

    def _get_row_stock(self, row, row_index) -> int:
        """获取产品行的库存数量"""
        try:
            self.logger.info(f"  🔍 开始获取产品 {row_index} 的库存信息...")

            # 查找所有单元格
            cells = row.query_selector_all("xpath=.//div[contains(@class, 'pro-virtual-table__row-cell')]")
            self.logger.info(f"  产品 {row_index}: 找到 {len(cells)} 个单元格")

            # 方法1: 查找库存列（通常在特定位置，包含纯数字）
            for i, cell in enumerate(cells):
                try:
                    cell_text = cell.inner_text().strip()
                    self.logger.info(f"    单元格 {i+1}: '{cell_text}'")

                    # 检查是否是库存列的特征
                    # 1. 纯数字且大于0
                    # 2. 不包含货币符号
                    # 3. 不包含百分号
                    # 4. 不是很大的数字（排除产品ID）
                    if (cell_text.isdigit() and
                        int(cell_text) > 0 and
                        int(cell_text) < 100000 and  # 排除产品ID
                        'THB' not in cell_text and
                        'CNY' not in cell_text and
                        '%' not in cell_text):

                        stock = int(cell_text)
                        self.logger.info(f"  ✅ 产品 {row_index}: 方法1找到库存 = {stock}")
                        return stock

                except Exception as e:
                    continue

            # 方法2: 查找包含span的库存单元格
            for i, cell in enumerate(cells):
                try:
                    spans = cell.query_selector_all("xpath=.//span")
                    for span in spans:
                        span_text = span.inner_text().strip()
                        if (span_text.isdigit() and
                            int(span_text) > 0 and
                            int(span_text) < 100000):
                            stock = int(span_text)
                            self.logger.info(f"  ✅ 产品 {row_index}: 方法2找到库存 = {stock}")
                            return stock
                except Exception as e:
                    continue

            # 方法3: 通过列位置推断（库存通常在固定位置）
            # 根据表格结构，库存列通常在第6列（索引5）
            if len(cells) >= 6:
                try:
                    stock_cell = cells[5]  # 库存列
                    cell_text = stock_cell.inner_text().strip()

                    # 尝试提取数字
                    import re
                    numbers = re.findall(r'\d+', cell_text)
                    for num_str in numbers:
                        num = int(num_str)
                        if 0 < num < 100000:  # 合理的库存范围
                            self.logger.info(f"  ✅ 产品 {row_index}: 方法3找到库存 = {num}")
                            return num

                except Exception as e:
                    pass

            # 方法4: 查找所有数字，选择最可能的库存值
            all_numbers = []
            for cell in cells:
                try:
                    cell_text = cell.inner_text().strip()
                    import re
                    numbers = re.findall(r'\b\d+\b', cell_text)
                    for num_str in numbers:
                        num = int(num_str)
                        if 0 < num < 10000:  # 合理的库存范围
                            all_numbers.append(num)
                except Exception as e:
                    continue

            if all_numbers:
                # 选择最小的正数作为库存（通常库存不会很大）
                stock = min(all_numbers)
                self.logger.info(f"  ✅ 产品 {row_index}: 方法4找到库存 = {stock}")
                return stock

            self.logger.warning(f"  ❌ 产品 {row_index}: 所有方法都未找到库存信息")
            return 0

        except Exception as e:
            self.logger.warning(f"获取产品 {row_index} 库存失败: {e}")
            return 0

    def _fix_total_limit_input(self, row, stock, row_index) -> bool:
        """修复限购总量输入框"""
        try:
            self.logger.info(f"  🔧 开始修复产品 {row_index} 的限购总量...")

            # 优先查找标红的输入框（有错误提示的）
            error_input_selectors = [
                ".//div[contains(text(), '限购总量不能大于库存')]/..//input",
                ".//div[contains(text(), '限购总量不能大于库存')]/following-sibling::*/input",
                ".//div[contains(text(), '限购总量不能大于库存')]/preceding-sibling::*/input",
            ]

            # 先尝试找标红的输入框
            for selector in error_input_selectors:
                try:
                    inputs = row.query_selector_all(f"xpath={selector}")
                    if inputs:
                        self.logger.info(f"    找到标红输入框: {len(inputs)} 个")
                        for input_element in inputs:
                            if input_element.is_visible():
                                return self._adjust_input_value(input_element, stock, row_index, "限购总量")
                except Exception as e:
                    continue

            # 如果没找到标红输入框，使用常规方法
            total_limit_selectors = [
                ".//input[@max='1000']",
                ".//div[contains(@class, 'pro-virtual-table__row-cell')][7]//input",  # 第7列通常是限购总量
                ".//input[@type='text' and not(@max='99')]"  # 排除单用户限购量
            ]

            for selector in total_limit_selectors:
                try:
                    inputs = row.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"    选择器 '{selector}': 找到 {len(inputs)} 个输入框")

                    for input_element in inputs:
                        if input_element.is_visible():
                            return self._adjust_input_value(input_element, stock, row_index, "限购总量")

                except Exception as e:
                    self.logger.warning(f"  选择器 '{selector}' 失败: {e}")
                    continue

            self.logger.warning(f"  ❌ 产品 {row_index}: 未找到限购总量输入框")
            return False

        except Exception as e:
            self.logger.warning(f"修复产品 {row_index} 限购总量失败: {e}")
            return False

    def _adjust_input_value(self, input_element, stock, row_index, field_name) -> bool:
        """调整输入框的值"""
        try:
            current_value = input_element.input_value() or "0"
            current_limit = int(current_value) if current_value.isdigit() else 0

            self.logger.info(f"    {field_name}当前值: {current_value} -> {current_limit}")

            # 检查是否需要调整
            needs_adjustment = False
            target_value = current_limit

            if stock == 0 and current_limit > 0:
                needs_adjustment = True
                target_value = 0
                self.logger.info(f"  📝 产品 {row_index}: 库存为0，{field_name}必须调整 {current_limit} -> 0")
            elif current_limit > stock > 0:
                needs_adjustment = True
                target_value = stock
                self.logger.info(f"  📝 产品 {row_index}: {field_name}超出库存，调整 {current_limit} -> {stock}")

            if needs_adjustment:
                # 滚动到元素位置
                input_element.scroll_into_view_if_needed()
                time.sleep(1)

                # 点击输入框获得焦点
                input_element.click()
                time.sleep(0.5)

                # 清空并输入新值
                input_element.fill("")
                time.sleep(0.5)
                input_element.fill(str(target_value))
                time.sleep(1)

                # 验证输入
                new_value = input_element.input_value()
                if new_value == str(target_value):
                    self.logger.info(f"  ✅ 产品 {row_index}: {field_name}调整成功 -> {target_value}")
                    return True
                else:
                    self.logger.warning(f"  ❌ 产品 {row_index}: {field_name}调整失败，期望={target_value}, 实际={new_value}")
                    return False
            else:
                self.logger.info(f"  ✅ 产品 {row_index}: {field_name}({current_limit})无需调整")
                return True  # 无需调整也算成功

        except Exception as e:
            self.logger.warning(f"调整{field_name}失败: {e}")
            return False

    def _fix_user_limit_input(self, row, stock, row_index) -> bool:
        """修复单用户限购量输入框"""
        try:
            self.logger.info(f"  🔧 开始修复产品 {row_index} 的单用户限购量...")

            # 优先查找标红的输入框（有错误提示的）
            error_input_selectors = [
                ".//div[contains(text(), '限购总量不能大于库存')]/..//input[@max='99']",
                ".//div[contains(text(), '限购总量不能大于库存')]/following-sibling::*/input[@max='99']",
                ".//div[contains(text(), '限购总量不能大于库存')]/preceding-sibling::*/input[@max='99']",
            ]

            # 先尝试找标红的输入框
            for selector in error_input_selectors:
                try:
                    inputs = row.query_selector_all(f"xpath={selector}")
                    if inputs:
                        self.logger.info(f"    找到标红的单用户限购量输入框: {len(inputs)} 个")
                        for input_element in inputs:
                            if input_element.is_visible():
                                return self._adjust_input_value(input_element, stock, row_index, "单用户限购量")
                except Exception as e:
                    continue

            # 如果没找到标红输入框，使用常规方法
            user_limit_selectors = [
                ".//input[@max='99']",
                ".//div[contains(@class, 'pro-virtual-table__row-cell')][8]//input",  # 第8列通常是单用户限购量
                ".//input[@type='text' and @max='99']"
            ]

            for selector in user_limit_selectors:
                try:
                    inputs = row.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"    选择器 '{selector}': 找到 {len(inputs)} 个输入框")

                    for input_element in inputs:
                        if input_element.is_visible():
                            return self._adjust_input_value(input_element, stock, row_index, "单用户限购量")

                except Exception as e:
                    self.logger.warning(f"  选择器 '{selector}' 失败: {e}")
                    continue

            self.logger.warning(f"  ❌ 产品 {row_index}: 未找到单用户限购量输入框")
            return False

        except Exception as e:
            self.logger.warning(f"修复产品 {row_index} 单用户限购量失败: {e}")
            return False

# ==================== 主程序运行逻辑 ====================

def setup_logger(container_code: str) -> logging.Logger:
    """设置日志记录器"""
    logger = logging.getLogger(f"miaoshou_{container_code}")
    logger.setLevel(logging.INFO)

    # 清除现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 创建日志目录
    logs_dir = 'logs'
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)

    # 创建文件处理器
    log_file = os.path.join(logs_dir, f"miaoshou_{container_code}_{datetime.now().strftime('%Y-%m-%d')}.log")
    file_handler = FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 创建格式器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s: %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器到日志记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

def start_browser(container_code: str) -> Optional[int]:
    """启动指纹浏览器"""
    url = 'http://127.0.0.1:6873/api/v1/browser/start'
    data = {
        "containerCode": container_code,
        "skipSystemResourceCheck": True
    }

    try:
        # 测试连接
        requests.get('http://127.0.0.1:6873/api/v1/browser/list', timeout=5)
        print("指纹浏览器服务连接测试成功")

        response = requests.post(url, json=data, timeout=30)
        if response.status_code == 200:
            res_json = response.json()
            if res_json.get('code') == 0:
                debugging_port = res_json['data']['debuggingPort']
                print(f"指纹浏览器启动成功，调试端口: {debugging_port}")
                return int(debugging_port)
            else:
                print(f"启动指纹浏览器失败: {res_json.get('msg')}")
                return None
        else:
            print(f"启动指纹浏览器失败，HTTP状态码: {response.status_code}")
            return None

    except Exception as e:
        print(f"启动指纹浏览器时发生异常: {e}")
        return None

def run_automation_with_semaphore(container_code: str, env_name: str, operation_type: str,
                                activity_name: Optional[str] = None, duration_hours: Optional[int] = None) -> bool:
    """使用信号量控制的自动化运行函数"""
    global open_counter

    with semaphore:
        open_counter += 1
        if open_counter > max_open_times:
            print(f"已达到最大打开次数限制: {max_open_times}")
            return False

        logger = setup_logger(container_code)
        logger.info(f"正在启动指纹浏览器环境: {env_name} ({container_code})")
        logger.info(f"发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start")
        logger.info(f"请求数据: {{'containerCode': '{container_code}', 'skipSystemResourceCheck': True}}")

        debugging_port = start_browser(container_code)
        if debugging_port is None:
            logger.error(f"无法启动容器代码为 {container_code} 的指纹浏览器")
            return False

        try:
            logger.info("正在初始化自动化实例...")
            automation = MiaoshouAutomation(container_code, debugging_port, logger, env_name)
            logger.info(f"Container code: {container_code} started.")

            logger.info(f"开始执行操作类型: {operation_type}")

            if operation_type == "add_products":
                logger.info("开始为未开始的秒杀活动添加产品 - 持续轮询模式")
                logger.info(f"🔧 调试模式: {'开启' if DEBUG_MODE else '关闭'}")

                # 持续轮询处理未开始的活动，永不停止
                round_count = 0
                activity_index = 0  # 当前处理的活动索引
                no_activity_count = 0  # 没有活动的连续次数

                logger.info("🔄 开始持续轮询处理，永不停止监控未开始的活动...")
                logger.info("💡 程序将持续运行，监控新的未开始活动...")

                while True:  # 永远循环
                    round_count += 1
                    logger.info(f"🔄 第 {round_count} 轮处理，目标活动索引: {activity_index}")

                    result = automation.add_products_to_flash_sale(target_activity_index=activity_index)

                    if result == "continue":
                        logger.info(f"✅ 第 {round_count} 轮处理完成，准备处理下一个活动...")
                        activity_index += 1  # 移动到下一个活动
                        no_activity_count = 0  # 重置无活动计数

                        # 添加轮询间隔，避免过于频繁的请求
                        wait_time = random.randint(3, 8)
                        logger.info(f"⏳ 轮询间隔等待 {wait_time} 秒...")
                        time.sleep(wait_time)
                        continue

                    elif result:
                        logger.info(f"✅ 第 {round_count} 轮处理完成，但没有更多活动需要处理")
                        no_activity_count = 0
                        activity_index += 1  # 移动到下一个活动继续检查

                        # 添加轮询间隔
                        wait_time = random.randint(3, 8)
                        logger.info(f"⏳ 轮询间隔等待 {wait_time} 秒...")
                        time.sleep(wait_time)
                        continue

                    else:
                        logger.info(f"🔍 第 {round_count} 轮未找到活动，继续监控...")
                        no_activity_count += 1
                        activity_index += 1  # 移动到下一个活动

                        # 当没有活动时，增加等待时间，避免过于频繁的检查
                        if no_activity_count >= 5:
                            # 连续5次没有活动，重置索引从头开始检查
                            activity_index = 0
                            no_activity_count = 0
                            wait_time = random.randint(30, 60)  # 等待30-60秒
                            logger.info(f"🔄 重置活动索引，等待 {wait_time} 秒后重新开始检查...")
                        else:
                            wait_time = random.randint(10, 20)  # 等待10-20秒
                            logger.info(f"⏳ 等待 {wait_time} 秒后继续检查...")

                        time.sleep(wait_time)
                        continue

                # 注意：由于是无限循环，以下代码永远不会执行到
                # 程序将持续运行直到手动停止

            elif operation_type == "create_activity":
                logger.info(f"开始创建限时秒杀活动: {activity_name}")
                result = automation.create_flash_sale_activity(activity_name, duration_hours)
            else:
                logger.error(f"未知的操作类型: {operation_type}")
                result = False

            # 记录结果到Excel
            result_data = {
                '时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                '环境名称': env_name,
                '容器代码': container_code,
                '操作类型': operation_type,
                '活动名称': activity_name or 'N/A',
                '结果': '成功' if result else '失败'
            }

            if operation_type == "add_products":
                write_to_excel_realtime(result_data, f"AddProducts_Results_{datetime.now().strftime('%Y-%m-%d')}.xlsx")
            else:
                write_to_excel_realtime(result_data, f"CreateActivity_Results_{datetime.now().strftime('%Y-%m-%d')}.xlsx")

            return result

        except Exception as e:
            logger.error(f"自动化执行过程中发生异常: {e}")
            return False

        finally:
            try:
                print("🔄 准备关闭浏览器...")
                # 注意：这里不关闭浏览器，让用户手动检查结果
                # automation.close_browser()
            except Exception as e:
                logger.error(f"关闭浏览器时发生异常: {e}")

def read_env_from_excel(file_path: str) -> Dict[str, str]:
    """从Excel文件读取环境配置"""
    try:
        df = pd.read_excel(file_path)
        env_dict = {}

        for _, row in df.iterrows():
            # 修复：正确处理数字类型的容器代码，去掉.0后缀
            container_code_raw = row.iloc[0]
            env_name_raw = row.iloc[1]

            # 处理容器代码：如果是数字，转换为整数再转字符串
            if pd.isna(container_code_raw):
                continue
            elif isinstance(container_code_raw, (int, float)):
                container_code = str(int(container_code_raw))
            else:
                container_code = str(container_code_raw).strip()

            # 处理环境名称
            if pd.isna(env_name_raw):
                continue
            else:
                env_name = str(env_name_raw).strip()

            if container_code and env_name and container_code != 'nan' and env_name != 'nan':
                env_dict[container_code] = env_name

        return env_dict

    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return {}

def create_sample_environment_file():
    """创建示例环境配置文件"""
    if not os.path.exists(environment_path):
        sample_data = {
            '环境ID': ['1254658148'],
            '环境名称': ['环境专用-妙手1']
        }
        df = pd.DataFrame(sample_data)
        df.to_excel(environment_path, index=False, engine='openpyxl')
        print(f"📁 创建示例环境配置文件: {environment_path}")
    else:
        print(f"✅ 环境配置文件已存在: {environment_path}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 妙手ERP限时秒杀自动化程序")
    print("=" * 60)

    # 创建必要的目录
    for dir_name in ['logs', 'reports', 'debug_pages']:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"📁 创建目录: {dir_name}")

    # 创建示例环境配置文件
    create_sample_environment_file()

    # 等待启动时间
    if wait_time > 0:
        print(f"⏳ 等待 {wait_time} 秒后开始执行...")
        time.sleep(wait_time)

    # 读取环境配置
    env_dict = read_env_from_excel(environment_path)
    if not env_dict:
        print(f"❌ 无法读取环境配置文件: {environment_path}")
        print("请确保文件存在且格式正确（第一列：环境ID，第二列：环境名称）")
        return

    print(f"📋 读取到 {len(env_dict)} 个环境配置")
    for env_id, env_name in env_dict.items():
        print(f"  - {env_id}: {env_name}")

    print("\n🚀 开始为未开始的秒杀活动添加商品...")

    success_count = 0
    for container_code, env_name in env_dict.items():
        print(f"\n🎯 处理环境: {env_name} ({container_code})")

        result = run_automation_with_semaphore(
            container_code=container_code,
            env_name=env_name,
            operation_type="add_products",
            activity_name=None,
            duration_hours=None
        )

        if result:
            success_count += 1
            print(f"✅ {env_name} 处理成功")
        else:
            print(f"❌ {env_name} 处理失败")

        # 环境间隔
        time.sleep(2)

    print(f"\n📊 执行完成: {success_count}/{len(env_dict)} 个环境处理成功")
    print("🔍 请检查浏览器页面和日志文件获取详细信息")

if __name__ == '__main__':
    main()




