#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的main函数 - 直接循环执行
"""

import sys
import time
from datetime import datetime, timedelta

# 导入原有的函数
from miaoshou_automation_addproduct_copy import (
    run_single_cycle, 
    create_sample_environment_file,
    loop_interval_seconds,
    wait_time
)

def main():
    """主程序入口 - 简化版本，直接循环执行"""
    print("=" * 60)
    print("🎯 妙手ERP限时秒杀自动化程序")
    print("=" * 60)

    # 创建必要的目录
    import os
    for dir_name in ['logs', 'reports']:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"📁 创建目录: {dir_name}")

    # 创建示例环境配置文件
    create_sample_environment_file()

    # 等待启动时间
    if wait_time > 0:
        print(f"⏳ 等待 {wait_time} 秒后开始执行...")
        time.sleep(wait_time)

    print("🔄 启动循环模式：每小时执行一次创建活动操作")
    print("⏰ 按 Ctrl+C 可以停止循环")
    print("=" * 60)

    # 循环执行
    cycle_count = 0
    total_success = 0
    
    try:
        while True:
            cycle_count += 1
            print(f"\n🔄 第 {cycle_count} 次循环开始")
            print("=" * 40)
            
            # 执行单次操作
            success_count = run_single_cycle()
            total_success += success_count
            
            print("=" * 40)
            print(f"🔄 第 {cycle_count} 次循环完成")
            print(f"📊 累计统计: {total_success} 次成功，共 {cycle_count} 次循环")
            
            # 等待指定时间
            next_run_time = datetime.now() + timedelta(seconds=loop_interval_seconds)
            print(f"⏰ 下次运行时间: {next_run_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"💤 等待 {loop_interval_seconds//60} 分钟后继续运行...\n")
            time.sleep(loop_interval_seconds)
            
    except KeyboardInterrupt:
        print(f"\n\n🛑 用户中断循环")
        print(f"📊 最终统计: 共执行 {cycle_count} 次循环，累计成功 {total_success} 次")
        print("👋 程序已退出")
        
    except Exception as e:
        print(f"\n❌ 循环执行出现异常: {e}")
        print(f"📊 异常前统计: 共执行 {cycle_count} 次循环，累计成功 {total_success} 次")

if __name__ == '__main__':
    main()
