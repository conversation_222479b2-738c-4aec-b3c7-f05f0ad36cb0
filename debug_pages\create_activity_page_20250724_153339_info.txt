页面调试信息 - create_activity_page
时间: 20250724_153339
URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
标题: 妙手-创建活动
页面大小: 100734 字符

==================================================
表单元素列表:
  input 0: {'type': 'input', 'index': 0, 'id': 'jx-id-7987-38', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 1: {'type': 'input', 'index': 1, 'id': 'no-id', 'name': 'no-name', 'placeholder': '请选择或输入搜索', 'className': 'jx-cascader__search-input', 'type_attr': 'text'}
  input 2: {'type': 'input', 'index': 2, 'id': 'jx-id-7987-39', 'name': 'no-name', 'placeholder': '请输入活动名称', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 3: {'type': 'input', 'index': 3, 'id': 'jx-id-7987-40', 'name': 'no-name', 'placeholder': '选择开始日期时间', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 4: {'type': 'input', 'index': 4, 'id': 'jx-id-7987-43', 'name': 'no-name', 'placeholder': '选择结束日期时间', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 5: {'type': 'input', 'index': 5, 'id': 'no-id', 'name': 'jx-id-7987-23', 'placeholder': 'no-placeholder', 'className': 'jx-radio__original', 'type_attr': 'radio'}
  input 6: {'type': 'input', 'index': 6, 'id': 'no-id', 'name': 'jx-id-7987-23', 'placeholder': 'no-placeholder', 'className': 'jx-radio__original', 'type_attr': 'radio'}
  input 7: {'type': 'input', 'index': 7, 'id': 'jx-id-7987-47', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-switch__input', 'type_attr': 'checkbox'}
  input 8: {'type': 'input', 'index': 8, 'id': 'J_minimumPluginVersion', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'no-class', 'type_attr': 'text'}
  input 9: {'type': 'input', 'index': 9, 'id': 'J_appInfo', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'no-class', 'type_attr': 'hidden'}
  input 10: {'type': 'input', 'index': 10, 'id': 'jx-id-7987-108', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 11: {'type': 'input', 'index': 11, 'id': 'jx-id-7987-107', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 12: {'type': 'input', 'index': 12, 'id': 'jx-id-7987-109', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 13: {'type': 'input', 'index': 13, 'id': 'jx-id-7987-110', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 14: {'type': 'input', 'index': 14, 'id': 'jx-id-7987-111', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 15: {'type': 'input', 'index': 15, 'id': 'jx-id-7987-112', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 16: {'type': 'input', 'index': 16, 'id': 'jx-id-7987-113', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 17: {'type': 'input', 'index': 17, 'id': 'jx-id-7987-114', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 18: {'type': 'input', 'index': 18, 'id': 'jx-id-7987-115', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 19: {'type': 'input', 'index': 19, 'id': 'jx-id-7987-116', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 20: {'type': 'input', 'index': 20, 'id': 'jx-id-7987-117', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 21: {'type': 'input', 'index': 21, 'id': 'jx-id-7987-118', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 22: {'type': 'input', 'index': 22, 'id': 'jx-id-7987-119', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 23: {'type': 'input', 'index': 23, 'id': 'jx-id-7987-120', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 24: {'type': 'input', 'index': 24, 'id': 'jx-id-7987-121', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 25: {'type': 'input', 'index': 25, 'id': 'jx-id-7987-122', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 26: {'type': 'input', 'index': 26, 'id': 'jx-id-7987-123', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 27: {'type': 'input', 'index': 27, 'id': 'jx-id-7987-124', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 28: {'type': 'input', 'index': 28, 'id': 'jx-id-7987-125', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 29: {'type': 'input', 'index': 29, 'id': 'jx-id-7987-126', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 30: {'type': 'input', 'index': 30, 'id': 'jx-id-7987-127', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 31: {'type': 'input', 'index': 31, 'id': 'jx-id-7987-128', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 32: {'type': 'input', 'index': 32, 'id': 'jx-id-7987-129', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 33: {'type': 'input', 'index': 33, 'id': 'jx-id-7987-130', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 34: {'type': 'input', 'index': 34, 'id': 'jx-id-7987-131', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 35: {'type': 'input', 'index': 35, 'id': 'jx-id-7987-132', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 36: {'type': 'input', 'index': 36, 'id': 'jx-id-7987-133', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 37: {'type': 'input', 'index': 37, 'id': 'jx-id-7987-134', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 38: {'type': 'input', 'index': 38, 'id': 'jx-id-7987-135', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 39: {'type': 'input', 'index': 39, 'id': 'jx-id-7987-136', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 40: {'type': 'input', 'index': 40, 'id': 'jx-id-7987-137', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 41: {'type': 'input', 'index': 41, 'id': 'jx-id-7987-138', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 42: {'type': 'input', 'index': 42, 'id': 'jx-id-7987-139', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 43: {'type': 'input', 'index': 43, 'id': 'jx-id-7987-140', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 44: {'type': 'input', 'index': 44, 'id': 'jx-id-7987-141', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 45: {'type': 'input', 'index': 45, 'id': 'jx-id-7987-142', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 46: {'type': 'input', 'index': 46, 'id': 'jx-id-7987-143', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 47: {'type': 'input', 'index': 47, 'id': 'jx-id-7987-144', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 48: {'type': 'input', 'index': 48, 'id': 'jx-id-7987-145', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 49: {'type': 'input', 'index': 49, 'id': 'jx-id-7987-146', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 50: {'type': 'input', 'index': 50, 'id': 'jx-id-7987-147', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 51: {'type': 'input', 'index': 51, 'id': 'jx-id-7987-148', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 52: {'type': 'input', 'index': 52, 'id': 'jx-id-7987-149', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 53: {'type': 'input', 'index': 53, 'id': 'jx-id-7987-150', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 54: {'type': 'input', 'index': 54, 'id': 'jx-id-7987-151', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 55: {'type': 'input', 'index': 55, 'id': 'jx-id-7987-152', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 56: {'type': 'input', 'index': 56, 'id': 'jx-id-7987-153', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 57: {'type': 'input', 'index': 57, 'id': 'jx-id-7987-154', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 58: {'type': 'input', 'index': 58, 'id': 'jx-id-7987-155', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 59: {'type': 'input', 'index': 59, 'id': 'jx-id-7987-156', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 60: {'type': 'input', 'index': 60, 'id': 'jx-id-7987-157', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 61: {'type': 'input', 'index': 61, 'id': 'jx-id-7987-158', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 62: {'type': 'input', 'index': 62, 'id': 'jx-id-7987-41', 'name': 'no-name', 'placeholder': '选择日期', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 63: {'type': 'input', 'index': 63, 'id': 'jx-id-7987-42', 'name': 'no-name', 'placeholder': '选择时间', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 64: {'type': 'input', 'index': 64, 'id': 'jx-id-7987-44', 'name': 'no-name', 'placeholder': '选择日期', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 65: {'type': 'input', 'index': 65, 'id': 'jx-id-7987-45', 'name': 'no-name', 'placeholder': '选择时间', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 66: {'type': 'input', 'index': 66, 'id': 'J_dwh_logS', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'no-class', 'type_attr': 'text'}
  label 0: {'type': 'label', 'index': 0, 'id': 'jx-id-7987-13', 'text': '选择店铺\n:', 'for': 'jx-id-7987-38'}
  label 1: {'type': 'label', 'index': 1, 'id': 'jx-id-7987-16', 'text': '活动名称\n:', 'for': 'jx-id-7987-39'}
  label 2: {'type': 'label', 'index': 2, 'id': 'jx-id-7987-17', 'text': '开始时间\n:', 'for': 'jx-id-7987-40'}
  label 3: {'type': 'label', 'index': 3, 'id': 'jx-id-7987-19', 'text': '结束时间\n:', 'for': 'jx-id-7987-43'}
  label 4: {'type': 'label', 'index': 4, 'id': 'jx-id-7987-21', 'text': '产品类别\n:', 'for': 'jx-id-7987-46'}
  label 5: {'type': 'label', 'index': 5, 'id': 'no-id', 'text': '按产品(SPU)', 'for': 'no-for'}
  label 6: {'type': 'label', 'index': 6, 'id': 'no-id', 'text': '按单品(SKU)', 'for': 'no-for'}
  label 7: {'type': 'label', 'index': 7, 'id': 'jx-id-7987-24', 'text': '自动延续:', 'for': 'jx-id-7987-47'}
  button 0: {'type': 'button', 'index': 0, 'id': 'no-id', 'text': '创建活动', 'className': 'jx-button jx-button--primary jx-button--default pro-button'}
  button 1: {'type': 'button', 'index': 1, 'id': 'no-id', 'text': '取消创建', 'className': 'jx-button jx-button--default pro-button'}
  button 2: {'type': 'button', 'index': 2, 'id': 'no-id', 'text': 'no-text', 'className': 'd-arrow-left jx-picker-panel__icon-btn'}
  button 3: {'type': 'button', 'index': 3, 'id': 'no-id', 'text': 'no-text', 'className': 'jx-picker-panel__icon-btn arrow-left'}
  button 4: {'type': 'button', 'index': 4, 'id': 'no-id', 'text': 'no-text', 'className': 'jx-picker-panel__icon-btn arrow-right'}
  button 5: {'type': 'button', 'index': 5, 'id': 'no-id', 'text': 'no-text', 'className': 'jx-picker-panel__icon-btn d-arrow-right'}
  button 6: {'type': 'button', 'index': 6, 'id': 'no-id', 'text': '此刻', 'className': 'jx-button jx-button--small is-text jx-picker-panel__link-btn'}
  button 7: {'type': 'button', 'index': 7, 'id': 'no-id', 'text': '确定', 'className': 'jx-button jx-button--small is-plain jx-picker-panel__link-btn'}
  button 8: {'type': 'button', 'index': 8, 'id': 'no-id', 'text': 'no-text', 'className': 'd-arrow-left jx-picker-panel__icon-btn'}
  button 9: {'type': 'button', 'index': 9, 'id': 'no-id', 'text': 'no-text', 'className': 'jx-picker-panel__icon-btn arrow-left'}
  button 10: {'type': 'button', 'index': 10, 'id': 'no-id', 'text': 'no-text', 'className': 'jx-picker-panel__icon-btn arrow-right'}
  button 11: {'type': 'button', 'index': 11, 'id': 'no-id', 'text': 'no-text', 'className': 'jx-picker-panel__icon-btn d-arrow-right'}
  button 12: {'type': 'button', 'index': 12, 'id': 'no-id', 'text': '此刻', 'className': 'jx-button jx-button--small is-text jx-picker-panel__link-btn'}
  button 13: {'type': 'button', 'index': 13, 'id': 'no-id', 'text': '确定', 'className': 'jx-button jx-button--small is-plain jx-picker-panel__link-btn'}
