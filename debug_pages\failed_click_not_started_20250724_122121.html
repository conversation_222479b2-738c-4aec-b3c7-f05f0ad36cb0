<!DOCTYPE html><html lang="en"><head>
    <!-- Google tag (gtag.js) -->
<script src="https://hm.baidu.com/hm.js?7974377ca203df6dcf98836f348d0722"></script><script async="" src="https://www.googletagmanager.com/gtag/js?id=G-28Q604ZC7E"></script>
<script>
    window.dataLayer = window.dataLayer || [];

    function gtag() {
        dataLayer.push(arguments);
    }

    gtag('js', new Date());
    gtag('config', 'G-28Q604ZC7E');
</script>
<title>妙手ERP - 免费TikTok、Shopee、TEMU、OZON等跨境电商ERP软件</title>
<meta name="description" content="妙手ERP，80万+跨境电商卖家的共同选择，致力于为TikTok、Shopee虾皮、Temu、Ozon、Lazada、SHEIN、速卖通等平台卖家提供采集、刊登、定价、营销、订单、代发采购、仓储、物流、财务等一体化服务">
<meta name="keywords" content="免费跨境电商ERP，TikTok ERP，Shopee ERP，虾皮ERP，TEMU ERP，OZON ERP">

<link rel="shortcut icon" href="https://erp-static-c1.chengji-inc.com/earth.ico" type="image/x-icon">
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?7974377ca203df6dcf98836f348d0722";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
<script type="application/javascript">
    window.ERP = {
        host: 'https://erp.91miaoshou.com/'
    };
    window.__aliCaptchaConfig = {
        sceneId: '1v2boc07',
        prefix: 'oosnfx',
        region: 'cn',
        isNeedAliCaptchaVerify: 1,
        isEmailRegAliCaptchaVerify: 1,
    };
</script>

<link rel="stylesheet" href="/api/app/views/static/js/layui/css/layui.css">
<link rel="stylesheet" href="https://erp.91miaoshou.com/api/app/views/static/css/home/<USER>">
<link rel="stylesheet" href="https://erp.91miaoshou.com/api/app/views/static/css/home/<USER>">
<script src="https://erp.91miaoshou.com/api/app/views/static/js/jquery-1.11.0.min.js"></script>
<script src="https://erp.91miaoshou.com/api/app/views/static/js/request_tool.js"></script>
<script src="https://erp.91miaoshou.com/api/app/views/static/js/home/<USER>"></script>
<script src="https://erp.91miaoshou.com/api/app/views/static/js/home/<USER>/api.js?20250611"></script>
<script src="https://erp.91miaoshou.com/api/app/views/static/js/biz/crypto-js.min.js?20250611"></script>

    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">

    <link rel="stylesheet" href="https://erp.91miaoshou.com/api/app/views/static/css/swiper.min.css">
<link rel="stylesheet" href="https://erp.91miaoshou.com/api/app/views/static/css/home/<USER>">
<link rel="stylesheet" href="https://erp.91miaoshou.com/api/app/views/static/css/home/<USER>">

    <script>
        var loginSuccessRedirectUrl = 'https://erp.91miaoshou.com/tiktok/marketing/flashSale';
        var subAccountRedirectUrl = 'https://erp.91miaoshou.com/welcome';
        var appName = 'mserp';
        var homeLoginBannerKey = commonUtil.isMobile()
            ? 'MOBILELOGINCENTERBANNER'
            : 'LOGINCENTERBANNER';
    </script>
    <link rel="canonical" href="https://erp.91miaoshou.com/">
<link id="layuicss-laydate" rel="stylesheet" href="https://erp.91miaoshou.com/api/app/views/static/js/layui/css/modules/laydate/default/laydate.css?v=5.3.1" media="all"><link id="layuicss-layer" rel="stylesheet" href="https://erp.91miaoshou.com/api/app/views/static/js/layui/css/modules/layer/default/layer.css?v=3.5.1" media="all"><link id="layuicss-skincodecss" rel="stylesheet" href="https://erp.91miaoshou.com/api/app/views/static/js/layui/css/modules/code.css?v=2" media="all"><script>
  console.log("start patch Notification");

  const OldNotify = window.Notification;
  const newNotify = function (title, opt) {
    const instance = new OldNotify(title, opt);
    try {
      window.postMessage({action: "notification", data: {}});
    } catch (e) {}
    return instance;
  };
  if (OldNotify.prototype) {
    newNotify.prototype = Object.create(OldNotify.prototype);
    newNotify.prototype.constructor = newNotify;
  }
  newNotify.requestPermission = OldNotify.requestPermission.bind(OldNotify);
  newNotify.toString = OldNotify.toString.bind(OldNotify);
  Object.defineProperty(newNotify, 'permission', {
    get: () => OldNotify.permission,
  });
  Object.defineProperty(newNotify, 'maxActions', {
    get: () => OldNotify.maxActions,
  });
  Object.defineProperty(newNotify, 'name', {
    get: () => OldNotify.name,
  });

  window.Notification = newNotify;
  </script></head>
<body>
<div class="home">
    <!-- 顶部菜单 -->

<div class="menu-background">
    <div class="menu">
        <div class="menu-left">
                        <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="" onclick="window.open('https://erp.91miaoshou.com', '_blank')">
                    </div>
        <div class="menu-right">
            <ul class="menus layui-nav">
                <li class="layui-nav-item active">
                    <a href="https://erp.91miaoshou.com" target="_blank" title="首页">首页</a>
                </li>
                <li class="layui-nav-item "><a href="/price.html" target="_blank" title="订购价格">订购价格</a></li>
                <li class="layui-nav-item "><a href="/about-us.html" target="_blank" title="关于我们">关于我们</a></li>
                <li class="layui-nav-item "><a href="/platforms.html" target="_blank" title="平台入驻">平台入驻</a></li>
                <li class="layui-nav-item has-children ">
                    <a href="javascript:;" title="活动与资源">活动与资源<i class="layui-icon layui-icon-down layui-nav-more"></i></a>
                    <dl class="layui-nav-child">
                        <dd class="nav-sub-child-wrap">
                            <a href="javascript:;" class="" title="跨境平台">跨境平台</a>
                            <dl class="nav-sub-child platform-menu">
                                                                                                    <dd><a href="/shopee.html" class="" target="_blank" title="Shopee平台">Shopee平台</a></dd>
                                                                                                                                    <dd><a href="/tiktok.html" class="" target="_blank" title="TikTok平台">TikTok平台</a></dd>
                                                                                                                                    <dd><a href="/temu.html" class="" target="_blank" title="Temu平台">Temu平台</a></dd>
                                                                                                                                    <dd><a href="/lazada.html" class="" target="_blank" title="Lazada平台">Lazada平台</a></dd>
                                                                                                                                    <dd><a href="/ozon.html" class="" target="_blank" title="Ozon平台">Ozon平台</a></dd>
                                                                                                                                    <dd><a href="/aliexpress.html" class="" target="_blank" title="速卖通平台">速卖通平台</a></dd>
                                                                                                                                    <dd><a href="/shein.html" class="" target="_blank" title="SHEIN平台">SHEIN平台</a></dd>
                                                                                                                                    <dd><a href="/coupang.html" class="" target="_blank" title="Coupang平台">Coupang平台</a></dd>
                                                                                                                                    <dd><a href="/mercadolibre.html" class="" target="_blank" title="美客多平台">美客多平台</a></dd>
                                                                                                                                    <dd><a href="/jumia.html" class="" target="_blank" title="Jumia平台">Jumia平台</a></dd>
                                                                                                                                    <dd><a href="/wildberries.html" class="" target="_blank" title="Wildberries平台">Wildberries平台</a></dd>
                                                                                                                                    <dd><a href="/walmart.html" class="" target="_blank" title="沃尔玛平台">沃尔玛平台</a></dd>
                                                                                                                                    <dd><a href="/emag.html" class="" target="_blank" title="eMAG平台">eMAG平台</a></dd>
                                                                                                                                    <dd><a href="/allegro.html" class="" target="_blank" title="Allegro平台">Allegro平台</a></dd>
                                                                                                                                    <dd><a href="/miravia.html" class="" target="_blank" title="Miravia平台">Miravia平台</a></dd>
                                                                                                                                    <dd><a href="/joom.html" class="" target="_blank" title="Joom平台">Joom平台</a></dd>
                                                                                            </dl>
                        </dd>
                        <dd class="nav-sub-child-wrap">
                            <a href="javascript:;" class="" title="热门功能">热门功能</a>
                            <dl class="nav-sub-child platform-menu hot-function">
                                                                    <dd><a href="/hgbq.html" class="" target="_blank" title="">跨境合规标签</a></dd>
                                                                    <dd><a href="/djmb.html" class="" target="_blank" title="">跨境定价模版</a></dd>
                                                                    <dd><a href="/kjxp.html" class="" target="_blank" title="">跨境选品工具</a></dd>
                                                            </dl>
                        </dd>
                        <dd><a class="" href="/partners.html" target="_blank" title="生态合作">生态合作</a></dd>
                        <dd><a class="" href="/activity.html" target="_blank" title="活动专区">活动专区</a></dd>
                        <dd>
                            <a href="/tms.html" class="" title="贷代资源">贷代资源</a>
                        </dd>
                    </dl>
                </li>
                <li class="layui-nav-item">
                    <a href="https://erp.91miaoshou.com/help_center/index.html" target="_blank" title="帮助中心">帮助中心</a>
                </li>
                <li class="switch-english">EN</li>
            <span class="layui-nav-bar"></span></ul>
            <div class="btn-box">
                <button class="free-use-button J_registerShowModalBtn">免费使用</button>
            </div>
        </div>
    </div>
</div>
<!-- 产品的弹出盒子 -->
<div class="show-product-box">
    <div class="product-box">
        <div class="product-item">
            <div class="title">ERP</div>
            <a href="https://erp.91miaoshou.com/" target="_blank">
                <div class="info">
                    <div class="info-title">
                        <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>/mserp.png" alt=""><span>妙手ERP</span>
                    </div>
                    <div class="describe">更高效的跨境电商ERP</div>
                </div>
            </a>
            <a href="https://www.easyboss.com/" target="_blank">
                <div class="info">
                    <div class="info-title">
                        <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>/easyboss.png" alt="">
                        <span>EasyBoss ERP</span>
                    </div>
                    <div class="describe">东南亚本土店铺精细化运营管家</div>
                </div>
            </a>
        </div>
        <div class="product-item">
            <div class="title">客服软件</div>
            <a href="https://www.askchat.com/" target="_blank">
                <div class="info">
                    <div class="info-title">
                        <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>/msdkf.png" alt="">
                        <span>AskChat爱商聊</span>
                    </div>
                    <div class="describe">聚合多平台多店铺客服管理</div>
                </div>
            </a>
        </div>
        <div class="product-item">
            <div class="title">仓储管理</div>
            <a href="https://www.huoxiaoyi.com/" target="_blank">
                <div class="info">
                    <div class="info-title">
                        <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>/hxy.png" alt=""> <span>货小易</span>
                    </div>
                    <div class="describe">TMS货代发货系统</div>
                </div>
            </a>
        </div>
    </div>
</div>
<!-- 中英文的切换盒子 -->
<div class="switch-english-box">
    <div class="language-box">
        <div>ZH</div>
        <div>EN</div>
    </div>
</div>    <!-- 右侧的导航 -->
<div class="right-menus">
    <div class="sidebar-box">
        <div class="sidebar-item mobile">
            <div class="item-show mobile-popup">
                <p>手机号：14759016685</p>
            </div>
        </div>
        <div class="sidebar-item wechat">
            <div class="item-show wechat-popup" style="margin-right: 125px;">
                <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>/wechat-pulbic-qr-code-v20250307.png" alt="">
                <div class="wechat-scan-text">
                    微信扫码联系客服
                </div>
            </div>
            <div class="item-show wechat-popup">
                <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>/wechat-public-qr-code.png" alt="">
                <div class="wechat-scan-text">
                    扫码关注微信公众号
                </div>
            </div>
        </div>
        <div class="sidebar-item association">
            <div class="item-show association-popup">
                <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>/association-qr-code-v3.png" alt="">
                <div class="association-scan-text">
                    官方卖家社群，扫码拉你进群
                </div>
            </div>
        </div>
    </div>
    <!-- 回到顶部 -->
    <div class="return-top">
        <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
    </div>
</div>    <!-- 轮播区域 -->
    <div class="swiper-box">
        <div class="swiper-container swiper-container-initialized swiper-container-horizontal">
            <div class="swiper-wrapper J_swiperWrapper" style="transition-duration: 0ms; transform: translate3d(-967px, 0px, 0px);"><div class="swiper-slide swiper-slide-prev" style="width: 967px;"><a href="https://erp.91miaoshou.com/logistics/agent" target="_blank"><img src="https://earth-rt.chengji-inc.com/market/open/2025-07-18/1e81b2ed-037a-4a9c-916d-24664016f050.png" alt=""></a></div><div class="swiper-slide swiper-slide-active" style="width: 967px;"><a href="https://erp.91miaoshou.com/auth/partner/tiktokBusiness" target="_blank"><img src="https://earth-rt.chengji-inc.com/market/open/2025-07-02/c9a1b400-3a7b-457d-b1a4-a66f671803da.jpg" alt=""></a></div><div class="swiper-slide swiper-slide-next" style="width: 967px;"><a href="https://erp.91miaoshou.com/auth_shop/index?platform=tiktok&amp;utm_source=250526tkjp" target="_blank"><img src="https://earth-rt.chengji-inc.com/market/open/2025-06-10/de563cf7-596e-4ce4-8ace-b339eea07cc5.png" alt=""></a></div><div class="swiper-slide" style="width: 967px;"><a href="javascript:void(0);"><img src="https://earth-rt.chengji-inc.com/market/open/2025-05-22/fb0462ce-4cd5-4f08-a89b-245db5890768.png" alt=""></a></div></div>
        <span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span></div>
    </div>
    <div class="swiper-pagination swiper-pagination-clickable swiper-pagination-bullets"><span class="swiper-pagination-bullet" tabindex="0" role="button" aria-label="Go to slide 1"></span><span class="swiper-pagination-bullet swiper-pagination-bullet-active" tabindex="0" role="button" aria-label="Go to slide 2"></span><span class="swiper-pagination-bullet" tabindex="0" role="button" aria-label="Go to slide 3"></span><span class="swiper-pagination-bullet" tabindex="0" role="button" aria-label="Go to slide 4"></span></div>
    <!-- 登录盒子 -->
    <div class="login-mask">
        <div class="login-box">
            <div class="login-title">登录</div>
            <div class="account">
                <input type="text" placeholder="手机号/子账号/邮箱" name="mobile" class="account-input J_inputField">
                <div class="account-icon">
                    <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                </div>
            </div>
            <div class="password">
                <input type="password" placeholder="密码" name="password" class="password-input J_inputField">
                <div class="password-icon">
                    <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                </div>
                <div class="icon-box eyes-close icon-pointer" onclick="handleOpenEyes('password')">
                    <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                </div>
                <div class="icon-box eyes-open icon-pointer hide" onclick="handleCloseEyes('password')">
                    <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                </div>
            </div>

            <button class="login login-button">立即登录</button>
            <div class="remember-box">
                <div class="remember-box-left">
                    <span class="remember-check-box" type="checkbox"></span>
                    <span class="remember">记住账号</span>
                </div>
                <div class="remember-box-right">
                    <div class="J_registerShowModalBtn">注册</div>
                    <span class="line"></span>
                    <div class="forget">
                        <span>忘记密码</span> <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                        <!-- 忘记密码 -->
                        <div class="forget-the-password">
                            <div class="forget-item" id="J_showMobileRecoveryDialog">使用注册手机号找回</div>
                            <div class="forget-item" id="J_showEmailRecoveryDialog">使用注册邮箱找回</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 异地登录验证 -->
    <div class="login-verify-dialog">
        <div class="login-verify-dialog-content">
            <div class="login-verify-dialog-content-header">
                <div class="dialog-title">异地登录安全验证</div>
                <div class="dialog-close J_loginVerifyDialogCloseBtn">×</div>
            </div>
            <div class="login-verify-dialog-content-main">
                <div class="tip-box">
                    <p class="tip">您正在<span class="key-point">异地登录</span>，登录地：<span class="login-verify-address"></span></p>
                    <p class="tip">为保障账号安全，登录前请完成验证。</p>
                </div>
                <div class="login-verify-form">
                    <div class="login-verify-form-item">
                        <input type="text" class="login-verify-form-input login-verify-account-input" data-verify-id="" readonly="">
                    </div>
                    <div class="login-verify-form-item code-form-item">
                        <input type="text" placeholder="请输入验证码" class="login-verify-form-input login-verify-code-input">
                        <span class="get-login-verify-code-btn">获取验证码</span>
                    </div>
                </div>
                <div class="login-verify-btn">立即验证</div>
            </div>
        </div>
    </div>

    <!-- 平台授权 -->
    <div class="authorization">
        <div class="activity-title">
            <div>OFFICIAI AUTHORIZATION</div>
            <div>主流跨境电商平台官方授权</div>
        </div>
        <div class="platform-box">
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2024-12-26/3ba2c3c2-8f88-4d05-8b8a-bc23cba300d9.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2024-12-26/a23c3697-dd1d-46b8-b772-2031e7c7d9f7.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/c01f478f-a275-4c32-87ef-1eec528e1ecf.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2024-06-11/1a51c14a-b9f8-4f94-9220-415ca3392c38.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/a248b1a8-88a1-46cb-bd1b-5bcf0ee1bc27.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2024-03-05/b7140142-ea76-495e-984e-ce2bf560f43d.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/d5802ebf-ac22-4d46-85b0-7eb4f088e438.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2024-03-19/2cd1da2e-e3e3-43d1-9169-9f1fdd0b8700.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2025-03-06/582c7d10-426a-41f8-9ddc-42222a30f4f8.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/4e3972c4-10f2-4fed-ba20-4508637bcc0d.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/f017a84e-6bb1-417a-8b0c-3da1955c8588.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2024-01-30/c030d1a1-e084-42a1-9c1d-d88af645b4ec.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-08-29/b334a977-4bd6-499b-95b7-b0a4f74f2b81.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2024-05-20/04b568f2-6b40-4276-8223-b4d8b8714e87.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2024-03-19/f0e1cc4b-3168-4b8e-831f-bc617bfaecde.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/93924b04-0279-4544-b597-73bae8cd199a.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/60dfbe8b-87e3-4208-ba17-41979debd45d.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/9cfb1a20-665f-4b84-ae4e-701d1c12ac2d.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-08-28/dd1b816d-187f-4a83-9b5f-f24778a58976.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2024-06-27/7e4b6cf3-1b34-4f2c-af02-4a143a56a68e.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/4a6d0a37-1e68-4800-9995-b1cea825ab33.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/836b9380-f5b2-4c8d-817d-3b46fbcf87a9.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/f9621ecf-33dc-4652-98b8-580110fdeeb3.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/2da09b9e-0bf9-4fa5-968d-2dd2d8b1657d.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/63f388fe-7ca0-44b6-96ff-687fa638e29d.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/02223e5a-3944-44e5-8913-fa35c2fdf58c.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/07e50b43-646c-437f-a58b-b6abe01df595.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/04d7c8f7-f91c-45ce-8c02-271ef5cec76e.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/6d64d89c-5e96-40a1-b0c8-7a933b4ee32f.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/71d54b97-52d1-44f8-96f3-d19d5b92c0a7.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/688712c1-0ea8-418c-9e8d-eb13a074a56e.png" alt="">
                </div>
            </div>
                        <div class="platform-item">
                <div class="platform-image-box">
                    <img src="https://earth-rt.chengji-inc.com/market/open/2023-06-02/7954c602-aa50-4b41-84fa-2e2ff73a7b5c.png" alt="">
                </div>
            </div>
                        <div class="platform-item"><button class="more-platform-button" onclick="window.open('/platforms.html')">更多平台 </button></div>
        </div>
    </div>
    <!--信赖选择-->
    <div class="select">
        <div class="activity-title">
            <div>TRUST CHOICE</div>
            <div>众多跨境卖家的信赖选择</div>
        </div>
        <div class="seller-box">
            <div class="seller-box-item">
                <img class="seller-box-item-image" src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                <div class="seller-box-item-number home-number">
                    80
                </div>
                <div>
                    <div class="seller-box-item-top">万+</div>
                    <div class="seller-box-item-bottom">跨境电商卖家</div>
                </div>
            </div>
            <div class="seller-box-item">
                <img class="seller-box-item-image" src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                <div class="seller-box-item-number home-number">
                    30
                </div>
                <div>
                    <div class="seller-box-item-top">+</div>
                    <div class="seller-box-item-bottom">跨境电商平台</div>
                </div>
            </div>
            <div class="seller-box-item">
                <img class="seller-box-item-image" src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                <div class="seller-box-item-number home-number">
                    360
                </div>
                <div>
                    <div class="seller-box-item-top">+</div>
                    <div class="seller-box-item-bottom">物流服务商</div>
                </div>
            </div>
            <div class="seller-box-item">
                <img class="seller-box-item-image" src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                <div class="seller-box-item-number home-number">
                    170
                </div>
                <div>
                    <div class="seller-box-item-top">+</div>
                    <div class="seller-box-item-bottom">仓储服务商</div>
                </div>
            </div>
            <div class="seller-box-item">
                <img class="seller-box-item-image" src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                <div class="seller-box-item-number home-number">
                    60
                </div>
                <div>
                    <div class="seller-box-item-top">+</div>
                    <div class="seller-box-item-bottom">跨境货代</div>
                </div>
            </div>
            <div class="seller-box-item">
                <img class="seller-box-item-image" src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                <div class="seller-box-item-number home-number">
                    500
                </div>
                <div>
                    <div class="seller-box-item-top">万+</div>
                    <div class="seller-box-item-bottom">每日处理订单量</div>
                </div>
            </div>
        </div>
    </div>
    <!-- 集成管理 -->
    <div class="integrated-management">
        <div class="activity-title">
            <div>INTEGRATED MANAGFMENT</div>
            <div>9大核心模块打造集成化管理</div>
        </div>
        <div class="module-box">
            <div class="module">
                <div class="module-inner">
                    <div class="icon">
                        <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                    </div>
                    <div class="title">多平台多店上货</div>
                    <div class="text">
                        支持采集100+货源平台商品，快速上货到多个平台多个店铺，支持批量翻译图片、一键自动抠图、批量添加水印等
                    </div>
                    <button class="core-module-detail-btn J_registerShowModalBtn">查看详情</button>
                </div>
            </div>
            <div class="module">
                <div class="module-inner">
                    <div class="icon">
                        <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                    </div>
                    <div class="title">多平台选品分析</div>
                    <div class="text">
                        快速洞察TikTok Shop、Shopee、Temu、Lazada等平台热销产品，并支持一键采集上货，同时提供数据分析工具，精准锁定TikTok热销类目、带货达人、热门视频等资源
                    </div>
                    <button class="core-module-detail-btn J_registerShowModalBtn">查看详情</button>
                </div>
            </div>
            <div class="module">
                <div class="module-inner">
                    <div class="icon">
                        <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                    </div>
                    <div class="title">海量优质货盘资源</div>
                    <div class="text">
                        直连跨境及本土优质货盘资源，跨境货盘已对接1688跨境货盘、淘宝跨境等平台；本土货盘覆盖美区、欧区、东南亚等市场，汇聚阿里美区现货盘、Doba等优质货源，支持一键采集上货
                    </div>
                    <button class="core-module-detail-btn J_registerShowModalBtn">查看详情</button>
                </div>
            </div>
            <div class="module">
                <div class="module-inner">
                    <div class="icon">
                        <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                    </div>
                    <div class="title">AI聚合与营销工具</div>
                    <div class="text">
                        AI智能优化Listing，生成标题、描述、商品图，智能去除水印，模特换脸换衣。可创建折扣、优惠券、满减满赠等营销活动，支持批量创建与多店同步管理
                    </div>
                    <button class="core-module-detail-btn J_registerShowModalBtn">查看详情</button>
                </div>
            </div>
            <div class="module">
                <div class="module-inner">
                    <div class="icon">
                        <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                    </div>
                    <div class="title">产品精准定价</div>
                    <div class="text">
                        结合多重价格因素，生成精准定价模板，可一键引用、批量生成售价，避免亏损，保证利润
                    </div>
                    <button class="core-module-detail-btn J_registerShowModalBtn">查看详情</button>
                </div>
            </div>
            <div class="module">
                <div class="module-inner">
                    <div class="icon">
                        <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                    </div>
                    <div class="title">高效订单处理</div>
                    <div class="text">
                        支持多货源平台一键采购、调用库存、代打包、打印面单、拣货单、分拣等多种操作，可处理售后订单，标记黑名单
                    </div>
                    <button class="core-module-detail-btn J_registerShowModalBtn">查看详情</button>
                </div>
            </div>
            <div class="module">
                <div class="module-inner">
                    <div class="icon">
                        <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                    </div>
                    <div class="title">智能库存与物流协同</div>
                    <div class="text">
                        全链路库存监控，智能补货预警，支持自营仓、第三方仓、平台仓。整合各平台支持承运的物流商，实现便捷化申请运单、物流跟踪及运费预估
                    </div>
                    <button class="core-module-detail-btn J_registerShowModalBtn">查看详情</button>
                </div>
            </div>
            <div class="module">
                <div class="module-inner">
                    <div class="icon">
                        <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                    </div>
                    <div class="title">多维度利润报表</div>
                    <div class="text">
                        基于店铺、订单、产品、员工等维度，整合销量、成本、利润、回款等数据可视化分析，清晰了解经营状况
                    </div>
                    <button class="core-module-detail-btn J_registerShowModalBtn">查看详情</button>
                </div>
            </div>
            <div class="module">
                <div class="module-inner">
                    <div class="icon">
                        <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                    </div>
                    <div class="title">托管模式集成管理</div>
                    <div class="text">集成管理TikTok Shop、Temu、SHEIN、AliExpress等平台半托管及全托管模式，支持合规标签模板一键套用、仓库库存智能联动、PDA扫描发货等</div>
                    <button class="core-module-detail-btn J_registerShowModalBtn">查看详情</button>
                </div>
            </div>
        </div>
    <div class="integrated-collapse-btn">展开全部</div></div>
    <!-- 订购方案 -->
    <div class="order">
        <div class="activity-title">
            <div>ORDERING SCHFMF</div>
            <div>个性化灵活订购方案</div>
        </div>
        <div class="select-box">
            <div class="select-menu">
                <div class="select-item" data-type="adv" style="color: rgb(255, 119, 51);">按店铺订购</div>
                <div class="select-item" data-type="vip">按套餐订购</div>
                <div class="select-active"></div>
            </div>
        </div>
        <div class="shwo-box">
            <div class="show-box-left">
                <div>按店铺订购</div>
                <div>低至4.5元/店铺/月，适用单平台多店铺卖家</div>
                <div>
                    <div class="purchase-button"><span>立即订购</span></div>
                </div>
            </div>
            <div class="show-box-right">
                <img class="in-pc J_orderTypeImg" src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" data-adv-img="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" data-vip-img="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                <img class="in-mobile J_orderTypeImg" src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" data-adv-img="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" data-vip-img="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
            </div>
        </div>
    </div>
    <!-- 免费使用盒子 -->
    <div class="free-box">
        <div>全面提升运营效率，从使用妙手开始</div>
        <div>简单、高效，助您开启轻量化运营之路</div>
        <div class="free-box-button J_registerShowModalBtn">
            <span>立即免费使用</span>
            <div class="free-button-animation"></div>
        </div>
    </div>
    <!-- 底部的地址栏 -->
    <div class="address-box">
    <div class="address-box-top">
        <div class="address-box-top-left">
            <div class="ms-erp">
                <div class="title">
                    <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                </div>
                <div class="text-content">
                    妙手ERP，为深圳呈云网络科技有限公司旗下产品，致力于为跨境电商卖家提供采集、刊登、定价、营销、订单、仓储、采购、物流、代打包、财务、客服等一体化运营服务，现已服务80万+跨境电商卖家，成就高效跨境电商。
                </div>
            </div>
            <div class="bottom-navigation">
                <div class="title">客户服务</div>
                <div class="link-box">
                    <div>
                        <a href="/about-us.html" target="_blank" title="关于妙手">关于妙手</a>
                    </div>
                    <div>
                        <a href="/price.html" target="_blank" title="订购价格">订购价格</a>
                    </div>
                    <div>
                        <a href="https://erp.91miaoshou.com/help_center/index.html" target="_blank" title="帮助中心">帮助中心</a>
                    </div>
                    <div>
                        <a href="/contact-us.html" target="_blank" title="联系我们">联系我们</a>
                    </div>
                </div>
            </div>
            <div class="bottom-navigation activities-navigation">
                <div class="title">活动与资源</div>
                <div class="link-box">
                    <div><a href="/activity.html" target="_blank" title="活动专区">活动专区</a></div>
                    <div><a href="/partners.html" target="_blank" title="生态合作">生态合作</a></div>
                    <div><a href="/platforms.html" target="_blank" title="平台入驻">平台入驻</a></div>
                    <div><a href="/blog/index.html" target="_blank" title="妙手跨境学院">妙手跨境学院</a></div>
                </div>
            </div>
        </div>
        <div class="address-box-top-right">
            <div class="address-box-top-right-right">

                <div>
                    <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                    <p>14759016685</p>
                </div>
                <div>
                    <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                    <p>深圳市龙岗区坂田街道岗头社区天安云谷产业园一期3栋B座1802</p>
                </div>
            </div>
            <div class="address-box-top-right-left">
                <div class="text">妙手官方公众号</div>
                <div>
                    <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                </div>
                <div>扫码关注妙手动态</div>
            </div>
            <div class="address-box-top-right-left">
                <div class="text text-content">官方卖家社群</div>
                <div>
                    <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>/association-qr-code-v3.png" alt="">
                </div>
                <div>扫码拉你进群</div>
            </div>
            <div class="watermark-box">
                <div class="tik-tok">
                    <div class="show-popup">
                        <div class="code">
                            <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                        </div>
                        <div class="txt">抖音二维码</div>
                    </div>
                </div>
                <div class="bilibili"></div>
                <div class="tencent"></div>
                <div class="iqiyi"></div>
            </div>
            <div class="address-box-mobile-qrcode in-mobile">
                <div class="text">妙手ERP官方公众号</div>
                <div>
                    <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="">
                </div>
            </div>
        </div>
    </div>

    <div class="address-box-bottom">
                <div class="top">
            <div>友情链接:</div>
                        <div><a href="https://www.easyboss.com/?utm_source=msdibudaohang" target="_blank">EasyBoss</a></div>
                        <div><a href="https://www.askchat.com/?utm_source=msdibudaohang" target="_blank">爱商聊</a></div>
                        <div><a href="https://tms.huoxiaoyi.com/?utm_source=msyqlj" target="_blank">货小易</a></div>
                        <div><a href="https://www.greatboss.com/" target="_blank">GreatBoss</a></div>
                        <div><a href="https://www.topwms.com/" target="_blank">TOPWMS</a></div>
                        <div><a href="https://www.goodcang.com/" target="_blank">谷仓海外仓</a></div>
                        <div><a href="https://www.yiketu.com/" target="_blank">易可图</a></div>
                        <div><a href="https://shopee.cn/" target="_blank">Shopee</a></div>
                    </div>
                <div class="bottom">
            <div>
                <a href=""> Copyright© 2025 深圳呈云网络科技有限公司 版权所有</a>
            </div>
            <div>
                <a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备18124050号</a>
            </div>
            <div>
                <a href="https://erp.91miaoshou.com/user-service.html" target="_blank">用户服务协议</a>
            </div>
            <div>
                <a href="https://erp.91miaoshou.com/privacy-policy.html" target="_blank">隐私条款</a>
            </div>
        </div>
    </div>


    <!-- 底部导航 -->
    <div class="bottom-menus in-mobile">
        <div class="mobile">
            <a href="tel: 14759016685" target="_blank">
                电话咨询
            </a>
        </div>
    </div>
</div>

<script src="https://erp.91miaoshou.com/api/app/views/static/js/layui/layui.js"></script>
<script src="https://erp.91miaoshou.com/api/app/views/static/js/home/<USER>"></script><style>html{font-size:192px;}</style>
</div>

<script src="https://erp.91miaoshou.com/api/app/views/static/js/swiper.min.js"></script>
<script src="https://erp.91miaoshou.com/api/app/views/static/js/home/<USER>"></script>
<script src="https://erp.91miaoshou.com/api/app/views/static/js/home/<USER>"></script>

<!-- 注册弹窗 -->
<div class="register-dialog">
    <div class="register-dialog-content">
        <div class="dialog-close J_hideDialog" data-dialog=".register-dialog">×</div>

        <div class="dialog-title">注册</div>

        <div class="register-type-tabs">
            <div class="register-type-tab active" data-type="mobile">
                手机号注册
            </div>

            <div class="register-type-tab" data-type="email">
                邮箱注册
            </div>
        </div>

        <!-- 电话注册 -->
        <div class="register-form mobile-register-form active J_registerForm" data-type="mobile">
            <input type="hidden" name="captchaUuid" class="J_inputField">
            <div class="register-form-item is-required mobile-form-item">
                <input type="text" placeholder="手机号" name="mobile" class="register-mobile J_inputField">
            </div>

            <div class="register-form-item is-required">
                <input type="password" placeholder="密码" name="password" class="register-password J_inputField">
            </div>

            <div class="register-form-item is-required">
                <input type="password" placeholder="确认密码" name="confirmPassword" class="register-confirm-password J_inputField">
            </div>

            <div class="register-form-item">
                <input type="text" placeholder="QQ账号" name="qq" class="register-qq J_inputField">
            </div>

            <div class="register-form-item is-required mobile-code-form-item">
                <div id="J_mobileRegisterAliCaptchaBox"></div>
                <div class="code-form-content">
                    <input type="text" placeholder="短信验证码" name="code" class="register-mobile-code J_inputField">
                    <span class="get-mobile-code-btn J_sendSmsBtn" id="J_mobileRegisterAliCaptchaBtn">获取验证码</span>
                </div>
            </div>

            <div class="agree-check-form-item">
                <span class="agree-checkbox"></span>
                <div class="agree-check-text">
                    <span>我已阅读并同意</span>
                    <span class="service-agreement" onclick="window.open('https://erp.91miaoshou.com/user-service.html', '_blank')">用户服务协议、</span>
                    <span class="privacy-agreement" onclick="window.open('https://erp.91miaoshou.com/privacy-policy.html', '_blank')">隐私政策</span>
                </div>
            </div>

            <div class="register-form-item">
                <div class="register-btn disabled tooltip">
                    立即注册
                    <span class="tooltip-text">需同意服务协议和隐私协议政策后,才可以注册</span>
                </div>
            </div>

            <div class="register-login-btn">
                <span><span>已有账号?</span><a class="J_registerLoginSwitch" href="javascript:void(0);" data-type="login">立即登录</a></span>
            </div>
        </div>

        <!-- 邮箱注册 -->
        <div class="register-form email-register-form J_registerForm" data-type="email">
            <input type="hidden" name="captchaUuid" class="J_inputField">
            <input type="hidden" name="mobileCountryCode" class="J_inputField" value="86">
            <div class="register-form-item is-required email-form-item">
                <input type="text" name="email" placeholder="请输入电子邮箱" class="register-email J_inputField">
            </div>

            <div class="register-form-item is-required email-form-item validate-form-item validate-form-item-register-mobile">
                <div class="form-wrap">
                    <span class="form-wrap__prepend">+86</span>
                    <input type="text" placeholder="手机号" name="mobile" class="register-mobile email-register-form__register-mobile validate-input-error J_inputField ">
                </div>
                <div class="error-tip warning">提醒：请输入真实有效的手机号，不作为登录账号，<span style="color:#FF7665;">仅用于身份安全校验和首次订购验证</span></div>
            </div>

            <div class="register-form-item is-required">
                <input type="password" name="password" placeholder="密码" class="register-password J_inputField">
            </div>

            <div class="register-form-item is-required">
                <input type="password" name="confirmPassword" placeholder="确认密码" class="register-confirm-password J_inputField">
            </div>

            <div class="register-form-item is-required email-code-form-item">
                <div id="J_emailRegisterAliCaptchaBox"></div>
                <div class="code-form-content">
                    <input type="text" name="code" placeholder="邮箱验证码" class="register-email-code J_inputField">
                    <span class="get-email-code-btn J_sendEmailCodeBtn" id="J_emailRegisterAliCaptchaBtn">
                获取验证码
              </span>
                </div>
            </div>

            <div class="register-form-item J_NoReceiveValidateCode" style="height: 10px; margin-top: 10px; display: none;">
                <a class="J_NoReceiveValidateCodeBtn" style="font-size:14px;color: #ff7733;width: 100%;text-align: right;margin-right: 20px;" href="javascript:void(0);">未收到验证码?</a>
            </div>

            <div class="register-form-item ">
                <input type="text" name="qq" placeholder="QQ账号" class="register-qq J_inputField">
            </div>

            <div class="register-form-item">
                <div class="register-btn">
                    立即注册
                </div>
            </div>
            <div class="register-login-btn">
                <span><span>已有账号?</span><a class="J_registerLoginSwitch" href="javascript:void(0);" data-type="login">立即登录</a></span>
            </div>
        </div>

        <!-- 登录 -->
        <div class="register-form login-register-form" style="display: none">
            <div class="register-form-item is-required">
                <input type="text" name="mobile" placeholder="手机号/子账号/邮箱" class="J_inputField">
            </div>

            <div class="register-form-item is-required">
                <input type="password" name="password" placeholder="密码" class=" J_inputField">
            </div>

            <div class="register-form-item">
                <div class="register-btn" id="J_loginAction" data-success-redirect-url="https://erp.91miaoshou.com/tiktok/marketing/flashSale">
                    立即登录
                </div>
            </div>
            <div class="register-login-btn">
                <span><a class="J_registerLoginSwitch" href="javascript: void(0);" data-type="register">返回注册</a></span>
            </div>
        </div>
    </div>
</div>

<!-- 注册成功弹窗二维码 -->
<div class="register-success-dialog">
    <div class="dialog-content">
        <div class="register-success-container">
            <div class="welcome-text">欢迎注册妙手账号</div>

            <div class="register-success-text">
                <span class="success-icon"></span>
                注册成功!
            </div>

            <div class="qr-code-box">
                <img src="https://erp.91miaoshou.com/api/app/views/static/img/home/<USER>" alt="" class="qr-code-image J_registerSuccessQrCodeImg">
            </div>

            <div class="qr-code-tips">微信扫描以上客服二维码，可免费获得授权店铺额度</div>

            <div class="register-success-login-button J_hideDialog" data-dialog=".register-success-dialog">
                立即登录
            </div>
        </div>
    </div>
</div>

<div id="toast" class="toast"></div>

<script type="text/javascript" src="https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js"></script>
<script src="https://erp.91miaoshou.com/api/app/views/static/js/home/<USER>"></script>
<link rel="stylesheet" href="https://erp.91miaoshou.com/api/app/views/static/css/home/<USER>">
<!-- 注册手机找回弹窗 -->
<div class="mobile-recovery-dialog">
    <div class="dialog-content">
        <div class="dialog-close J_mobileRecoveryDialogCloseBtn">×</div>

        <div class="dialog-body">
            <div class="recovery-step-tabs">
                <div class="recovery-step-tab first-step active">第一步</div>
                <div class="recovery-step-tab second-step">第二步</div>
            </div>

            <div class="recovery-form first-step-recovery-form active">
                <input type="hidden" name="captchaUuid" class="J_inputField">
                <div class="recovery-form-item">
                    <input type="text" placeholder="注册妙手ERP主账号手机号" name="mobile" class="recovery-mobile J_inputField">
                </div>

                <div class="recovery-form-item">
                    <div id="J_mobileFindPasswordAliCaptchaBox"></div>
                    <div class="code-form-content">
                        <input type="text" placeholder="短信验证码" name="code" class="recovery-mobile-code J_inputField">
                        <span class="get-code-btn" id="J_mobileFindPasswordAliCaptchaBtn" data-api-url="https://erp.91miaoshou.com/api/auth/account/send_find_password_code">
                  获取验证码
                </span>
                    </div>

                </div>

                <div class="recovery-form-item">
                    <div class="recovery-button J_mobileNextStepBtn">
                        下一步
                    </div>
                </div>
            </div>

            <div class="recovery-form second-step-recovery-form">
                <div class="recovery-form-item">
                    <input type="password" placeholder="密码" name="password" class="recovery-mobile-password J_inputField">
                </div>

                <div class="recovery-form-item">
                    <input type="password" placeholder="确认密码" name="confirmPassword" class="recovery-mobile-confirm-password J_inputField">
                </div>

                <div class="recovery-form-item">
                    <div class="recovery-button J_mobileResetBtn">
                        重置密码
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 注册邮箱找回弹窗 -->
<div class="email-recovery-dialog">
    <div class="dialog-content">
        <div class="dialog-close J_closeEmailRecoveryDialogBtn">×</div>

        <div class="dialog-body">
            <div class="recovery-form">
                <input type="hidden" name="captchaUuid" class="J_inputField">
                <div class="recovery-form-item">
                    <input placeholder="请输入电子邮箱" class="email-recovery-input J_inputField" name="email">
                </div>

                <div class="recovery-tips-line">
                    密码重置邮件将会发送到您的邮箱中，邮件有效期为24小时。 请及时登陆邮箱，点击邮件中的链接重置账户。
                </div>

                <div class="recovery-form-item">
                    <div id="J_emailFindPasswordAliCaptchaBox"></div>
                    <div class="recovery-button" id="J_emailFindPasswordAliCaptchaBtn">
                        发送重置邮件
                    </div>
                </div>

                <div class="recovery-form-item J_resetNoReceiveValidateCode" style="display: none;">
                    <div class="J_resetNoReceiveValidateCodeBtn" style="text-align: center;width: 100%;color: #ff7733"><a style="cursor:pointer">未收到验证码?</a></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 未收到验证码 -->
<div class="no-receive-reset-validate-code-dialog">
    <div class="dialog-content">
        <div class="no-receive-validate-code-container">
            <div class="title-text"><h2>未收到验证码?</h2></div>

            <div class="no-receive-validate-code-text">
                <p><span>1. 可能被识别为垃圾邮件，请至垃圾邮件中查看</span></p>
                <p><span>2. 请检查您输入的邮箱是否正确，或者尝试使用其他邮箱注册</span></p>
                <p><span>3. 请检查当前网络问题是否正常</span></p>
            </div>
            <div class="no-receive-btn">
                <a id="J_noReceiveResetCloseBtn">关闭</a>
            </div>
        </div>
    </div>
</div>

<div class="message">
    这是一个消息框
</div>


</body></html>