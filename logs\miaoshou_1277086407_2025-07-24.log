2025-07-24 12:20:52,383 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 12:20:52,383 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 12:20:52,384 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 12:20:52,410 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 12:20:58,497 - INFO: 启动响应: {'requestId': 'c29e5c15-5d2e-42f7-ad00-3ac85b01a1f1', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '7378', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 5735, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': 'c29e5c15-5d2e-42f7-ad00-3ac85b01a1f1', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 12:20:58,499 - INFO: 指纹浏览器启动成功，调试端口: 7378
2025-07-24 12:20:58,499 - INFO: 正在初始化自动化实例...
2025-07-24 12:21:02,597 - INFO: Container code: 1277086407 started.
2025-07-24 12:21:02,597 - INFO: 开始执行操作类型: add_products
2025-07-24 12:21:02,597 - INFO: 开始为未开始的秒杀活动添加产品
2025-07-24 12:21:02,597 - INFO: 🚀 开始为未开始的秒杀活动添加产品
2025-07-24 12:21:02,597 - INFO: 🎯 第0步：关闭多余的标签页
2025-07-24 12:21:02,597 - INFO: 当前打开的标签页数量: 1
2025-07-24 12:21:02,597 - INFO: ✅ 只有一个标签页，无需关闭
2025-07-24 12:21:02,597 - INFO: 🎯 第一步：导航到秒杀活动页面
2025-07-24 12:21:12,708 - INFO: ✅ 页面DOM加载完成
2025-07-24 12:21:17,713 - INFO: ✅ 成功导航到秒杀活动页面
2025-07-24 12:21:17,713 - INFO: 🎯 第二步：点击未开始标签
2025-07-24 12:21:17,714 - INFO: 等待页面稳定 3.7 秒...
2025-07-24 12:21:21,440 - INFO:   🎯 尝试点击未开始标签: 方法1
2025-07-24 12:21:21,441 - INFO:      选择器: //label[contains(@class, 'pro-radio-button')][contains(., '未开始')]
2025-07-24 12:21:21,536 - INFO:      找到 0 个未开始标签
2025-07-24 12:21:21,536 - WARNING:      未找到未开始标签: 方法1
2025-07-24 12:21:21,537 - ERROR: ❌ 第二步失败：无法点击未开始标签
2025-07-24 12:21:21,548 - INFO: 📄 已保存HTML源码: debug_pages\failed_click_not_started_20250724_122121.html
2025-07-24 12:21:22,224 - INFO: 📸 已保存页面截图: debug_pages\failed_click_not_started_20250724_122121.png
2025-07-24 12:21:22,255 - INFO: 📋 已保存页面信息: debug_pages\failed_click_not_started_20250724_122121_info.txt
2025-07-24 12:22:30,418 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 12:22:30,420 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 12:22:30,421 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 12:22:30,452 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 12:22:30,799 - INFO: 启动响应: {'requestId': '58563555-0e6d-4da4-827a-2d0f8f284c91', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '7378', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 98036, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '58563555-0e6d-4da4-827a-2d0f8f284c91', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 12:22:30,799 - INFO: 指纹浏览器启动成功，调试端口: 7378
2025-07-24 12:22:30,799 - INFO: 正在初始化自动化实例...
2025-07-24 12:22:31,090 - INFO: Container code: 1277086407 started.
2025-07-24 12:22:31,090 - INFO: 开始执行操作类型: add_products
2025-07-24 12:22:31,091 - INFO: 开始为未开始的秒杀活动添加产品
2025-07-24 12:22:31,091 - INFO: 🚀 开始为未开始的秒杀活动添加产品
2025-07-24 12:22:31,091 - INFO: 🎯 第0步：关闭多余的标签页
2025-07-24 12:22:31,092 - INFO: 当前打开的标签页数量: 1
2025-07-24 12:22:31,092 - INFO: ✅ 只有一个标签页，无需关闭
2025-07-24 12:22:31,092 - INFO: 🎯 第一步：导航到秒杀活动页面
2025-07-24 12:22:31,872 - INFO: ✅ 页面DOM加载完成
2025-07-24 12:22:36,885 - INFO: ✅ 成功导航到秒杀活动页面
2025-07-24 12:22:36,885 - INFO: 🎯 第二步：点击未开始标签
2025-07-24 12:22:36,885 - INFO: 等待页面稳定 2.9 秒...
2025-07-24 12:22:39,780 - INFO:   🎯 尝试点击未开始标签: 方法1
2025-07-24 12:22:39,780 - INFO:      选择器: //label[contains(@class, 'pro-radio-button')][contains(., '未开始')]
2025-07-24 12:22:39,808 - INFO:      找到 1 个未开始标签
2025-07-24 12:22:39,823 - INFO:      元素可见性: True
2025-07-24 12:22:40,410 - INFO:   ✅ 成功点击未开始标签: 方法1
2025-07-24 12:22:42,417 - INFO: ✅ 成功点击未开始标签
2025-07-24 12:22:42,417 - INFO: 🎯 第三步：点击第一个活动的管理产品
2025-07-24 12:22:42,417 - INFO: 等待活动列表加载 3.4 秒...
2025-07-24 12:22:45,837 - INFO: 🔍 保存点击未开始后的页面状态...
2025-07-24 12:22:45,845 - INFO: 📄 已保存HTML源码: debug_pages\after_click_not_started_20250724_122245.html
2025-07-24 12:22:45,973 - INFO: 📸 已保存页面截图: debug_pages\after_click_not_started_20250724_122245.png
2025-07-24 12:22:45,990 - INFO: 📋 已保存页面信息: debug_pages\after_click_not_started_20250724_122245_info.txt
2025-07-24 12:22:45,990 - INFO: 🔍 调试：查看页面上的所有按钮和链接...
2025-07-24 12:22:45,996 - INFO: 找到 64 个可能的操作按钮:
2025-07-24 12:22:45,996 - INFO:   SPAN 6: '产品采集' (可见: True)
2025-07-24 12:22:45,996 - INFO:   SPAN 21: '产品模板' (可见: True)
2025-07-24 12:22:45,996 - INFO:   SPAN 22: '在线产品' (可见: True)
2025-07-24 12:22:45,996 - INFO:   SPAN 23: '全球产品' (可见: True)
2025-07-24 12:22:45,996 - INFO:   SPAN 24: '店铺产品' (可见: True)
2025-07-24 12:22:45,996 - INFO:   SPAN 28: '产品导出' (可见: True)
2025-07-24 12:22:45,997 - INFO:   SPAN 31: '营销管理' (可见: True)
2025-07-24 12:22:45,997 - INFO:   SPAN 42: '产品ID' (可见: True)
2025-07-24 12:22:45,997 - INFO:   SPAN 44: '产品名称' (可见: True)
2025-07-24 12:22:45,997 - INFO:   SPAN 78: '33个产品（含915个SKU）' (可见: True)
2025-07-24 12:22:45,997 - INFO:   BUTTON 81: '管理产品' (可见: True)
2025-07-24 12:22:45,997 - INFO:   SPAN 82: '管理产品' (可见: True)
2025-07-24 12:22:45,997 - INFO:   BUTTON 83: '编辑' (可见: True)
2025-07-24 12:22:45,997 - INFO:   SPAN 84: '编辑' (可见: True)
2025-07-24 12:22:45,997 - INFO:   SPAN 94: '30个产品（含911个SKU）' (可见: True)
2025-07-24 12:22:45,997 - INFO:   BUTTON 97: '管理产品' (可见: True)
2025-07-24 12:22:45,997 - INFO:   SPAN 98: '管理产品' (可见: True)
2025-07-24 12:22:45,998 - INFO:   BUTTON 99: '编辑' (可见: True)
2025-07-24 12:22:45,998 - INFO:   SPAN 100: '编辑' (可见: True)
2025-07-24 12:22:45,998 - INFO:   SPAN 110: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:22:45,998 - INFO:   BUTTON 113: '管理产品' (可见: True)
2025-07-24 12:22:45,998 - INFO:   SPAN 114: '管理产品' (可见: True)
2025-07-24 12:22:45,998 - INFO:   BUTTON 115: '编辑' (可见: True)
2025-07-24 12:22:45,998 - INFO:   SPAN 116: '编辑' (可见: True)
2025-07-24 12:22:45,998 - INFO:   SPAN 126: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:22:45,998 - INFO:   BUTTON 129: '管理产品' (可见: True)
2025-07-24 12:22:45,998 - INFO:   SPAN 130: '管理产品' (可见: True)
2025-07-24 12:22:45,998 - INFO:   BUTTON 131: '编辑' (可见: True)
2025-07-24 12:22:45,998 - INFO:   SPAN 132: '编辑' (可见: True)
2025-07-24 12:22:45,999 - INFO:   SPAN 142: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:22:45,999 - INFO:   BUTTON 145: '管理产品' (可见: True)
2025-07-24 12:22:45,999 - INFO:   SPAN 146: '管理产品' (可见: True)
2025-07-24 12:22:45,999 - INFO:   BUTTON 147: '编辑' (可见: True)
2025-07-24 12:22:45,999 - INFO:   SPAN 148: '编辑' (可见: True)
2025-07-24 12:22:45,999 - INFO:   SPAN 158: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:22:45,999 - INFO:   BUTTON 161: '管理产品' (可见: True)
2025-07-24 12:22:45,999 - INFO:   SPAN 162: '管理产品' (可见: True)
2025-07-24 12:22:45,999 - INFO:   BUTTON 163: '编辑' (可见: True)
2025-07-24 12:22:45,999 - INFO:   SPAN 164: '编辑' (可见: True)
2025-07-24 12:22:45,999 - INFO:   SPAN 174: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:22:45,999 - INFO:   BUTTON 177: '管理产品' (可见: True)
2025-07-24 12:22:45,999 - INFO:   SPAN 178: '管理产品' (可见: True)
2025-07-24 12:22:45,999 - INFO:   BUTTON 179: '编辑' (可见: True)
2025-07-24 12:22:45,999 - INFO:   SPAN 180: '编辑' (可见: True)
2025-07-24 12:22:45,999 - INFO:   SPAN 190: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:22:45,999 - INFO:   BUTTON 193: '管理产品' (可见: True)
2025-07-24 12:22:45,999 - INFO:   SPAN 194: '管理产品' (可见: True)
2025-07-24 12:22:45,999 - INFO:   BUTTON 195: '编辑' (可见: True)
2025-07-24 12:22:45,999 - INFO:   SPAN 196: '编辑' (可见: True)
2025-07-24 12:22:45,999 - INFO:   SPAN 206: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:22:46,001 - INFO:   BUTTON 209: '管理产品' (可见: True)
2025-07-24 12:22:46,001 - INFO:   SPAN 210: '管理产品' (可见: True)
2025-07-24 12:22:46,001 - INFO:   BUTTON 211: '编辑' (可见: True)
2025-07-24 12:22:46,001 - INFO:   SPAN 212: '编辑' (可见: True)
2025-07-24 12:22:46,001 - INFO:   SPAN 222: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:22:46,001 - INFO:   BUTTON 225: '管理产品' (可见: True)
2025-07-24 12:22:46,001 - INFO:   SPAN 226: '管理产品' (可见: True)
2025-07-24 12:22:46,001 - INFO:   BUTTON 227: '编辑' (可见: True)
2025-07-24 12:22:46,001 - INFO:   SPAN 228: '编辑' (可见: True)
2025-07-24 12:22:46,001 - INFO:   SPAN 238: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:22:46,002 - INFO:   BUTTON 241: '管理产品' (可见: True)
2025-07-24 12:22:46,002 - INFO:   SPAN 242: '管理产品' (可见: True)
2025-07-24 12:22:46,002 - INFO:   BUTTON 243: '编辑' (可见: True)
2025-07-24 12:22:46,002 - INFO:   SPAN 244: '编辑' (可见: True)
2025-07-24 12:22:46,002 - INFO:   🎯 尝试点击管理产品: 方法1
2025-07-24 12:22:46,002 - INFO:      选择器: (//button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button')][contains(., '管理产品')])[1]
2025-07-24 12:22:46,008 - INFO:      找到 1 个管理产品按钮
2025-07-24 12:22:46,013 - INFO:      元素可见性: True
2025-07-24 12:22:46,604 - INFO:   ✅ 成功点击管理产品: 方法1
2025-07-24 12:22:49,617 - INFO: ✅ 成功点击管理产品按钮
2025-07-24 12:22:49,617 - INFO: 🎯 第四步：处理新标签页并添加产品
2025-07-24 12:22:49,617 - INFO: 等待新标签页打开...
2025-07-24 12:22:54,628 - INFO: 等待尝试 1/15: 当前标签页数量 1
2025-07-24 12:22:54,649 - INFO:   标签页 1: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 12:22:56,656 - INFO: 等待尝试 2/15: 当前标签页数量 2
2025-07-24 12:22:56,658 - INFO:   标签页 1: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 12:22:56,662 - INFO:   标签页 2: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale/create?shopId=8905292&platformPromotionId=7530486698141026066&site=TH&step=2, 标题=妙手-管理活动产品
2025-07-24 12:22:56,662 - INFO: ✅ 检测到新标签页已打开
2025-07-24 12:22:56,662 - INFO: 当前打开的标签页数量: 2
2025-07-24 12:22:56,662 - INFO: 切换到产品管理页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create?shopId=8905292&platformPromotionId=7530486698141026066&site=TH&step=2
2025-07-24 12:22:56,663 - INFO: 等待产品管理页面加载...
2025-07-24 12:22:56,671 - INFO: DOM内容加载完成，继续等待页面稳定...
2025-07-24 12:23:01,688 - INFO: 网络空闲状态达成
2025-07-24 12:23:04,693 - INFO: ✅ 产品管理页面加载完成
2025-07-24 12:23:04,693 - INFO: 🔍 保存产品管理页面调试信息...
2025-07-24 12:23:04,702 - INFO: 📄 已保存HTML源码: debug_pages\product_management_page_20250724_122304.html
2025-07-24 12:23:04,868 - INFO: 📸 已保存页面截图: debug_pages\product_management_page_20250724_122304.png
2025-07-24 12:23:04,879 - INFO: 📋 已保存页面信息: debug_pages\product_management_page_20250724_122304_info.txt
2025-07-24 12:23:04,880 - INFO: 🔍 调试：查看产品管理页面上的所有按钮...
2025-07-24 12:23:04,890 - INFO: 找到 29 个按钮:
2025-07-24 12:23:04,890 - INFO:   A 1: '预约培训
免费' (可见: True)
2025-07-24 12:23:04,891 - INFO:   A 2: '数据同步' (可见: True)
2025-07-24 12:23:04,891 - INFO:   A 3: '返回活动列表' (可见: True)
2025-07-24 12:23:04,891 - INFO:   BUTTON 4: '添加产品' (可见: True)
2025-07-24 12:23:04,892 - INFO:   BUTTON 5: '批量秒杀价格' (可见: True)
2025-07-24 12:23:04,892 - INFO:   BUTTON 6: '批量限购总量' (可见: True)
2025-07-24 12:23:04,892 - INFO:   BUTTON 7: '批量单用户限购量' (可见: True)
2025-07-24 12:23:04,892 - INFO:   BUTTON 8: '批量移除' (可见: True)
2025-07-24 12:23:04,892 - INFO:   BUTTON 9: '翻译' (可见: True)
2025-07-24 12:23:04,892 - INFO:   BUTTON 11: '移除' (可见: True)
2025-07-24 12:23:04,892 - INFO:   BUTTON 12: '同步' (可见: True)
2025-07-24 12:23:04,892 - INFO:   BUTTON 13: '展开SKU(16)' (可见: True)
2025-07-24 12:23:04,892 - INFO:   BUTTON 15: '移除' (可见: True)
2025-07-24 12:23:04,892 - INFO:   BUTTON 16: '同步' (可见: True)
2025-07-24 12:23:04,893 - INFO:   BUTTON 17: '展开SKU(10)' (可见: True)
2025-07-24 12:23:04,893 - INFO: 🎯 第五步：点击添加产品按钮
2025-07-24 12:23:06,901 - INFO:   🎯 尝试点击添加产品: 方法1
2025-07-24 12:23:06,901 - INFO:      选择器: //button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button')][contains(., '添加产品')]
2025-07-24 12:23:06,910 - INFO:      找到 1 个添加产品按钮
2025-07-24 12:23:06,922 - INFO:      元素可见性: True
2025-07-24 12:23:07,580 - INFO:   ✅ 成功点击添加产品: 方法1
2025-07-24 12:23:09,591 - INFO: ✅ 成功点击添加产品按钮
2025-07-24 12:23:15,597 - INFO: 🎯 第六步：处理添加产品弹窗
2025-07-24 12:23:15,597 - INFO: 📋 步骤1：勾选隐藏已参与限时秒杀的产品
2025-07-24 12:23:17,606 - INFO:   🎯 尝试勾选隐藏复选框: 方法1
2025-07-24 12:23:17,646 - INFO:   ✅ 成功勾选隐藏复选框: 方法1
2025-07-24 12:23:24,659 - INFO: 📊 步骤2：获取可添加产品总数量
2025-07-24 12:23:24,659 - INFO: 🔍 调试：查找所有可能包含数量信息的元素...
2025-07-24 12:23:24,674 - INFO: 找到 17 个包含'条'的元素:
2025-07-24 12:23:24,683 - INFO:   元素 1: '33条'
2025-07-24 12:23:24,688 - INFO:   元素 2: '20条/页'
2025-07-24 12:23:24,692 - INFO:   元素 3: '10条/页'
2025-07-24 12:23:24,695 - INFO:   元素 4: '20条/页'
2025-07-24 12:23:24,698 - INFO:   元素 5: '50条/页'
2025-07-24 12:23:24,700 - INFO:   元素 6: '100条/页'
2025-07-24 12:23:24,704 - INFO:   元素 7: '200条/页'
2025-07-24 12:23:24,708 - INFO:   元素 8: '500条/页'
2025-07-24 12:23:24,711 - INFO:   元素 9: '1000条/页'
2025-07-24 12:23:24,714 - INFO:   元素 10: '10条/页'
2025-07-24 12:23:24,714 - INFO: 🎯 尝试获取总数: 方法1 - //div[contains(@class, 'jx-pagination__total')]//span
2025-07-24 12:23:24,718 - INFO:      找到 2 个匹配元素
2025-07-24 12:23:24,723 - INFO:      元素 1 文本: '33条'
2025-07-24 12:23:24,723 - INFO:      提取到数字: 33
2025-07-24 12:23:24,724 - INFO: 📊 检测到可添加产品总数: 33
2025-07-24 12:23:27,738 - INFO: 📉 产品数量 33 <= 1000，执行一键全选策略
2025-07-24 12:23:29,744 - INFO: ☑️ 点击一键全选搜索结果产品
2025-07-24 12:23:29,785 - INFO: ✅ 成功点击一键全选搜索结果产品: 方法1
2025-07-24 12:23:36,798 - INFO: ✅ 点击确定按钮
2025-07-24 12:23:36,829 - INFO: ✅ 成功点击确定按钮: 方法1
2025-07-24 12:23:41,832 - INFO: 🎉 产品添加操作完成！
2025-07-24 12:24:21,143 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 12:24:21,144 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 12:24:21,144 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 12:24:21,173 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 12:24:21,515 - INFO: 启动响应: {'requestId': '8d803764-9d1a-4b93-aaa7-fe492a25d0bd', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '7378', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 208753, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '8d803764-9d1a-4b93-aaa7-fe492a25d0bd', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 12:24:21,515 - INFO: 指纹浏览器启动成功，调试端口: 7378
2025-07-24 12:24:21,515 - INFO: 正在初始化自动化实例...
2025-07-24 12:24:21,806 - INFO: Container code: 1277086407 started.
2025-07-24 12:24:21,806 - INFO: 开始执行操作类型: add_products
2025-07-24 12:24:21,806 - INFO: 开始为未开始的秒杀活动添加产品
2025-07-24 12:24:21,806 - INFO: 🚀 开始为未开始的秒杀活动添加产品
2025-07-24 12:24:21,806 - INFO: 🎯 第0步：关闭多余的标签页
2025-07-24 12:24:21,807 - INFO: 当前打开的标签页数量: 2
2025-07-24 12:24:21,807 - INFO: 关闭标签页 2: https://erp.91miaoshou.com/tiktok/marketing/flashSale
2025-07-24 12:24:21,825 - INFO: ✅ 已关闭多余标签页，使用第一个标签页
2025-07-24 12:24:21,825 - INFO: 🎯 第一步：导航到秒杀活动页面
2025-07-24 12:24:22,375 - INFO: ✅ 页面DOM加载完成
2025-07-24 12:24:27,382 - INFO: ✅ 成功导航到秒杀活动页面
2025-07-24 12:24:27,383 - INFO: 🎯 第二步：点击未开始标签
2025-07-24 12:24:27,383 - INFO: 等待页面稳定 3.4 秒...
2025-07-24 12:24:30,769 - INFO:   🎯 尝试点击未开始标签: 方法1
2025-07-24 12:24:30,769 - INFO:      选择器: //label[contains(@class, 'pro-radio-button')][contains(., '未开始')]
2025-07-24 12:24:30,788 - INFO:      找到 1 个未开始标签
2025-07-24 12:24:30,796 - INFO:      元素可见性: True
2025-07-24 12:24:31,348 - INFO:   ✅ 成功点击未开始标签: 方法1
2025-07-24 12:24:33,349 - INFO: ✅ 成功点击未开始标签
2025-07-24 12:24:33,349 - INFO: 🎯 第三步：点击第一个活动的管理产品
2025-07-24 12:24:33,349 - INFO: 等待活动列表加载 4.3 秒...
2025-07-24 12:24:37,705 - INFO: 🔍 保存点击未开始后的页面状态...
2025-07-24 12:24:37,726 - INFO: 📄 已保存HTML源码: debug_pages\after_click_not_started_20250724_122437.html
2025-07-24 12:25:07,743 - WARNING: 保存页面调试信息失败: Page.screenshot: Timeout 30000ms exceeded.
Call log:
  - taking page screenshot
  - waiting for fonts to load...
  - fonts loaded

2025-07-24 12:25:07,743 - INFO: 🔍 调试：查看页面上的所有按钮和链接...
2025-07-24 12:25:07,753 - INFO: 找到 64 个可能的操作按钮:
2025-07-24 12:25:07,754 - INFO:   SPAN 6: '产品采集' (可见: True)
2025-07-24 12:25:07,754 - INFO:   SPAN 21: '产品模板' (可见: True)
2025-07-24 12:25:07,754 - INFO:   SPAN 22: '在线产品' (可见: True)
2025-07-24 12:25:07,754 - INFO:   SPAN 23: '全球产品' (可见: True)
2025-07-24 12:25:07,754 - INFO:   SPAN 24: '店铺产品' (可见: True)
2025-07-24 12:25:07,754 - INFO:   SPAN 28: '产品导出' (可见: True)
2025-07-24 12:25:07,754 - INFO:   SPAN 31: '营销管理' (可见: True)
2025-07-24 12:25:07,754 - INFO:   SPAN 42: '产品ID' (可见: True)
2025-07-24 12:25:07,754 - INFO:   SPAN 44: '产品名称' (可见: True)
2025-07-24 12:25:07,755 - INFO:   SPAN 78: '33个产品（含915个SKU）' (可见: True)
2025-07-24 12:25:07,755 - INFO:   BUTTON 81: '管理产品' (可见: True)
2025-07-24 12:25:07,755 - INFO:   SPAN 82: '管理产品' (可见: True)
2025-07-24 12:25:07,755 - INFO:   BUTTON 83: '编辑' (可见: True)
2025-07-24 12:25:07,755 - INFO:   SPAN 84: '编辑' (可见: True)
2025-07-24 12:25:07,755 - INFO:   SPAN 94: '30个产品（含911个SKU）' (可见: True)
2025-07-24 12:25:07,755 - INFO:   BUTTON 97: '管理产品' (可见: True)
2025-07-24 12:25:07,756 - INFO:   SPAN 98: '管理产品' (可见: True)
2025-07-24 12:25:07,756 - INFO:   BUTTON 99: '编辑' (可见: True)
2025-07-24 12:25:07,756 - INFO:   SPAN 100: '编辑' (可见: True)
2025-07-24 12:25:07,756 - INFO:   SPAN 110: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:25:07,756 - INFO:   BUTTON 113: '管理产品' (可见: True)
2025-07-24 12:25:07,756 - INFO:   SPAN 114: '管理产品' (可见: True)
2025-07-24 12:25:07,756 - INFO:   BUTTON 115: '编辑' (可见: True)
2025-07-24 12:25:07,756 - INFO:   SPAN 116: '编辑' (可见: True)
2025-07-24 12:25:07,756 - INFO:   SPAN 126: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:25:07,757 - INFO:   BUTTON 129: '管理产品' (可见: True)
2025-07-24 12:25:07,757 - INFO:   SPAN 130: '管理产品' (可见: True)
2025-07-24 12:25:07,757 - INFO:   BUTTON 131: '编辑' (可见: True)
2025-07-24 12:25:07,757 - INFO:   SPAN 132: '编辑' (可见: True)
2025-07-24 12:25:07,757 - INFO:   SPAN 142: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:25:07,757 - INFO:   BUTTON 145: '管理产品' (可见: True)
2025-07-24 12:25:07,757 - INFO:   SPAN 146: '管理产品' (可见: True)
2025-07-24 12:25:07,757 - INFO:   BUTTON 147: '编辑' (可见: True)
2025-07-24 12:25:07,758 - INFO:   SPAN 148: '编辑' (可见: True)
2025-07-24 12:25:07,758 - INFO:   SPAN 158: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:25:07,758 - INFO:   BUTTON 161: '管理产品' (可见: True)
2025-07-24 12:25:07,758 - INFO:   SPAN 162: '管理产品' (可见: True)
2025-07-24 12:25:07,758 - INFO:   BUTTON 163: '编辑' (可见: True)
2025-07-24 12:25:07,758 - INFO:   SPAN 164: '编辑' (可见: True)
2025-07-24 12:25:07,758 - INFO:   SPAN 174: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:25:07,758 - INFO:   BUTTON 177: '管理产品' (可见: True)
2025-07-24 12:25:07,758 - INFO:   SPAN 178: '管理产品' (可见: True)
2025-07-24 12:25:07,758 - INFO:   BUTTON 179: '编辑' (可见: True)
2025-07-24 12:25:07,758 - INFO:   SPAN 180: '编辑' (可见: True)
2025-07-24 12:25:07,759 - INFO:   SPAN 190: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:25:07,759 - INFO:   BUTTON 193: '管理产品' (可见: True)
2025-07-24 12:25:07,759 - INFO:   SPAN 194: '管理产品' (可见: True)
2025-07-24 12:25:07,759 - INFO:   BUTTON 195: '编辑' (可见: True)
2025-07-24 12:25:07,759 - INFO:   SPAN 196: '编辑' (可见: True)
2025-07-24 12:25:07,759 - INFO:   SPAN 206: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:25:07,759 - INFO:   BUTTON 209: '管理产品' (可见: True)
2025-07-24 12:25:07,759 - INFO:   SPAN 210: '管理产品' (可见: True)
2025-07-24 12:25:07,759 - INFO:   BUTTON 211: '编辑' (可见: True)
2025-07-24 12:25:07,759 - INFO:   SPAN 212: '编辑' (可见: True)
2025-07-24 12:25:07,759 - INFO:   SPAN 222: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:25:07,759 - INFO:   BUTTON 225: '管理产品' (可见: True)
2025-07-24 12:25:07,760 - INFO:   SPAN 226: '管理产品' (可见: True)
2025-07-24 12:25:07,760 - INFO:   BUTTON 227: '编辑' (可见: True)
2025-07-24 12:25:07,760 - INFO:   SPAN 228: '编辑' (可见: True)
2025-07-24 12:25:07,760 - INFO:   SPAN 238: '0个产品（含0个SKU）' (可见: True)
2025-07-24 12:25:07,760 - INFO:   BUTTON 241: '管理产品' (可见: True)
2025-07-24 12:25:07,760 - INFO:   SPAN 242: '管理产品' (可见: True)
2025-07-24 12:25:07,760 - INFO:   BUTTON 243: '编辑' (可见: True)
2025-07-24 12:25:07,761 - INFO:   SPAN 244: '编辑' (可见: True)
2025-07-24 12:25:07,761 - INFO:   🎯 尝试点击管理产品: 方法1
2025-07-24 12:25:07,761 - INFO:      选择器: (//button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button')][contains(., '管理产品')])[1]
2025-07-24 12:25:07,818 - INFO:      找到 1 个管理产品按钮
2025-07-24 12:25:07,822 - INFO:      元素可见性: True
2025-07-24 12:25:08,487 - INFO:   ✅ 成功点击管理产品: 方法1
2025-07-24 12:25:11,490 - INFO: ✅ 成功点击管理产品按钮
2025-07-24 12:25:11,490 - INFO: 🎯 第四步：处理新标签页并添加产品
2025-07-24 12:25:11,490 - INFO: 等待新标签页打开...
2025-07-24 12:25:16,502 - INFO: 等待尝试 1/15: 当前标签页数量 1
2025-07-24 12:25:16,505 - INFO:   标签页 1: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 12:25:18,509 - INFO: 等待尝试 2/15: 当前标签页数量 2
2025-07-24 12:25:18,512 - INFO:   标签页 1: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 12:25:18,517 - INFO:   标签页 2: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale/create?shopId=8905292&platformPromotionId=7530486698141026066&site=TH&step=2, 标题=妙手-管理活动产品
2025-07-24 12:25:18,517 - INFO: ✅ 检测到新标签页已打开
2025-07-24 12:25:18,517 - INFO: 当前打开的标签页数量: 2
2025-07-24 12:25:18,518 - INFO: 切换到产品管理页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create?shopId=8905292&platformPromotionId=7530486698141026066&site=TH&step=2
2025-07-24 12:25:18,518 - INFO: 等待产品管理页面加载...
2025-07-24 12:25:18,520 - INFO: DOM内容加载完成，继续等待页面稳定...
2025-07-24 12:25:23,525 - INFO: 网络空闲状态达成
2025-07-24 12:25:26,536 - INFO: ✅ 产品管理页面加载完成
2025-07-24 12:25:26,537 - INFO: 🔍 保存产品管理页面调试信息...
2025-07-24 12:25:26,543 - INFO: 📄 已保存HTML源码: debug_pages\product_management_page_20250724_122526.html
2025-07-24 12:25:26,674 - INFO: 📸 已保存页面截图: debug_pages\product_management_page_20250724_122526.png
2025-07-24 12:25:26,684 - INFO: 📋 已保存页面信息: debug_pages\product_management_page_20250724_122526_info.txt
2025-07-24 12:25:26,685 - INFO: 🔍 调试：查看产品管理页面上的所有按钮...
2025-07-24 12:25:26,689 - INFO: 找到 29 个按钮:
2025-07-24 12:25:26,689 - INFO:   A 1: '预约培训
免费' (可见: True)
2025-07-24 12:25:26,689 - INFO:   A 2: '数据同步' (可见: True)
2025-07-24 12:25:26,689 - INFO:   A 3: '返回活动列表' (可见: True)
2025-07-24 12:25:26,690 - INFO:   BUTTON 4: '添加产品' (可见: True)
2025-07-24 12:25:26,690 - INFO:   BUTTON 5: '批量秒杀价格' (可见: True)
2025-07-24 12:25:26,690 - INFO:   BUTTON 6: '批量限购总量' (可见: True)
2025-07-24 12:25:26,690 - INFO:   BUTTON 7: '批量单用户限购量' (可见: True)
2025-07-24 12:25:26,690 - INFO:   BUTTON 8: '批量移除' (可见: True)
2025-07-24 12:25:26,690 - INFO:   BUTTON 9: '翻译' (可见: True)
2025-07-24 12:25:26,690 - INFO:   BUTTON 11: '移除' (可见: True)
2025-07-24 12:25:26,691 - INFO:   BUTTON 12: '同步' (可见: True)
2025-07-24 12:25:26,691 - INFO:   BUTTON 13: '展开SKU(16)' (可见: True)
2025-07-24 12:25:26,691 - INFO:   BUTTON 15: '移除' (可见: True)
2025-07-24 12:25:26,691 - INFO:   BUTTON 16: '同步' (可见: True)
2025-07-24 12:25:26,691 - INFO:   BUTTON 17: '展开SKU(10)' (可见: True)
2025-07-24 12:25:26,691 - INFO: 🎯 第五步：点击添加产品按钮
2025-07-24 12:25:28,696 - INFO:   🎯 尝试点击添加产品: 方法1
2025-07-24 12:25:28,696 - INFO:      选择器: //button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button')][contains(., '添加产品')]
2025-07-24 12:25:28,702 - INFO:      找到 1 个添加产品按钮
2025-07-24 12:25:28,709 - INFO:      元素可见性: True
2025-07-24 12:25:29,353 - INFO:   ✅ 成功点击添加产品: 方法1
2025-07-24 12:25:31,361 - INFO: ✅ 成功点击添加产品按钮
2025-07-24 12:25:37,362 - INFO: 🎯 第六步：处理添加产品弹窗
2025-07-24 12:25:37,362 - INFO: 📋 步骤1：勾选隐藏已参与限时秒杀的产品
2025-07-24 12:25:39,370 - INFO:   🎯 尝试勾选隐藏复选框: 方法1
2025-07-24 12:25:39,424 - INFO:   ✅ 成功勾选隐藏复选框: 方法1
2025-07-24 12:25:46,437 - INFO: 📊 步骤2：获取可添加产品总数量
2025-07-24 12:25:46,437 - INFO: 🔍 调试：查找所有可能包含数量信息的元素...
2025-07-24 12:25:46,448 - INFO: 找到 17 个包含'条'的元素:
2025-07-24 12:25:46,452 - INFO:   元素 1: '33条'
2025-07-24 12:25:46,456 - INFO:   元素 2: '20条/页'
2025-07-24 12:25:46,459 - INFO:   元素 3: '10条/页'
2025-07-24 12:25:46,461 - INFO:   元素 4: '20条/页'
2025-07-24 12:25:46,465 - INFO:   元素 5: '50条/页'
2025-07-24 12:25:46,467 - INFO:   元素 6: '100条/页'
2025-07-24 12:25:46,472 - INFO:   元素 7: '200条/页'
2025-07-24 12:25:46,475 - INFO:   元素 8: '500条/页'
2025-07-24 12:25:46,478 - INFO:   元素 9: '1000条/页'
2025-07-24 12:25:46,484 - INFO:   元素 10: '10条/页'
2025-07-24 12:25:46,484 - INFO: 🎯 尝试获取总数: 方法1 - //div[contains(@class, 'jx-pagination__total')]//span
2025-07-24 12:25:46,490 - INFO:      找到 2 个匹配元素
2025-07-24 12:25:46,495 - INFO:      元素 1 文本: '33条'
2025-07-24 12:25:46,495 - INFO:      提取到数字: 33
2025-07-24 12:25:46,495 - INFO: 📊 检测到可添加产品总数: 33
2025-07-24 12:25:49,510 - INFO: 📉 产品数量 33 <= 1000，执行一键全选策略
2025-07-24 12:25:51,513 - INFO: ☑️ 点击一键全选搜索结果产品
2025-07-24 12:25:51,556 - INFO: ✅ 成功点击一键全选搜索结果产品: 方法1
2025-07-24 12:25:58,569 - INFO: ✅ 点击确定按钮
2025-07-24 12:25:58,598 - INFO: ✅ 成功点击确定按钮: 方法1
2025-07-24 12:26:03,608 - INFO: 🎉 产品添加操作完成！
2025-07-24 12:41:59,690 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 12:41:59,690 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 12:41:59,690 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 12:41:59,708 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 12:42:00,006 - INFO: 启动响应: {'requestId': '4389c2c1-2e10-413f-8d54-ff680f5d1b72', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '7378', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 1267244, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '4389c2c1-2e10-413f-8d54-ff680f5d1b72', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 12:42:00,006 - INFO: 指纹浏览器启动成功，调试端口: 7378
2025-07-24 12:42:00,006 - INFO: 正在初始化自动化实例...
2025-07-24 12:42:00,321 - INFO: Container code: 1277086407 started.
2025-07-24 12:42:00,322 - INFO: 开始执行操作类型: create_activity
2025-07-24 12:42:00,322 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_124159
2025-07-24 12:42:00,322 - INFO: 活动持续时间: 24小时
2025-07-24 12:42:00,322 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_124159
2025-07-24 12:42:00,323 - INFO: 🧹 清理多余的标签页...
2025-07-24 12:42:00,323 - INFO: 当前共有 3 个标签页
2025-07-24 12:42:00,347 - INFO: 已关闭标签页 1
2025-07-24 12:42:00,401 - INFO: 已关闭标签页 2
2025-07-24 12:42:00,402 - INFO: 步骤1: 正在打开页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale
2025-07-24 12:42:06,510 - ERROR: 创建限时秒杀活动失败: Page.goto: net::ERR_CONNECTION_CLOSED at https://erp.91miaoshou.com/tiktok/marketing/flashSale
Call log:
  - navigating to "https://erp.91miaoshou.com/tiktok/marketing/flashSale", waiting until "load"

2025-07-24 12:42:06,510 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 12:43:20,774 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 12:43:20,774 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 12:43:20,774 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 12:43:20,802 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 12:43:21,361 - INFO: 启动响应: {'requestId': '21b21959-1c9b-454a-b58c-c7d086727ba1', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '7378', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 1348600, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '21b21959-1c9b-454a-b58c-c7d086727ba1', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 12:43:21,361 - INFO: 指纹浏览器启动成功，调试端口: 7378
2025-07-24 12:43:21,362 - INFO: 正在初始化自动化实例...
2025-07-24 12:43:21,656 - INFO: Container code: 1277086407 started.
2025-07-24 12:43:21,656 - INFO: 开始执行操作类型: create_activity
2025-07-24 12:43:21,656 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_124320
2025-07-24 12:43:21,656 - INFO: 活动持续时间: 24小时
2025-07-24 12:43:21,657 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_124320
2025-07-24 12:43:21,657 - INFO: 🧹 清理多余的标签页...
2025-07-24 12:43:21,657 - INFO: 当前共有 1 个标签页
2025-07-24 12:43:21,657 - INFO: 步骤1: 正在打开页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale
2025-07-24 12:43:24,900 - INFO: 页面加载成功 - 标题: 妙手-限时秒杀
2025-07-24 12:43:24,900 - INFO: 当前URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale
2025-07-24 12:43:24,900 - INFO: 等待 1.9 秒...
2025-07-24 12:43:26,811 - INFO: 步骤2: 点击创建活动按钮
2025-07-24 12:43:26,811 - INFO: 🔍 调试: 查看页面上的所有按钮
2025-07-24 12:43:26,818 - INFO: 找到 21 个按钮:
2025-07-24 12:43:26,818 - INFO:   按钮 0: '搜索' (可见)
2025-07-24 12:43:26,819 - INFO:   按钮 1: '重置' (可见)
2025-07-24 12:43:26,819 - INFO:   按钮 2: '创建活动' (可见)
2025-07-24 12:43:26,819 - INFO:   按钮 3: '自动延续活动' (可见)
2025-07-24 12:43:26,819 - INFO:   按钮 4: '自动延续跟踪记录' (可见)
2025-07-24 12:43:26,819 - INFO:   按钮 5: '同步活动' (可见)
2025-07-24 12:43:26,819 - INFO:   按钮 6: '全选' (可见)
2025-07-24 12:43:26,819 - INFO:   按钮 7: '反选' (可见)
2025-07-24 12:43:26,819 - INFO:   按钮 8: '' (可见)
2025-07-24 12:43:26,820 - INFO:   按钮 9: '' (可见)
2025-07-24 12:43:26,886 - INFO: 成功点击元素: 创建活动按钮
2025-07-24 12:43:26,886 - INFO: 🔍 等待新标签页打开...
2025-07-24 12:43:27,893 - INFO: 尝试 1/15: 当前共有 2 个标签页
2025-07-24 12:43:27,896 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 12:43:27,899 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 12:43:28,905 - INFO: 尝试 2/15: 当前共有 2 个标签页
2025-07-24 12:43:28,907 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 12:43:28,908 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 12:43:29,917 - INFO: 尝试 3/15: 当前共有 2 个标签页
2025-07-24 12:43:29,919 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 12:43:29,921 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 12:43:30,924 - INFO: 尝试 4/15: 当前共有 2 个标签页
2025-07-24 12:43:30,927 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 12:43:30,929 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 12:43:31,943 - INFO: 尝试 5/15: 当前共有 2 个标签页
2025-07-24 12:43:31,946 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 12:43:31,947 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 12:43:32,947 - INFO: 尝试 6/15: 当前共有 2 个标签页
2025-07-24 12:43:32,950 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 12:43:32,952 - WARNING: 检查标签页 1 失败: Page.title: Target page, context or browser has been closed
2025-07-24 12:43:33,957 - INFO: 尝试 7/15: 当前共有 1 个标签页
2025-07-24 12:43:33,959 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 12:43:34,969 - INFO: 尝试 8/15: 当前共有 1 个标签页
2025-07-24 12:43:34,972 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 12:43:35,982 - INFO: 尝试 9/15: 当前共有 1 个标签页
2025-07-24 12:43:35,984 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 12:43:36,998 - INFO: 尝试 10/15: 当前共有 1 个标签页
2025-07-24 12:43:36,999 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 12:43:38,009 - INFO: 尝试 11/15: 当前共有 1 个标签页
2025-07-24 12:43:38,011 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 12:43:39,026 - INFO: 尝试 12/15: 当前共有 1 个标签页
2025-07-24 12:43:39,030 - WARNING: 检查标签页 0 失败: Page.title: Target page, context or browser has been closed
2025-07-24 12:43:40,035 - INFO: 尝试 13/15: 当前共有 0 个标签页
2025-07-24 12:43:41,049 - INFO: 尝试 14/15: 当前共有 0 个标签页
2025-07-24 12:43:42,063 - INFO: 尝试 15/15: 当前共有 0 个标签页
2025-07-24 12:43:42,063 - WARNING: ⚠️ 未找到新的创建活动标签页，检查当前页面...
2025-07-24 12:43:42,065 - ERROR: 创建限时秒杀活动失败: Page.title: Target page, context or browser has been closed
2025-07-24 12:43:42,065 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 14:02:46,612 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 14:02:46,612 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 14:02:46,612 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 14:02:46,619 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 14:02:52,161 - INFO: 启动响应: {'requestId': '6be75436-deec-4ec2-bd27-bfd63d5d8c71', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '13925', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 5175, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '6be75436-deec-4ec2-bd27-bfd63d5d8c71', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 14:02:52,161 - INFO: 指纹浏览器启动成功，调试端口: 13925
2025-07-24 14:02:52,161 - INFO: 正在初始化自动化实例...
2025-07-24 14:02:55,708 - INFO: Container code: 1277086407 started.
2025-07-24 14:02:55,708 - INFO: 开始执行操作类型: create_activity
2025-07-24 14:02:55,708 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_140246
2025-07-24 14:02:55,708 - INFO: 活动持续时间: 24小时
2025-07-24 14:02:55,708 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_140246
2025-07-24 14:02:55,709 - INFO: 🧹 清理多余的标签页...
2025-07-24 14:02:55,709 - INFO: 当前共有 1 个标签页
2025-07-24 14:02:55,709 - INFO: 步骤1: 正在打开页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale
2025-07-24 14:03:07,146 - INFO: 页面加载成功 - 标题: 妙手-限时秒杀
2025-07-24 14:03:07,146 - INFO: 当前URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale
2025-07-24 14:03:07,146 - INFO: 等待 1.1 秒...
2025-07-24 14:03:08,294 - INFO: 步骤2: 点击创建活动按钮
2025-07-24 14:03:08,294 - INFO: 🔍 调试: 查看页面上的所有按钮
2025-07-24 14:03:08,303 - INFO: 找到 68 个按钮:
2025-07-24 14:03:08,303 - INFO:   按钮 0: '搜索' (可见)
2025-07-24 14:03:08,303 - INFO:   按钮 1: '重置' (可见)
2025-07-24 14:03:08,303 - INFO:   按钮 2: '创建活动' (可见)
2025-07-24 14:03:08,303 - INFO:   按钮 3: '自动延续活动' (可见)
2025-07-24 14:03:08,303 - INFO:   按钮 4: '自动延续跟踪记录
7' (可见)
2025-07-24 14:03:08,303 - INFO:   按钮 5: '同步活动' (可见)
2025-07-24 14:03:08,303 - INFO:   按钮 6: '管理产品' (可见)
2025-07-24 14:03:08,304 - INFO:   按钮 7: '编辑' (可见)
2025-07-24 14:03:08,304 - INFO:   按钮 8: '终止' (可见)
2025-07-24 14:03:08,304 - INFO:   按钮 9: '复制' (可见)
2025-07-24 14:03:08,383 - INFO: 成功点击元素: 创建活动按钮
2025-07-24 14:03:08,384 - INFO: 🔍 等待新标签页打开...
2025-07-24 14:03:09,399 - INFO: 尝试 1/15: 当前共有 2 个标签页
2025-07-24 14:03:09,403 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:03:09,405 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:03:10,415 - INFO: 尝试 2/15: 当前共有 2 个标签页
2025-07-24 14:03:10,417 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:03:10,419 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:03:11,420 - INFO: 尝试 3/15: 当前共有 2 个标签页
2025-07-24 14:03:11,422 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:03:11,424 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:03:12,434 - INFO: 尝试 4/15: 当前共有 2 个标签页
2025-07-24 14:03:12,436 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:03:12,439 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:03:13,439 - INFO: 尝试 5/15: 当前共有 2 个标签页
2025-07-24 14:03:13,441 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:03:13,442 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:03:14,452 - INFO: 尝试 6/15: 当前共有 2 个标签页
2025-07-24 14:03:14,454 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:03:14,455 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:03:15,463 - INFO: 尝试 7/15: 当前共有 2 个标签页
2025-07-24 14:03:15,465 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:03:15,466 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:03:16,470 - INFO: 尝试 8/15: 当前共有 2 个标签页
2025-07-24 14:03:16,473 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:03:16,475 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:03:17,484 - INFO: 尝试 9/15: 当前共有 2 个标签页
2025-07-24 14:03:17,487 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:03:17,488 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:03:18,494 - INFO: 尝试 10/15: 当前共有 2 个标签页
2025-07-24 14:03:18,496 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:03:18,498 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:03:19,499 - INFO: 尝试 11/15: 当前共有 2 个标签页
2025-07-24 14:03:19,502 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:03:19,503 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:03:20,513 - INFO: 尝试 12/15: 当前共有 2 个标签页
2025-07-24 14:03:20,515 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:03:20,517 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:03:21,524 - INFO: 尝试 13/15: 当前共有 2 个标签页
2025-07-24 14:03:21,527 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:03:21,528 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:03:22,531 - INFO: 尝试 14/15: 当前共有 2 个标签页
2025-07-24 14:03:22,533 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:03:22,535 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:03:23,545 - INFO: 尝试 15/15: 当前共有 2 个标签页
2025-07-24 14:03:23,547 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:03:23,548 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:03:23,548 - WARNING: ⚠️ 未找到新的创建活动标签页，检查当前页面...
2025-07-24 14:03:23,550 - ERROR: 创建限时秒杀活动失败: Page.title: Target crashed 
2025-07-24 14:03:23,550 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 14:03:50,840 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 14:03:50,840 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 14:03:50,840 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 14:03:50,863 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 14:03:51,200 - INFO: 启动响应: {'requestId': '6dd6adc5-3a73-4070-95d7-a5f349e9dc8c', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '13925', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 64213, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '6dd6adc5-3a73-4070-95d7-a5f349e9dc8c', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 14:03:51,200 - INFO: 指纹浏览器启动成功，调试端口: 13925
2025-07-24 14:03:51,201 - INFO: 正在初始化自动化实例...
2025-07-24 14:03:51,496 - INFO: Container code: 1277086407 started.
2025-07-24 14:03:51,496 - INFO: 开始执行操作类型: create_activity
2025-07-24 14:03:51,496 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_140350
2025-07-24 14:03:51,496 - INFO: 活动持续时间: 24小时
2025-07-24 14:03:51,496 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_140350
2025-07-24 14:03:51,497 - INFO: 🧹 清理多余的标签页...
2025-07-24 14:03:51,497 - INFO: 当前共有 1 个标签页
2025-07-24 14:03:51,497 - INFO: 步骤1: 正在打开页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale
2025-07-24 14:03:57,525 - INFO: 页面加载成功 - 标题: 妙手-限时秒杀
2025-07-24 14:03:57,525 - INFO: 当前URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale
2025-07-24 14:03:57,525 - INFO: 等待 1.5 秒...
2025-07-24 14:03:59,055 - INFO: 步骤2: 点击创建活动按钮
2025-07-24 14:03:59,055 - INFO: 🔍 调试: 查看页面上的所有按钮
2025-07-24 14:03:59,064 - INFO: 找到 68 个按钮:
2025-07-24 14:03:59,064 - INFO:   按钮 0: '搜索' (可见)
2025-07-24 14:03:59,064 - INFO:   按钮 1: '重置' (可见)
2025-07-24 14:03:59,064 - INFO:   按钮 2: '创建活动' (可见)
2025-07-24 14:03:59,064 - INFO:   按钮 3: '自动延续活动' (可见)
2025-07-24 14:03:59,064 - INFO:   按钮 4: '自动延续跟踪记录
7' (可见)
2025-07-24 14:03:59,064 - INFO:   按钮 5: '同步活动' (可见)
2025-07-24 14:03:59,064 - INFO:   按钮 6: '管理产品' (可见)
2025-07-24 14:03:59,064 - INFO:   按钮 7: '编辑' (可见)
2025-07-24 14:03:59,065 - INFO:   按钮 8: '终止' (可见)
2025-07-24 14:03:59,065 - INFO:   按钮 9: '复制' (可见)
2025-07-24 14:03:59,131 - INFO: 成功点击元素: 创建活动按钮
2025-07-24 14:03:59,131 - INFO: 🔍 等待新标签页打开...
2025-07-24 14:04:00,134 - INFO: 尝试 1/15: 当前共有 2 个标签页
2025-07-24 14:04:00,139 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 14:04:00,142 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:04:01,149 - INFO: 尝试 2/15: 当前共有 2 个标签页
2025-07-24 14:04:01,151 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 14:04:01,153 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:04:02,157 - INFO: 尝试 3/15: 当前共有 2 个标签页
2025-07-24 14:04:02,160 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 14:04:02,162 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:04:03,171 - INFO: 尝试 4/15: 当前共有 2 个标签页
2025-07-24 14:04:03,173 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 14:04:03,178 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:04:04,179 - INFO: 尝试 5/15: 当前共有 2 个标签页
2025-07-24 14:04:04,182 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 14:04:04,186 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:04:05,191 - INFO: 尝试 6/15: 当前共有 2 个标签页
2025-07-24 14:04:05,193 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 14:04:05,195 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:04:06,201 - INFO: 尝试 7/15: 当前共有 2 个标签页
2025-07-24 14:04:06,203 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 14:04:06,205 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:04:07,209 - INFO: 尝试 8/15: 当前共有 2 个标签页
2025-07-24 14:04:07,212 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 14:04:07,213 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:04:08,215 - INFO: 尝试 9/15: 当前共有 2 个标签页
2025-07-24 14:04:08,218 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 14:04:08,220 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:04:09,231 - INFO: 尝试 10/15: 当前共有 2 个标签页
2025-07-24 14:04:09,234 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 14:04:09,236 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:04:10,246 - INFO: 尝试 11/15: 当前共有 3 个标签页
2025-07-24 14:04:10,253 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale, 标题=妙手-限时秒杀
2025-07-24 14:04:10,258 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:04:10,263 - INFO:   标签页 2: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale/create, 标题=妙手-创建活动
2025-07-24 14:04:10,263 - INFO: ✅ 找到创建活动标签页: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:04:13,826 - INFO: ✅ 已切换到创建活动标签页并等待加载完成
2025-07-24 14:04:13,828 - INFO: 创建活动页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:04:13,828 - INFO: 创建活动页面标题: 妙手-创建活动
2025-07-24 14:04:13,828 - INFO: 🔍 保存创建活动页面调试信息...
2025-07-24 14:04:13,834 - INFO: 📄 已保存HTML源码: debug_pages\create_activity_page_20250724_140413.html
2025-07-24 14:04:17,290 - INFO: 📸 已保存页面截图: debug_pages\create_activity_page_20250724_140413.png
2025-07-24 14:04:17,300 - INFO: 📋 已保存页面信息: debug_pages\create_activity_page_20250724_140413_info.txt
2025-07-24 14:04:17,301 - INFO: 🔍 检查页面跳转状态...
2025-07-24 14:04:17,304 - INFO: 当前页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:04:17,304 - INFO: 当前页面标题: 妙手-创建活动
2025-07-24 14:04:17,304 - INFO: ✅ 确认已跳转到创建活动页面 (通过URL检查)
2025-07-24 14:04:17,304 - INFO: ✅ 页面跳转检查通过，继续执行...
2025-07-24 14:04:17,304 - INFO: 步骤3: 开始选择店铺
2025-07-24 14:04:17,304 - INFO: 等待创建活动页面完全加载...
2025-07-24 14:04:19,314 - INFO: 🔍 调试: 查看页面上的所有label元素
2025-07-24 14:04:19,317 - INFO: 找到 8 个label元素:
2025-07-24 14:04:19,317 - INFO:   🎯 重要: label 0: '选择店铺
:' (ID: jx-id-2959-13, 可见: True)
2025-07-24 14:04:19,317 - INFO:       XPath: //*[@id="jx-id-2959-13"]
2025-07-24 14:04:19,318 - INFO:   label 1: '活动名称
:' (ID: jx-id-2959-16, 可见)
2025-07-24 14:04:19,318 - INFO:   label 2: '开始时间
:' (ID: jx-id-2959-17, 可见)
2025-07-24 14:04:19,318 - INFO:   label 3: '结束时间
:' (ID: jx-id-2959-19, 可见)
2025-07-24 14:04:19,318 - INFO:   label 4: '产品类别
:' (ID: jx-id-2959-21, 可见)
2025-07-24 14:04:19,318 - INFO:   label 5: '按产品(SPU)' (ID: no-id, 可见)
2025-07-24 14:04:19,318 - INFO:   label 6: '按单品(SKU)' (ID: no-id, 可见)
2025-07-24 14:04:19,318 - INFO:   label 7: '自动延续:' (ID: jx-id-2959-24, 可见)
2025-07-24 14:04:19,318 - INFO: 🎯 第一步：点击'选择店铺'输入框
2025-07-24 14:04:19,319 - INFO: 等待 3.3 秒后开始操作...
2025-07-24 14:04:22,644 - INFO:   🎯 尝试点击: 输入框(placeholder)
2025-07-24 14:04:22,644 - INFO:      选择器: //input[@placeholder='请选择或输入搜索']
2025-07-24 14:04:22,651 - INFO:      找到 2 个匹配元素
2025-07-24 14:04:22,664 - INFO:      元素可见性: True
2025-07-24 14:04:53,204 - WARNING:   ❌ 点击输入框(placeholder)失败: ElementHandle.click: Timeout 30000ms exceeded.
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <input type="text" placeholder="请选择或输入搜索" class="jx-cascader__search-input"/> from <div class="jx-cascader__tags">…</div> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <input type="text" placeholder="请选择或输入搜索" class="jx-cascader__search-input"/> from <div class="jx-cascader__tags">…</div> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    13 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <input type="text" placeholder="请选择或输入搜索" class="jx-cascader__search-input"/> from <div class="jx-cascader__tags">…</div> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms
    - waiting for element to be visible, enabled and stable

2025-07-24 14:04:53,204 - INFO:   🎯 尝试点击: 输入框(class)
2025-07-24 14:04:53,204 - INFO:      选择器: //input[@class='jx-input__inner' and @placeholder='请选择或输入搜索']
2025-07-24 14:04:53,208 - INFO:      找到 1 个匹配元素
2025-07-24 14:04:53,211 - INFO:      元素可见性: True
2025-07-24 14:05:25,101 - WARNING:   ❌ 点击输入框(class)失败: ElementHandle.click: Timeout 30000ms exceeded.
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <input type="text" placeholder="请选择或输入搜索" class="jx-cascader__search-input"/> from <div class="jx-cascader__tags">…</div> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <input type="text" placeholder="请选择或输入搜索" class="jx-cascader__search-input"/> from <div class="jx-cascader__tags">…</div> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    11 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <input type="text" placeholder="请选择或输入搜索" class="jx-cascader__search-input"/> from <div class="jx-cascader__tags">…</div> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms

2025-07-24 14:05:25,101 - INFO:   🎯 尝试点击: 级联选择器容器
2025-07-24 14:05:25,101 - INFO:      选择器: //div[contains(@class, 'jx-cascader')]
2025-07-24 14:05:25,108 - INFO:      找到 7 个匹配元素
2025-07-24 14:05:25,112 - INFO:      元素可见性: True
2025-07-24 14:05:27,703 - INFO:   ✅ 成功点击: 级联选择器容器
2025-07-24 14:05:29,715 - INFO:   🎉 下拉菜单已成功弹出！
2025-07-24 14:05:29,715 - INFO: 🎯 第二步：等待下拉菜单出现
2025-07-24 14:05:29,715 - INFO:   等待下拉菜单加载 5.7 秒...
2025-07-24 14:05:35,452 - INFO:   ✅ 下拉菜单已显示
2025-07-24 14:05:35,452 - INFO: 🎯 第三步：选择国家（泰国）
2025-07-24 14:05:35,452 - INFO:   等待国家列表加载 3.5 秒...
2025-07-24 14:05:38,917 - INFO:   🎯 尝试勾选泰国复选框: 方法1
2025-07-24 14:05:38,917 - INFO:      选择器: //li[contains(@class, 'jx-cascader-node')]//span[contains(text(), '泰国')]/../..//span[@class='jx-checkbox__inner']
2025-07-24 14:05:38,921 - INFO:      找到 1 个泰国复选框
2025-07-24 14:05:38,925 - INFO:      复选框可见性: True
2025-07-24 14:05:41,413 - INFO:   ✅ 成功勾选泰国复选框: 方法1
2025-07-24 14:05:43,424 - INFO:   🎉 泰国复选框已成功勾选！
2025-07-24 14:05:43,424 - INFO: 🎯 第四步：勾选按单品(SKU)
2025-07-24 14:05:43,424 - INFO: 等待页面稳定 3.4 秒...
2025-07-24 14:05:46,852 - INFO:   🎯 尝试勾选按单品(SKU): 方法1
2025-07-24 14:05:46,853 - INFO:      选择器: //label[contains(@class, 'jx-radio')][.//span[contains(text(), '按单品(SKU)')]]
2025-07-24 14:05:46,856 - INFO:      找到 0 个按单品(SKU)选项
2025-07-24 14:05:46,856 - WARNING:      未找到按单品(SKU)选项: 方法1
2025-07-24 14:05:46,856 - INFO:   🎯 尝试勾选按单品(SKU): 方法2
2025-07-24 14:05:46,856 - INFO:      选择器: //input[@type='radio'][@value='2']
2025-07-24 14:05:46,859 - INFO:      找到 1 个按单品(SKU)选项
2025-07-24 14:05:46,863 - INFO:      元素可见性: True
2025-07-24 14:06:18,367 - WARNING:   ❌ 勾选按单品(SKU)失败: 方法2 - ElementHandle.click: Timeout 30000ms exceeded.
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <span class="jx-radio__inner"></span> intercepts pointer events
    - retrying click action
    - waiting 20ms
    - waiting for element to be visible, enabled and stable
    - element is visible, enabled and stable
    - scrolling into view if needed
    - done scrolling
    - <span class="jx-radio__inner"></span> intercepts pointer events
  2 × retrying click action
      - waiting 100ms
      - waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <li tabindex="-1" role="menuitem" data-v-8939b932="" class="jx-menu-item J_TiktokItemItemIndex">…</li> from <div data-v-1ccdc61d="" data-v-b8241973="" class="basic-layout-side earth">…</div> subtree intercepts pointer events
  4 × retrying click action
      - waiting 500ms
      - waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <span class="jx-radio__inner"></span> intercepts pointer events
    - retrying click action
      - waiting 500ms
      - waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <span class="jx-radio__inner"></span> intercepts pointer events
    - retrying click action
      - waiting 500ms
      - waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <li tabindex="-1" role="menuitem" data-v-8939b932="" class="jx-menu-item J_TiktokItemItemIndex">…</li> from <div data-v-1ccdc61d="" data-v-b8241973="" class="basic-layout-side earth">…</div> subtree intercepts pointer events
    - retrying click action
      - waiting 500ms
      - waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <li tabindex="-1" role="menuitem" data-v-8939b932="" class="jx-menu-item J_TiktokItemItemIndex">…</li> from <div data-v-1ccdc61d="" data-v-b8241973="" class="basic-layout-side earth">…</div> subtree intercepts pointer events
  - retrying click action
    - waiting 500ms
    - waiting for element to be visible, enabled and stable

2025-07-24 14:06:18,367 - INFO:   🎯 尝试勾选按单品(SKU): 方法3
2025-07-24 14:06:18,367 - INFO:      选择器: //label[.//span[contains(text(), '按单品(SKU)')]]//span[@class='jx-radio__inner']
2025-07-24 14:06:18,371 - INFO:      找到 0 个按单品(SKU)选项
2025-07-24 14:06:18,371 - WARNING:      未找到按单品(SKU)选项: 方法3
2025-07-24 14:06:18,372 - INFO:   🎯 尝试勾选按单品(SKU): 方法4
2025-07-24 14:06:18,372 - INFO:      选择器: //span[contains(text(), '按单品(SKU)')]/..
2025-07-24 14:06:18,375 - INFO:      找到 0 个按单品(SKU)选项
2025-07-24 14:06:18,375 - WARNING:      未找到按单品(SKU)选项: 方法4
2025-07-24 14:06:18,375 - INFO:   🎯 尝试勾选按单品(SKU): 方法5
2025-07-24 14:06:18,375 - INFO:      选择器: //label[contains(@class, 'jx-radio')][contains(., '按单品(SKU)')]
2025-07-24 14:06:18,379 - INFO:      找到 1 个按单品(SKU)选项
2025-07-24 14:06:18,383 - INFO:      元素可见性: True
2025-07-24 14:06:23,884 - INFO:   ✅ 成功勾选按单品(SKU): 方法5
2025-07-24 14:06:24,898 - INFO: 🎯 第五步：勾选自动延续
2025-07-24 14:06:24,898 - INFO: 等待页面稳定 1.9 秒...
2025-07-24 14:06:26,801 - INFO:   🎯 尝试勾选自动延续: 方法1
2025-07-24 14:06:26,801 - INFO:      选择器: //div[contains(@class, 'jx-switch')]
2025-07-24 14:06:26,805 - INFO:      找到 2 个自动延续选项
2025-07-24 14:06:26,810 - INFO:      元素可见性: True
2025-07-24 14:06:30,192 - INFO:   ✅ 成功勾选自动延续: 方法1
2025-07-24 14:06:31,205 - INFO: 🎯 第六步：点击创建活动
2025-07-24 14:06:31,205 - INFO: 等待页面稳定 1.2 秒...
2025-07-24 14:06:32,420 - INFO:   🎯 尝试点击创建活动: 方法1
2025-07-24 14:06:32,420 - INFO:      选择器: //button[.//span[contains(text(), '创建活动')]]
2025-07-24 14:06:32,423 - INFO:      找到 0 个创建活动按钮
2025-07-24 14:06:32,423 - WARNING:      未找到创建活动按钮: 方法1
2025-07-24 14:06:32,423 - INFO:   🎯 尝试点击创建活动: 方法2
2025-07-24 14:06:32,423 - INFO:      选择器: //button[contains(@class, 'jx-button--primary')]
2025-07-24 14:06:32,430 - INFO:      找到 1 个创建活动按钮
2025-07-24 14:06:32,432 - INFO:      元素可见性: True
2025-07-24 14:06:35,701 - INFO:   ✅ 成功点击创建活动: 方法2
2025-07-24 14:06:37,709 - INFO: 🎉 活动创建流程已完成！
2025-07-24 14:06:37,709 - INFO: 📋 创建的活动信息:
2025-07-24 14:06:37,709 - INFO:    - 活动名称: 107 泰国汽车 3-9
2025-07-24 14:06:37,709 - INFO:    - 选择国家: 泰国
2025-07-24 14:06:37,710 - INFO:    - 产品类别: 按单品(SKU)
2025-07-24 14:06:37,710 - INFO:    - 自动延续: 已启用
2025-07-24 14:06:37,710 - INFO: 步骤4: 设置产品类别为按单品(SKU)
2025-07-24 14:06:37,711 - INFO: 设置产品类别: 按单品(SKU)
2025-07-24 14:06:37,724 - INFO: 尝试选择按单品(SKU) (选择器 1)
2025-07-24 14:06:37,725 - WARNING: 选择器 1 失败: Page.evaluate: Target page, context or browser has been closed
2025-07-24 14:06:37,725 - INFO: 尝试选择按单品(SKU) (选择器 2)
2025-07-24 14:06:37,728 - WARNING: 选择器 2 失败: Page.evaluate: Target page, context or browser has been closed
2025-07-24 14:06:37,728 - INFO: 尝试选择按单品(SKU) (选择器 3)
2025-07-24 14:06:37,731 - WARNING: 选择器 3 失败: Page.evaluate: Target page, context or browser has been closed
2025-07-24 14:06:37,731 - ERROR: 所有选择器都失败，无法选择按单品(SKU)
2025-07-24 14:06:37,731 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 14:06:42,430 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 14:06:42,430 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 14:06:42,430 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 14:06:42,455 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 14:06:46,055 - INFO: 启动响应: {'requestId': '2bd8158d-ea63-4743-bbda-78c4c3598b0d', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 3277, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '2bd8158d-ea63-4743-bbda-78c4c3598b0d', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 14:06:46,055 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 14:06:46,055 - INFO: 正在初始化自动化实例...
2025-07-24 14:06:46,439 - INFO: Container code: 1277086407 started.
2025-07-24 14:06:46,439 - INFO: 开始执行操作类型: create_activity
2025-07-24 14:06:46,440 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_140642
2025-07-24 14:06:46,440 - INFO: 活动持续时间: 24小时
2025-07-24 14:06:46,440 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_140642
2025-07-24 14:06:46,440 - INFO: 🧹 清理多余的标签页...
2025-07-24 14:06:46,440 - INFO: 当前共有 1 个标签页
2025-07-24 14:06:46,440 - INFO: 步骤1: 正在打开页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale
2025-07-24 14:06:59,232 - INFO: 页面加载成功 - 标题: 妙手-限时秒杀
2025-07-24 14:06:59,232 - INFO: 当前URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale
2025-07-24 14:06:59,232 - INFO: 等待 1.2 秒...
2025-07-24 14:07:00,465 - INFO: 步骤2: 点击创建活动按钮
2025-07-24 14:07:00,465 - INFO: 🔍 调试: 查看页面上的所有按钮
2025-07-24 14:07:00,476 - INFO: 找到 68 个按钮:
2025-07-24 14:07:00,476 - INFO:   按钮 0: '搜索' (可见)
2025-07-24 14:07:00,476 - INFO:   按钮 1: '重置' (可见)
2025-07-24 14:07:00,476 - INFO:   按钮 2: '创建活动' (可见)
2025-07-24 14:07:00,476 - INFO:   按钮 3: '自动延续活动' (可见)
2025-07-24 14:07:00,477 - INFO:   按钮 4: '自动延续跟踪记录
7' (可见)
2025-07-24 14:07:00,477 - INFO:   按钮 5: '同步活动' (可见)
2025-07-24 14:07:00,477 - INFO:   按钮 6: '管理产品' (可见)
2025-07-24 14:07:00,477 - INFO:   按钮 7: '编辑' (可见)
2025-07-24 14:07:00,477 - INFO:   按钮 8: '终止' (可见)
2025-07-24 14:07:00,477 - INFO:   按钮 9: '复制' (可见)
2025-07-24 14:07:00,558 - INFO: 成功点击元素: 创建活动按钮
2025-07-24 14:07:00,558 - INFO: 🔍 等待新标签页打开...
2025-07-24 14:07:01,568 - INFO: 尝试 1/15: 当前共有 2 个标签页
2025-07-24 14:07:01,573 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:07:01,575 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:07:02,582 - INFO: 尝试 2/15: 当前共有 2 个标签页
2025-07-24 14:07:02,585 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:07:02,589 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:07:03,604 - INFO: 尝试 3/15: 当前共有 2 个标签页
2025-07-24 14:07:03,606 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:07:03,607 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:07:04,618 - INFO: 尝试 4/15: 当前共有 2 个标签页
2025-07-24 14:07:04,620 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:07:04,621 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:07:05,634 - INFO: 尝试 5/15: 当前共有 2 个标签页
2025-07-24 14:07:05,637 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:07:05,639 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:07:06,639 - INFO: 尝试 6/15: 当前共有 2 个标签页
2025-07-24 14:07:06,641 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:07:06,643 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:07:07,652 - INFO: 尝试 7/15: 当前共有 2 个标签页
2025-07-24 14:07:07,654 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:07:07,655 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:07:08,659 - INFO: 尝试 8/15: 当前共有 2 个标签页
2025-07-24 14:07:08,661 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:07:08,663 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:07:09,667 - INFO: 尝试 9/15: 当前共有 2 个标签页
2025-07-24 14:07:09,669 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:07:09,671 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:07:10,672 - INFO: 尝试 10/15: 当前共有 2 个标签页
2025-07-24 14:07:10,674 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:07:10,677 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:07:11,689 - INFO: 尝试 11/15: 当前共有 2 个标签页
2025-07-24 14:07:11,691 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:07:11,693 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:07:12,703 - INFO: 尝试 12/15: 当前共有 2 个标签页
2025-07-24 14:07:12,705 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:07:12,706 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:07:13,707 - INFO: 尝试 13/15: 当前共有 2 个标签页
2025-07-24 14:07:13,710 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:07:13,711 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:07:14,722 - INFO: 尝试 14/15: 当前共有 2 个标签页
2025-07-24 14:07:14,725 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:07:14,727 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:07:15,730 - INFO: 尝试 15/15: 当前共有 2 个标签页
2025-07-24 14:07:15,732 - INFO:   标签页 0: URL=about:blank, 标题=
2025-07-24 14:07:15,734 - WARNING: 检查标签页 1 失败: Page.title: Target crashed 
2025-07-24 14:07:15,734 - WARNING: ⚠️ 未找到新的创建活动标签页，检查当前页面...
2025-07-24 14:07:15,735 - ERROR: 创建限时秒杀活动失败: Page.title: Target crashed 
2025-07-24 14:07:15,735 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 14:22:54,755 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 14:22:54,756 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 14:22:54,756 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 14:22:54,780 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 14:22:55,095 - INFO: 启动响应: {'requestId': 'c287b755-e12e-4afb-9e4b-a9dacc36d69f', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 972319, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': 'c287b755-e12e-4afb-9e4b-a9dacc36d69f', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 14:22:55,095 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 14:22:55,095 - INFO: 正在初始化自动化实例...
2025-07-24 14:22:55,406 - INFO: Container code: 1277086407 started.
2025-07-24 14:22:55,406 - INFO: 开始执行操作类型: create_activity
2025-07-24 14:22:55,406 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_142254
2025-07-24 14:22:55,407 - INFO: 活动持续时间: 24小时
2025-07-24 14:22:55,407 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_142254
2025-07-24 14:22:55,407 - INFO: 🧹 清理多余的标签页...
2025-07-24 14:22:55,407 - INFO: 当前共有 3 个标签页
2025-07-24 14:22:55,430 - INFO: 已关闭标签页 1
2025-07-24 14:22:55,453 - INFO: 已关闭标签页 2
2025-07-24 14:22:55,453 - INFO: 步骤1: 直接打开创建活动页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:23:07,832 - INFO: 页面加载成功 - 标题: 妙手-创建活动
2025-07-24 14:23:07,833 - INFO: 当前URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:23:07,833 - INFO: 等待创建活动页面完全加载 4.8 秒...
2025-07-24 14:23:12,667 - INFO: 步骤2: 已跳过点击创建活动按钮（直接访问创建页面）
2025-07-24 14:23:12,667 - INFO: ✅ 已成功进入创建活动页面
2025-07-24 14:23:13,349 - INFO: ✅ 创建活动页面网络请求已完成
2025-07-24 14:23:13,351 - INFO: 创建活动页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:23:13,351 - INFO: 创建活动页面标题: 妙手-创建活动
2025-07-24 14:23:13,351 - INFO: 🔍 保存创建活动页面调试信息...
2025-07-24 14:23:13,357 - INFO: 📄 已保存HTML源码: debug_pages\create_activity_page_20250724_142313.html
2025-07-24 14:23:13,485 - INFO: 📸 已保存页面截图: debug_pages\create_activity_page_20250724_142313.png
2025-07-24 14:23:13,494 - INFO: 📋 已保存页面信息: debug_pages\create_activity_page_20250724_142313_info.txt
2025-07-24 14:23:13,495 - INFO: 🔍 验证创建活动页面加载状态...
2025-07-24 14:23:13,495 - INFO: ✅ 确认已进入创建活动页面 (通过URL检查)
2025-07-24 14:23:13,495 - INFO: ✅ 创建活动页面加载验证通过，继续执行...
2025-07-24 14:23:13,495 - INFO: 步骤3: 开始选择店铺
2025-07-24 14:23:13,495 - INFO: 等待创建活动页面完全加载...
2025-07-24 14:23:15,499 - INFO: 🔍 调试: 查看页面上的所有label元素
2025-07-24 14:23:15,507 - INFO: 找到 8 个label元素:
2025-07-24 14:23:15,507 - INFO:   🎯 重要: label 0: '选择店铺
:' (ID: jx-id-3056-13, 可见: True)
2025-07-24 14:23:15,507 - INFO:       XPath: //*[@id="jx-id-3056-13"]
2025-07-24 14:23:15,508 - INFO:   label 1: '活动名称
:' (ID: jx-id-3056-16, 可见)
2025-07-24 14:23:15,508 - INFO:   label 2: '开始时间
:' (ID: jx-id-3056-17, 可见)
2025-07-24 14:23:15,508 - INFO:   label 3: '结束时间
:' (ID: jx-id-3056-19, 可见)
2025-07-24 14:23:15,508 - INFO:   label 4: '产品类别
:' (ID: jx-id-3056-21, 可见)
2025-07-24 14:23:15,508 - INFO:   label 5: '按产品(SPU)' (ID: no-id, 可见)
2025-07-24 14:23:15,508 - INFO:   label 6: '按单品(SKU)' (ID: no-id, 可见)
2025-07-24 14:23:15,509 - INFO:   label 7: '自动延续:' (ID: jx-id-3056-24, 可见)
2025-07-24 14:23:15,509 - INFO: 🎯 第一步：点击'选择店铺'输入框
2025-07-24 14:23:15,509 - INFO: 等待 2.5 秒后开始操作...
2025-07-24 14:23:18,021 - INFO:   🎯 尝试点击: 输入框(placeholder)
2025-07-24 14:23:18,021 - INFO:      选择器: //input[@placeholder='请选择或输入搜索']
2025-07-24 14:23:18,029 - INFO:      找到 2 个匹配元素
2025-07-24 14:23:18,038 - INFO:      元素可见性: True
2025-07-24 14:23:48,568 - WARNING:   ❌ 点击输入框(placeholder)失败: ElementHandle.click: Timeout 30000ms exceeded.
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <input type="text" placeholder="请选择或输入搜索" class="jx-cascader__search-input"/> from <div class="jx-cascader__tags">…</div> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <input type="text" placeholder="请选择或输入搜索" class="jx-cascader__search-input"/> from <div class="jx-cascader__tags">…</div> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    57 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <input type="text" placeholder="请选择或输入搜索" class="jx-cascader__search-input"/> from <div class="jx-cascader__tags">…</div> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms

2025-07-24 14:23:48,568 - INFO:   🎯 尝试点击: 输入框(class)
2025-07-24 14:23:48,569 - INFO:      选择器: //input[@class='jx-input__inner' and @placeholder='请选择或输入搜索']
2025-07-24 14:23:48,575 - INFO:      找到 1 个匹配元素
2025-07-24 14:23:48,579 - INFO:      元素可见性: True
2025-07-24 14:24:19,133 - WARNING:   ❌ 点击输入框(class)失败: ElementHandle.click: Timeout 30000ms exceeded.
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <input type="text" placeholder="请选择或输入搜索" class="jx-cascader__search-input"/> from <div class="jx-cascader__tags">…</div> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <input type="text" placeholder="请选择或输入搜索" class="jx-cascader__search-input"/> from <div class="jx-cascader__tags">…</div> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    57 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <input type="text" placeholder="请选择或输入搜索" class="jx-cascader__search-input"/> from <div class="jx-cascader__tags">…</div> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms

2025-07-24 14:24:19,133 - INFO:   🎯 尝试点击: 级联选择器容器
2025-07-24 14:24:19,134 - INFO:      选择器: //div[contains(@class, 'jx-cascader')]
2025-07-24 14:24:19,141 - INFO:      找到 7 个匹配元素
2025-07-24 14:24:19,146 - INFO:      元素可见性: True
2025-07-24 14:24:19,733 - INFO:   ✅ 成功点击: 级联选择器容器
2025-07-24 14:24:21,746 - INFO:   🎉 下拉菜单已成功弹出！
2025-07-24 14:24:21,746 - INFO: 🎯 第二步：等待下拉菜单出现
2025-07-24 14:24:21,747 - INFO:   等待下拉菜单加载 3.7 秒...
2025-07-24 14:24:25,504 - INFO:   ✅ 下拉菜单已显示
2025-07-24 14:24:25,504 - INFO: 🎯 第三步：选择国家（泰国）
2025-07-24 14:24:25,504 - INFO:   等待国家列表加载 3.5 秒...
2025-07-24 14:24:29,012 - INFO:   🎯 尝试勾选泰国复选框: 方法1
2025-07-24 14:24:29,012 - INFO:      选择器: //li[contains(@class, 'jx-cascader-node')]//span[contains(text(), '泰国')]/../..//span[@class='jx-checkbox__inner']
2025-07-24 14:24:29,019 - INFO:      找到 1 个泰国复选框
2025-07-24 14:24:29,024 - INFO:      复选框可见性: True
2025-07-24 14:24:29,644 - INFO:   ✅ 成功勾选泰国复选框: 方法1
2025-07-24 14:24:31,653 - INFO:   🎉 泰国复选框已成功勾选！
2025-07-24 14:24:31,653 - INFO: 🎯 第四步：勾选按单品(SKU)
2025-07-24 14:24:31,653 - INFO: 等待页面稳定 3.7 秒...
2025-07-24 14:24:35,322 - INFO:   🎯 尝试勾选按单品(SKU): 方法1
2025-07-24 14:24:35,322 - INFO:      选择器: //label[contains(@class, 'jx-radio')][.//span[contains(text(), '按单品(SKU)')]]
2025-07-24 14:24:35,326 - INFO:      找到 0 个按单品(SKU)选项
2025-07-24 14:24:35,326 - WARNING:      未找到按单品(SKU)选项: 方法1
2025-07-24 14:24:35,326 - INFO:   🎯 尝试勾选按单品(SKU): 方法2
2025-07-24 14:24:35,326 - INFO:      选择器: //input[@type='radio'][@value='2']
2025-07-24 14:24:35,330 - INFO:      找到 1 个按单品(SKU)选项
2025-07-24 14:24:35,333 - INFO:      元素可见性: True
2025-07-24 14:25:05,867 - WARNING:   ❌ 勾选按单品(SKU)失败: 方法2 - ElementHandle.click: Timeout 30000ms exceeded.
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <span class="jx-radio__inner"></span> intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <span class="jx-radio__inner"></span> intercepts pointer events
    - retrying click action
      - waiting 100ms
    57 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <span class="jx-radio__inner"></span> intercepts pointer events
     - retrying click action
       - waiting 500ms

2025-07-24 14:25:05,867 - INFO:   🎯 尝试勾选按单品(SKU): 方法3
2025-07-24 14:25:05,867 - INFO:      选择器: //label[.//span[contains(text(), '按单品(SKU)')]]//span[@class='jx-radio__inner']
2025-07-24 14:25:05,871 - INFO:      找到 0 个按单品(SKU)选项
2025-07-24 14:25:05,871 - WARNING:      未找到按单品(SKU)选项: 方法3
2025-07-24 14:25:05,871 - INFO:   🎯 尝试勾选按单品(SKU): 方法4
2025-07-24 14:25:05,871 - INFO:      选择器: //span[contains(text(), '按单品(SKU)')]/..
2025-07-24 14:25:05,874 - INFO:      找到 0 个按单品(SKU)选项
2025-07-24 14:25:05,874 - WARNING:      未找到按单品(SKU)选项: 方法4
2025-07-24 14:25:05,874 - INFO:   🎯 尝试勾选按单品(SKU): 方法5
2025-07-24 14:25:05,875 - INFO:      选择器: //label[contains(@class, 'jx-radio')][contains(., '按单品(SKU)')]
2025-07-24 14:25:05,878 - INFO:      找到 1 个按单品(SKU)选项
2025-07-24 14:25:05,884 - INFO:      元素可见性: True
2025-07-24 14:25:06,450 - INFO:   ✅ 成功勾选按单品(SKU): 方法5
2025-07-24 14:25:07,460 - INFO: 🎯 第五步：勾选自动延续
2025-07-24 14:25:07,460 - INFO: 等待页面稳定 1.5 秒...
2025-07-24 14:25:08,924 - INFO:   🎯 尝试勾选自动延续: 方法1
2025-07-24 14:25:08,924 - INFO:      选择器: //div[contains(@class, 'jx-switch')]
2025-07-24 14:25:08,929 - INFO:      找到 2 个自动延续选项
2025-07-24 14:25:08,933 - INFO:      元素可见性: True
2025-07-24 14:25:09,476 - INFO:   ✅ 成功勾选自动延续: 方法1
2025-07-24 14:25:10,489 - INFO: 🎯 第六步：点击创建活动
2025-07-24 14:25:10,489 - INFO: 等待页面稳定 1.5 秒...
2025-07-24 14:25:11,989 - INFO:   🎯 尝试点击创建活动: 方法1
2025-07-24 14:25:11,989 - INFO:      选择器: //button[.//span[contains(text(), '创建活动')]]
2025-07-24 14:25:11,992 - INFO:      找到 0 个创建活动按钮
2025-07-24 14:25:11,992 - WARNING:      未找到创建活动按钮: 方法1
2025-07-24 14:25:11,993 - INFO:   🎯 尝试点击创建活动: 方法2
2025-07-24 14:25:11,993 - INFO:      选择器: //button[contains(@class, 'jx-button--primary')]
2025-07-24 14:25:11,996 - INFO:      找到 1 个创建活动按钮
2025-07-24 14:25:11,999 - INFO:      元素可见性: True
2025-07-24 14:25:12,585 - INFO:   ✅ 成功点击创建活动: 方法2
2025-07-24 14:25:14,595 - INFO: 🎉 活动创建流程已完成！
2025-07-24 14:25:14,595 - INFO: 📋 创建的活动信息:
2025-07-24 14:25:14,595 - INFO:    - 活动名称: 107 泰国汽车 3-9
2025-07-24 14:25:14,595 - INFO:    - 选择国家: 泰国
2025-07-24 14:25:14,595 - INFO:    - 产品类别: 按单品(SKU)
2025-07-24 14:25:14,595 - INFO:    - 自动延续: 已启用
2025-07-24 14:25:14,595 - INFO: 步骤4: 设置产品类别为按单品(SKU)
2025-07-24 14:25:14,596 - INFO: 设置产品类别: 按单品(SKU)
2025-07-24 14:25:14,599 - INFO: ✅ 按单品(SKU)已经被选中
2025-07-24 14:25:14,599 - INFO: 步骤5: 开启自动延续
2025-07-24 14:25:14,599 - INFO: 检查并开启自动延续开关
2025-07-24 14:25:14,599 - INFO: 尝试自动延续开关 (选择器 1)
2025-07-24 14:25:19,613 - WARNING: 自动延续选择器 1 失败: Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//input[@class='jx-switch__input' and @type='checkbox' and @role='switch']") to be visible
    15 × locator resolved to hidden <input name="" role="switch" true-value="1" type="checkbox" false-value="2" id="jx-id-3056-47" aria-checked="true" aria-disabled="false" class="jx-switch__input"/>

2025-07-24 14:25:19,613 - INFO: 尝试自动延续开关 (选择器 2)
2025-07-24 14:25:24,628 - WARNING: 自动延续选择器 2 失败: Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//div[contains(@class, 'auto-extension-label')]/following-sibling::div//input[@type='checkbox']") to be visible

2025-07-24 14:25:24,628 - INFO: 尝试自动延续开关 (选择器 3)
2025-07-24 14:25:29,636 - WARNING: 自动延续选择器 3 失败: Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//div[contains(@class, 'jx-switch')]//input[@class='jx-switch__input']") to be visible
    15 × locator resolved to hidden <input name="" role="switch" true-value="1" type="checkbox" false-value="2" id="jx-id-3056-47" aria-checked="true" aria-disabled="false" class="jx-switch__input"/>

2025-07-24 14:25:29,636 - INFO: 尝试自动延续开关 (选择器 4)
2025-07-24 14:25:34,650 - WARNING: 自动延续选择器 4 失败: Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//input[@type='checkbox' and @role='switch' and contains(@true-value, '1')]") to be visible
    15 × locator resolved to hidden <input name="" role="switch" true-value="1" type="checkbox" false-value="2" id="jx-id-3056-47" aria-checked="true" aria-disabled="false" class="jx-switch__input"/>

2025-07-24 14:25:34,651 - ERROR: 所有选择器都失败，无法操作自动延续开关
2025-07-24 14:25:34,651 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 14:26:28,025 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 14:26:28,025 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 14:26:28,025 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 14:26:28,050 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 14:26:28,392 - INFO: 启动响应: {'requestId': 'ae791d39-6736-4534-bddb-d2a30b343f2d', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 1185617, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': 'ae791d39-6736-4534-bddb-d2a30b343f2d', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 14:26:28,393 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 14:26:28,393 - INFO: 正在初始化自动化实例...
2025-07-24 14:26:28,679 - INFO: Container code: 1277086407 started.
2025-07-24 14:26:28,680 - INFO: 开始执行操作类型: create_activity
2025-07-24 14:26:28,680 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_142628
2025-07-24 14:26:28,680 - INFO: 活动持续时间: 24小时
2025-07-24 14:26:28,680 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_142628
2025-07-24 14:26:28,680 - INFO: 🧹 清理多余的标签页...
2025-07-24 14:26:28,680 - INFO: 当前共有 2 个标签页
2025-07-24 14:26:28,724 - INFO: 已关闭标签页 1
2025-07-24 14:26:28,725 - INFO: 步骤1: 直接打开创建活动页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:26:36,144 - INFO: 页面加载成功 - 标题: 妙手-创建活动
2025-07-24 14:26:36,144 - INFO: 当前URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:26:36,144 - INFO: 等待创建活动页面完全加载 3.1 秒...
2025-07-24 14:26:39,290 - INFO: 步骤2: 已跳过点击创建活动按钮（直接访问创建页面）
2025-07-24 14:26:39,290 - INFO: ✅ 已成功进入创建活动页面
2025-07-24 14:26:41,943 - INFO: ✅ 创建活动页面网络请求已完成
2025-07-24 14:26:41,945 - INFO: 创建活动页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:26:41,946 - INFO: 创建活动页面标题: 妙手-创建活动
2025-07-24 14:26:41,946 - INFO: 🔍 保存创建活动页面调试信息...
2025-07-24 14:26:41,957 - INFO: 📄 已保存HTML源码: debug_pages\create_activity_page_20250724_142641.html
2025-07-24 14:26:42,123 - INFO: 📸 已保存页面截图: debug_pages\create_activity_page_20250724_142641.png
2025-07-24 14:26:42,134 - INFO: 📋 已保存页面信息: debug_pages\create_activity_page_20250724_142641_info.txt
2025-07-24 14:26:42,134 - INFO: 🔍 验证创建活动页面加载状态...
2025-07-24 14:26:42,135 - INFO: ✅ 确认已进入创建活动页面 (通过URL检查)
2025-07-24 14:26:42,135 - INFO: ✅ 创建活动页面加载验证通过，继续执行...
2025-07-24 14:26:42,135 - INFO: 步骤3: 开始选择店铺
2025-07-24 14:26:42,135 - INFO: 等待创建活动页面完全加载...
2025-07-24 14:26:44,136 - INFO: 🔍 调试: 查看页面上的所有label元素
2025-07-24 14:26:44,140 - INFO: 找到 8 个label元素:
2025-07-24 14:26:44,140 - INFO:   🎯 重要: label 0: '选择店铺
:' (ID: jx-id-3352-13, 可见: True)
2025-07-24 14:26:44,140 - INFO:       XPath: //*[@id="jx-id-3352-13"]
2025-07-24 14:26:44,140 - INFO:   label 1: '活动名称
:' (ID: jx-id-3352-16, 可见)
2025-07-24 14:26:44,141 - INFO:   label 2: '开始时间
:' (ID: jx-id-3352-17, 可见)
2025-07-24 14:26:44,141 - INFO:   label 3: '结束时间
:' (ID: jx-id-3352-19, 可见)
2025-07-24 14:26:44,141 - INFO:   label 4: '产品类别
:' (ID: jx-id-3352-21, 可见)
2025-07-24 14:26:44,141 - INFO:   label 5: '按产品(SPU)' (ID: no-id, 可见)
2025-07-24 14:26:44,141 - INFO:   label 6: '按单品(SKU)' (ID: no-id, 可见)
2025-07-24 14:26:44,141 - INFO:   label 7: '自动延续:' (ID: jx-id-3352-24, 可见)
2025-07-24 14:26:44,141 - INFO: 🎯 第一步：点击'选择店铺'输入框
2025-07-24 14:26:44,141 - INFO: 等待 4.1 秒后开始操作...
2025-07-24 14:26:48,292 - INFO:   🎯 尝试点击: 级联选择器容器
2025-07-24 14:26:48,292 - INFO:      选择器: //div[contains(@class, 'jx-cascader')]
2025-07-24 14:26:48,303 - INFO:      找到 7 个匹配元素
2025-07-24 14:26:48,314 - INFO:      元素可见性: True
2025-07-24 14:26:48,908 - INFO:   ✅ 成功点击: 级联选择器容器
2025-07-24 14:26:50,931 - INFO:   🎉 下拉菜单已成功弹出！
2025-07-24 14:26:50,931 - INFO: 🎯 第二步：等待下拉菜单出现
2025-07-24 14:26:50,931 - INFO:   等待下拉菜单加载 5.9 秒...
2025-07-24 14:26:56,874 - INFO:   ✅ 下拉菜单已显示
2025-07-24 14:26:56,874 - INFO: 🎯 第三步：选择国家（泰国）
2025-07-24 14:26:56,874 - INFO:   等待国家列表加载 2.9 秒...
2025-07-24 14:26:59,751 - INFO:   🎯 尝试勾选泰国复选框: 方法1
2025-07-24 14:26:59,751 - INFO:      选择器: //li[contains(@class, 'jx-cascader-node')]//span[contains(text(), '泰国')]/../..//span[@class='jx-checkbox__inner']
2025-07-24 14:26:59,756 - INFO:      找到 1 个泰国复选框
2025-07-24 14:26:59,759 - INFO:      复选框可见性: True
2025-07-24 14:27:00,348 - INFO:   ✅ 成功勾选泰国复选框: 方法1
2025-07-24 14:27:02,355 - INFO:   🎉 泰国复选框已成功勾选！
2025-07-24 14:27:02,355 - INFO: 🎯 第四步：勾选按单品(SKU)
2025-07-24 14:27:02,355 - INFO: 等待页面稳定 2.4 秒...
2025-07-24 14:27:04,714 - INFO:   🎯 尝试勾选按单品(SKU): 方法1
2025-07-24 14:27:04,714 - INFO:      选择器: //label[contains(@class, 'jx-radio')][.//span[contains(text(), '按单品(SKU)')]]
2025-07-24 14:27:04,718 - INFO:      找到 0 个按单品(SKU)选项
2025-07-24 14:27:04,718 - WARNING:      未找到按单品(SKU)选项: 方法1
2025-07-24 14:27:04,718 - INFO:   🎯 尝试勾选按单品(SKU): 方法2
2025-07-24 14:27:04,718 - INFO:      选择器: //input[@type='radio'][@value='2']
2025-07-24 14:27:04,724 - INFO:      找到 1 个按单品(SKU)选项
2025-07-24 14:27:04,727 - INFO:      元素可见性: True
2025-07-24 14:27:35,247 - WARNING:   ❌ 勾选按单品(SKU)失败: 方法2 - ElementHandle.click: Timeout 30000ms exceeded.
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <span class="jx-radio__inner"></span> intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <span class="jx-radio__inner"></span> intercepts pointer events
    - retrying click action
      - waiting 100ms
    57 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <span class="jx-radio__inner"></span> intercepts pointer events
     - retrying click action
       - waiting 500ms

2025-07-24 14:27:35,247 - INFO:   🎯 尝试勾选按单品(SKU): 方法3
2025-07-24 14:27:35,247 - INFO:      选择器: //label[.//span[contains(text(), '按单品(SKU)')]]//span[@class='jx-radio__inner']
2025-07-24 14:27:35,250 - INFO:      找到 0 个按单品(SKU)选项
2025-07-24 14:27:35,250 - WARNING:      未找到按单品(SKU)选项: 方法3
2025-07-24 14:27:35,250 - INFO:   🎯 尝试勾选按单品(SKU): 方法4
2025-07-24 14:27:35,251 - INFO:      选择器: //span[contains(text(), '按单品(SKU)')]/..
2025-07-24 14:27:35,253 - INFO:      找到 0 个按单品(SKU)选项
2025-07-24 14:27:35,253 - WARNING:      未找到按单品(SKU)选项: 方法4
2025-07-24 14:27:35,253 - INFO:   🎯 尝试勾选按单品(SKU): 方法5
2025-07-24 14:27:35,253 - INFO:      选择器: //label[contains(@class, 'jx-radio')][contains(., '按单品(SKU)')]
2025-07-24 14:27:35,259 - INFO:      找到 1 个按单品(SKU)选项
2025-07-24 14:27:35,263 - INFO:      元素可见性: True
2025-07-24 14:27:35,818 - INFO:   ✅ 成功勾选按单品(SKU): 方法5
2025-07-24 14:27:36,826 - INFO: 🎯 第五步：勾选自动延续
2025-07-24 14:27:36,826 - INFO: 等待页面稳定 1.8 秒...
2025-07-24 14:27:38,662 - INFO:   🎯 尝试勾选自动延续: 方法1
2025-07-24 14:27:38,662 - INFO:      选择器: //div[contains(@class, 'jx-switch')]
2025-07-24 14:27:38,666 - INFO:      找到 2 个自动延续选项
2025-07-24 14:27:38,669 - INFO:      元素可见性: True
2025-07-24 14:27:39,231 - INFO:   ✅ 成功勾选自动延续: 方法1
2025-07-24 14:27:40,243 - INFO: 🎯 第六步：点击创建活动
2025-07-24 14:27:40,243 - INFO: 等待页面稳定 1.2 秒...
2025-07-24 14:27:41,498 - INFO:   🎯 尝试点击创建活动: 方法1
2025-07-24 14:27:41,498 - INFO:      选择器: //button[.//span[contains(text(), '创建活动')]]
2025-07-24 14:27:41,502 - INFO:      找到 0 个创建活动按钮
2025-07-24 14:27:41,502 - WARNING:      未找到创建活动按钮: 方法1
2025-07-24 14:27:41,502 - INFO:   🎯 尝试点击创建活动: 方法2
2025-07-24 14:27:41,502 - INFO:      选择器: //button[contains(@class, 'jx-button--primary')]
2025-07-24 14:27:41,507 - INFO:      找到 1 个创建活动按钮
2025-07-24 14:27:41,510 - INFO:      元素可见性: True
2025-07-24 14:27:42,075 - INFO:   ✅ 成功点击创建活动: 方法2
2025-07-24 14:27:44,083 - INFO: 🎉 活动创建流程已完成！
2025-07-24 14:27:44,083 - INFO: 📋 创建的活动信息:
2025-07-24 14:27:44,083 - INFO:    - 活动名称: 107 泰国汽车 3-9
2025-07-24 14:27:44,083 - INFO:    - 选择国家: 泰国
2025-07-24 14:27:44,083 - INFO:    - 产品类别: 按单品(SKU)
2025-07-24 14:27:44,083 - INFO:    - 自动延续: 已启用
2025-07-24 14:27:44,083 - INFO: 步骤4: 设置产品类别为按单品(SKU)
2025-07-24 14:27:44,084 - INFO: 设置产品类别: 按单品(SKU)
2025-07-24 14:27:44,089 - INFO: ✅ 按单品(SKU)已经被选中
2025-07-24 14:27:44,089 - INFO: 步骤5: 开启自动延续
2025-07-24 14:27:44,090 - INFO: 检查并开启自动延续开关
2025-07-24 14:27:44,090 - INFO: 尝试自动延续开关 (选择器 1)
2025-07-24 14:27:49,094 - WARNING: 自动延续选择器 1 失败: Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//input[@class='jx-switch__input' and @type='checkbox' and @role='switch']") to be visible
    15 × locator resolved to hidden <input name="" role="switch" true-value="1" type="checkbox" false-value="2" id="jx-id-3352-47" aria-checked="true" aria-disabled="false" class="jx-switch__input"/>

2025-07-24 14:27:49,094 - INFO: 尝试自动延续开关 (选择器 2)
2025-07-24 14:27:54,099 - WARNING: 自动延续选择器 2 失败: Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//div[contains(@class, 'auto-extension-label')]/following-sibling::div//input[@type='checkbox']") to be visible

2025-07-24 14:27:54,099 - INFO: 尝试自动延续开关 (选择器 3)
2025-07-24 14:27:59,108 - WARNING: 自动延续选择器 3 失败: Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//div[contains(@class, 'jx-switch')]//input[@class='jx-switch__input']") to be visible
    15 × locator resolved to hidden <input name="" role="switch" true-value="1" type="checkbox" false-value="2" id="jx-id-3352-47" aria-checked="true" aria-disabled="false" class="jx-switch__input"/>

2025-07-24 14:27:59,109 - INFO: 尝试自动延续开关 (选择器 4)
2025-07-24 14:28:04,124 - WARNING: 自动延续选择器 4 失败: Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//input[@type='checkbox' and @role='switch' and contains(@true-value, '1')]") to be visible
    15 × locator resolved to hidden <input name="" role="switch" true-value="1" type="checkbox" false-value="2" id="jx-id-3352-47" aria-checked="true" aria-disabled="false" class="jx-switch__input"/>

2025-07-24 14:28:04,125 - ERROR: 所有选择器都失败，无法操作自动延续开关
2025-07-24 14:28:04,125 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 14:39:04,949 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 14:39:04,949 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 14:39:04,950 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 14:39:04,974 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 14:39:05,298 - INFO: 启动响应: {'requestId': 'b93418ca-4ba3-4a0f-b419-fb0f6ca2f826', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 1942515, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': 'b93418ca-4ba3-4a0f-b419-fb0f6ca2f826', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 14:39:05,298 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 14:39:05,298 - INFO: 正在初始化自动化实例...
2025-07-24 14:39:05,595 - INFO: Container code: 1277086407 started.
2025-07-24 14:39:05,595 - INFO: 开始执行操作类型: create_activity
2025-07-24 14:39:05,595 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_143904
2025-07-24 14:39:05,595 - INFO: 活动持续时间: 24小时
2025-07-24 14:39:05,596 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_143904
2025-07-24 14:39:05,596 - INFO: 🧹 清理多余的标签页...
2025-07-24 14:39:05,596 - INFO: 当前共有 2 个标签页
2025-07-24 14:39:05,663 - INFO: 已关闭标签页 1
2025-07-24 14:39:05,663 - INFO: 步骤1: 直接打开创建活动页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:39:16,480 - INFO: 页面加载成功 - 标题: 妙手-创建活动
2025-07-24 14:39:16,481 - INFO: 当前URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:39:16,481 - INFO: 等待创建活动页面完全加载 3.5 秒...
2025-07-24 14:39:19,968 - INFO: 步骤2: 已跳过点击创建活动按钮（直接访问创建页面）
2025-07-24 14:39:19,968 - INFO: ✅ 已成功进入创建活动页面
2025-07-24 14:39:19,972 - INFO: ✅ 创建活动页面网络请求已完成
2025-07-24 14:39:19,974 - INFO: 创建活动页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:39:19,974 - INFO: 创建活动页面标题: 妙手-创建活动
2025-07-24 14:39:19,974 - INFO: 🔍 保存创建活动页面调试信息...
2025-07-24 14:39:19,980 - INFO: 📄 已保存HTML源码: debug_pages\create_activity_page_20250724_143919.html
2025-07-24 14:39:20,120 - INFO: 📸 已保存页面截图: debug_pages\create_activity_page_20250724_143919.png
2025-07-24 14:39:20,132 - INFO: 📋 已保存页面信息: debug_pages\create_activity_page_20250724_143919_info.txt
2025-07-24 14:39:20,133 - INFO: 🔍 验证创建活动页面加载状态...
2025-07-24 14:39:20,133 - INFO: ✅ 确认已进入创建活动页面 (通过URL检查)
2025-07-24 14:39:20,133 - INFO: ✅ 创建活动页面加载验证通过，继续执行...
2025-07-24 14:39:20,133 - INFO: 步骤3: 开始选择店铺
2025-07-24 14:39:20,133 - INFO: 等待创建活动页面完全加载...
2025-07-24 14:39:22,142 - INFO: 🔍 调试: 查看页面上的所有label元素
2025-07-24 14:39:22,146 - INFO: 找到 8 个label元素:
2025-07-24 14:39:22,147 - INFO:   🎯 重要: label 0: '选择店铺
:' (ID: jx-id-6042-13, 可见: True)
2025-07-24 14:39:22,147 - INFO:       XPath: //*[@id="jx-id-6042-13"]
2025-07-24 14:39:22,147 - INFO:   label 1: '活动名称
:' (ID: jx-id-6042-16, 可见)
2025-07-24 14:39:22,147 - INFO:   label 2: '开始时间
:' (ID: jx-id-6042-17, 可见)
2025-07-24 14:39:22,147 - INFO:   label 3: '结束时间
:' (ID: jx-id-6042-19, 可见)
2025-07-24 14:39:22,147 - INFO:   label 4: '产品类别
:' (ID: jx-id-6042-21, 可见)
2025-07-24 14:39:22,148 - INFO:   label 5: '按产品(SPU)' (ID: no-id, 可见)
2025-07-24 14:39:22,148 - INFO:   label 6: '按单品(SKU)' (ID: no-id, 可见)
2025-07-24 14:39:22,148 - INFO:   label 7: '自动延续:' (ID: jx-id-6042-24, 可见)
2025-07-24 14:39:22,148 - INFO: 🎯 第一步：点击'选择店铺'输入框
2025-07-24 14:39:22,148 - INFO: 等待 3.1 秒后开始操作...
2025-07-24 14:39:25,302 - INFO:   🎯 尝试点击: 级联选择器容器
2025-07-24 14:39:25,302 - INFO:      选择器: //div[contains(@class, 'jx-cascader')]
2025-07-24 14:39:25,312 - INFO:      找到 7 个匹配元素
2025-07-24 14:39:25,324 - INFO:      元素可见性: True
2025-07-24 14:39:25,910 - INFO:   ✅ 成功点击: 级联选择器容器
2025-07-24 14:39:27,922 - INFO:   🎉 下拉菜单已成功弹出！
2025-07-24 14:39:27,922 - INFO: 🎯 第二步：等待下拉菜单出现
2025-07-24 14:39:27,922 - INFO:   等待下拉菜单加载 3.9 秒...
2025-07-24 14:39:31,859 - INFO:   ✅ 下拉菜单已显示
2025-07-24 14:39:31,859 - INFO: 🎯 第三步：选择第一个可用的国家/站点
2025-07-24 14:39:31,859 - INFO:   等待国家列表加载 3.9 秒...
2025-07-24 14:39:35,729 - INFO:   🔍 查找所有可用的国家选项...
2025-07-24 14:39:35,734 - INFO:   找到 2 个级联节点
2025-07-24 14:39:35,741 - WARNING:   处理国家节点 1 失败: ElementHandle.query_selector: Unsupported token "@class" while parsing css selector "span[contains(@class, 'jx-tooltip__trigger')]". Did you mean to CSS.escape it?
2025-07-24 14:39:35,746 - WARNING:   处理国家节点 2 失败: ElementHandle.query_selector: Unsupported token "@class" while parsing css selector "span[contains(@class, 'jx-tooltip__trigger')]". Did you mean to CSS.escape it?
2025-07-24 14:39:35,746 - ERROR:   ❌ 所有国家选项都无法点击
2025-07-24 14:39:35,746 - ERROR: ❌ 第三步失败：无法选择任何国家
2025-07-24 14:39:35,746 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 14:41:20,936 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 14:41:20,937 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 14:41:20,937 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 14:41:20,943 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 14:41:21,206 - INFO: 启动响应: {'requestId': '31d33a9b-48ae-4b05-9492-7dd8d1a72fa4', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 2078428, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '31d33a9b-48ae-4b05-9492-7dd8d1a72fa4', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 14:41:21,206 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 14:41:21,206 - INFO: 正在初始化自动化实例...
2025-07-24 14:41:21,505 - INFO: Container code: 1277086407 started.
2025-07-24 14:41:21,506 - INFO: 开始执行操作类型: create_activity
2025-07-24 14:41:21,506 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_144120
2025-07-24 14:41:21,506 - INFO: 活动持续时间: 24小时
2025-07-24 14:41:21,506 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_144120
2025-07-24 14:41:21,506 - INFO: 🧹 清理多余的标签页...
2025-07-24 14:41:21,506 - INFO: 当前共有 2 个标签页
2025-07-24 14:41:21,534 - INFO: 已关闭标签页 1
2025-07-24 14:41:21,534 - INFO: 步骤1: 直接打开创建活动页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:41:28,818 - INFO: 页面加载成功 - 标题: 妙手-创建活动
2025-07-24 14:41:28,818 - INFO: 当前URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:41:28,818 - INFO: 等待创建活动页面完全加载 3.8 秒...
2025-07-24 14:41:32,641 - INFO: 步骤2: 已跳过点击创建活动按钮（直接访问创建页面）
2025-07-24 14:41:32,641 - INFO: ✅ 已成功进入创建活动页面
2025-07-24 14:41:32,645 - INFO: ✅ 创建活动页面网络请求已完成
2025-07-24 14:41:32,647 - INFO: 创建活动页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:41:32,648 - INFO: 创建活动页面标题: 妙手-创建活动
2025-07-24 14:41:32,648 - INFO: 🔍 保存创建活动页面调试信息...
2025-07-24 14:41:32,655 - INFO: 📄 已保存HTML源码: debug_pages\create_activity_page_20250724_144132.html
2025-07-24 14:41:32,829 - INFO: 📸 已保存页面截图: debug_pages\create_activity_page_20250724_144132.png
2025-07-24 14:41:32,840 - INFO: 📋 已保存页面信息: debug_pages\create_activity_page_20250724_144132_info.txt
2025-07-24 14:41:32,841 - INFO: 🔍 验证创建活动页面加载状态...
2025-07-24 14:41:32,841 - INFO: ✅ 确认已进入创建活动页面 (通过URL检查)
2025-07-24 14:41:32,841 - INFO: ✅ 创建活动页面加载验证通过，继续执行...
2025-07-24 14:41:32,841 - INFO: 步骤3: 开始选择店铺
2025-07-24 14:41:32,841 - INFO: 等待创建活动页面完全加载...
2025-07-24 14:41:34,846 - INFO: 🔍 调试: 查看页面上的所有label元素
2025-07-24 14:41:34,850 - INFO: 找到 8 个label元素:
2025-07-24 14:41:34,850 - INFO:   🎯 重要: label 0: '选择店铺
:' (ID: jx-id-3709-13, 可见: True)
2025-07-24 14:41:34,851 - INFO:       XPath: //*[@id="jx-id-3709-13"]
2025-07-24 14:41:34,851 - INFO:   label 1: '活动名称
:' (ID: jx-id-3709-16, 可见)
2025-07-24 14:41:34,851 - INFO:   label 2: '开始时间
:' (ID: jx-id-3709-17, 可见)
2025-07-24 14:41:34,851 - INFO:   label 3: '结束时间
:' (ID: jx-id-3709-19, 可见)
2025-07-24 14:41:34,851 - INFO:   label 4: '产品类别
:' (ID: jx-id-3709-21, 可见)
2025-07-24 14:41:34,851 - INFO:   label 5: '按产品(SPU)' (ID: no-id, 可见)
2025-07-24 14:41:34,851 - INFO:   label 6: '按单品(SKU)' (ID: no-id, 可见)
2025-07-24 14:41:34,851 - INFO:   label 7: '自动延续:' (ID: jx-id-3709-24, 可见)
2025-07-24 14:41:34,851 - INFO: 🎯 第一步：点击'选择店铺'输入框
2025-07-24 14:41:34,852 - INFO: 等待 3.9 秒后开始操作...
2025-07-24 14:41:38,720 - INFO:   🎯 尝试点击: 级联选择器容器
2025-07-24 14:41:38,720 - INFO:      选择器: //div[contains(@class, 'jx-cascader')]
2025-07-24 14:41:38,731 - INFO:      找到 7 个匹配元素
2025-07-24 14:41:38,761 - INFO:      元素可见性: True
2025-07-24 14:41:39,373 - INFO:   ✅ 成功点击: 级联选择器容器
2025-07-24 14:41:41,384 - INFO:   🎉 下拉菜单已成功弹出！
2025-07-24 14:41:41,384 - INFO: 🎯 第二步：等待下拉菜单出现
2025-07-24 14:41:41,384 - INFO:   等待下拉菜单加载 5.4 秒...
2025-07-24 14:41:46,788 - INFO:   ✅ 下拉菜单已显示
2025-07-24 14:41:46,788 - INFO: 🎯 第三步：选择第一个可用的国家/站点
2025-07-24 14:41:46,788 - INFO:   等待国家列表加载 2.8 秒...
2025-07-24 14:41:49,589 - INFO:   🔍 查找所有可用的国家选项...
2025-07-24 14:41:49,595 - INFO:   找到 2 个级联节点
2025-07-24 14:41:49,610 - INFO:   🌍 找到国家选项 1: 泰国
2025-07-24 14:41:50,275 - INFO:   ✅ 成功选择国家: 泰国 (方法1)
2025-07-24 14:41:52,282 - INFO:   🎉 已选择国家: 泰国
2025-07-24 14:41:52,282 - INFO: 步骤4: 设置产品类别为按单品(SKU)
2025-07-24 14:41:52,282 - INFO: 设置产品类别: 按单品(SKU)
2025-07-24 14:42:22,299 - INFO: 尝试选择按单品(SKU) (选择器 1)
2025-07-24 14:42:22,334 - INFO: ✅ 成功选择按单品(SKU)
2025-07-24 14:42:22,334 - INFO: 等待选择生效 1.3 秒...
2025-07-24 14:42:23,676 - INFO: 步骤5: 开启自动延续
2025-07-24 14:42:23,676 - INFO: 检查并开启自动延续开关
2025-07-24 14:42:23,676 - INFO: 尝试自动延续开关 (选择器 1)
2025-07-24 14:42:28,678 - WARNING: 自动延续选择器 1 失败: Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//input[@class='jx-switch__input' and @type='checkbox' and @role='switch']") to be visible
    15 × locator resolved to hidden <input name="" role="switch" true-value="1" type="checkbox" false-value="2" id="jx-id-3709-47" aria-checked="true" aria-disabled="false" class="jx-switch__input"/>

2025-07-24 14:42:28,678 - INFO: 尝试自动延续开关 (选择器 2)
2025-07-24 14:42:33,691 - WARNING: 自动延续选择器 2 失败: Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//div[contains(@class, 'auto-extension-label')]/following-sibling::div//input[@type='checkbox']") to be visible

2025-07-24 14:42:33,691 - INFO: 尝试自动延续开关 (选择器 3)
2025-07-24 14:42:38,706 - WARNING: 自动延续选择器 3 失败: Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//div[contains(@class, 'jx-switch')]//input[@class='jx-switch__input']") to be visible
    15 × locator resolved to hidden <input name="" role="switch" true-value="1" type="checkbox" false-value="2" id="jx-id-3709-47" aria-checked="true" aria-disabled="false" class="jx-switch__input"/>

2025-07-24 14:42:38,707 - INFO: 尝试自动延续开关 (选择器 4)
2025-07-24 14:42:43,716 - WARNING: 自动延续选择器 4 失败: Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//input[@type='checkbox' and @role='switch' and contains(@true-value, '1')]") to be visible
    15 × locator resolved to hidden <input name="" role="switch" true-value="1" type="checkbox" false-value="2" id="jx-id-3709-47" aria-checked="true" aria-disabled="false" class="jx-switch__input"/>

2025-07-24 14:42:43,717 - ERROR: 所有选择器都失败，无法操作自动延续开关
2025-07-24 14:42:43,717 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 14:47:33,186 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 14:47:33,187 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 14:47:33,187 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 14:47:33,210 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 14:47:33,477 - INFO: 启动响应: {'requestId': '67a36332-d011-45b6-a845-72f2a391e772', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 2450702, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '67a36332-d011-45b6-a845-72f2a391e772', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 14:47:33,477 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 14:47:33,477 - INFO: 正在初始化自动化实例...
2025-07-24 14:47:33,780 - INFO: Container code: 1277086407 started.
2025-07-24 14:47:33,780 - INFO: 开始执行操作类型: create_activity
2025-07-24 14:47:33,780 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_144733
2025-07-24 14:47:33,780 - INFO: 活动持续时间: 24小时
2025-07-24 14:47:33,781 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_144733
2025-07-24 14:47:33,781 - INFO: 🧹 清理多余的标签页...
2025-07-24 14:47:33,781 - INFO: 当前共有 3 个标签页
2025-07-24 14:47:33,814 - INFO: 已关闭标签页 1
2025-07-24 14:47:33,839 - INFO: 已关闭标签页 2
2025-07-24 14:47:33,839 - INFO: 步骤1: 直接打开创建活动页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:47:38,748 - INFO: 页面加载成功 - 标题: 妙手-创建活动
2025-07-24 14:47:38,748 - INFO: 当前URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:47:38,748 - INFO: 等待创建活动页面完全加载 3.7 秒...
2025-07-24 14:47:42,500 - INFO: 步骤2: 已跳过点击创建活动按钮（直接访问创建页面）
2025-07-24 14:47:42,500 - INFO: ✅ 已成功进入创建活动页面
2025-07-24 14:47:57,508 - WARNING: 等待网络请求完成失败，继续执行: Timeout 15000ms exceeded.
2025-07-24 14:47:57,511 - INFO: 创建活动页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:47:57,511 - INFO: 创建活动页面标题: 妙手-创建活动
2025-07-24 14:47:57,511 - INFO: 🔍 保存创建活动页面调试信息...
2025-07-24 14:47:57,518 - INFO: 📄 已保存HTML源码: debug_pages\create_activity_page_20250724_144757.html
2025-07-24 14:47:57,661 - INFO: 📸 已保存页面截图: debug_pages\create_activity_page_20250724_144757.png
2025-07-24 14:47:57,673 - INFO: 📋 已保存页面信息: debug_pages\create_activity_page_20250724_144757_info.txt
2025-07-24 14:47:57,673 - INFO: 🔍 验证创建活动页面加载状态...
2025-07-24 14:47:57,673 - INFO: ✅ 确认已进入创建活动页面 (通过URL检查)
2025-07-24 14:47:57,673 - INFO: ✅ 创建活动页面加载验证通过，继续执行...
2025-07-24 14:47:57,674 - INFO: 步骤3: 开始选择店铺
2025-07-24 14:47:57,674 - INFO: 等待创建活动页面完全加载...
2025-07-24 14:47:59,688 - INFO: 🔍 调试: 查看页面上的所有label元素
2025-07-24 14:47:59,691 - INFO: 找到 8 个label元素:
2025-07-24 14:47:59,691 - INFO:   🎯 重要: label 0: '选择店铺
:' (ID: jx-id-6421-13, 可见: True)
2025-07-24 14:47:59,692 - INFO:       XPath: //*[@id="jx-id-6421-13"]
2025-07-24 14:47:59,692 - INFO:   label 1: '活动名称
:' (ID: jx-id-6421-16, 可见)
2025-07-24 14:47:59,692 - INFO:   label 2: '开始时间
:' (ID: jx-id-6421-17, 可见)
2025-07-24 14:47:59,692 - INFO:   label 3: '结束时间
:' (ID: jx-id-6421-19, 可见)
2025-07-24 14:47:59,692 - INFO:   label 4: '产品类别
:' (ID: jx-id-6421-21, 可见)
2025-07-24 14:47:59,692 - INFO:   label 5: '按产品(SPU)' (ID: no-id, 可见)
2025-07-24 14:47:59,692 - INFO:   label 6: '按单品(SKU)' (ID: no-id, 可见)
2025-07-24 14:47:59,692 - INFO:   label 7: '自动延续:' (ID: jx-id-6421-24, 可见)
2025-07-24 14:47:59,692 - INFO: 🎯 第一步：点击'选择店铺'输入框
2025-07-24 14:47:59,692 - INFO: 等待 4.7 秒后开始操作...
2025-07-24 14:48:04,395 - INFO:   🎯 尝试点击: 级联选择器容器
2025-07-24 14:48:04,395 - INFO:      选择器: //div[contains(@class, 'jx-cascader')]
2025-07-24 14:48:04,404 - INFO:      找到 7 个匹配元素
2025-07-24 14:48:04,413 - INFO:      元素可见性: True
2025-07-24 14:48:04,977 - INFO:   ✅ 成功点击: 级联选择器容器
2025-07-24 14:48:07,002 - INFO:   🎉 下拉菜单已成功弹出！
2025-07-24 14:48:07,002 - INFO: 🎯 第二步：等待下拉菜单出现
2025-07-24 14:48:07,002 - INFO:   等待下拉菜单加载 4.0 秒...
2025-07-24 14:48:11,017 - INFO:   ✅ 下拉菜单已显示
2025-07-24 14:48:11,018 - INFO: 🎯 第三步：选择第一个可用的国家/站点
2025-07-24 14:48:11,018 - INFO:   等待国家列表加载 3.0 秒...
2025-07-24 14:48:14,050 - INFO:   🔍 查找所有可用的国家选项...
2025-07-24 14:48:14,054 - INFO:   找到 2 个级联节点
2025-07-24 14:48:14,066 - INFO:   🌍 找到国家选项 1: 马来
2025-07-24 14:48:14,643 - INFO:   ✅ 成功选择国家: 马来 (方法1)
2025-07-24 14:48:16,658 - INFO:   🎉 已选择国家: 马来
2025-07-24 14:48:16,658 - INFO: 步骤4: 设置产品类别为按单品(SKU)
2025-07-24 14:48:16,658 - INFO: 设置产品类别: 按单品(SKU)
2025-07-24 14:48:46,676 - INFO: 尝试选择按单品(SKU) (选择器 1)
2025-07-24 14:48:46,681 - INFO: ✅ 成功选择按单品(SKU)
2025-07-24 14:48:46,681 - INFO: 等待选择生效 1.7 秒...
2025-07-24 14:48:48,375 - INFO: 步骤5: 开启自动延续
2025-07-24 14:48:48,375 - INFO: 检查并开启自动延续开关
2025-07-24 14:48:48,375 - INFO: 检查自动延续开关 (选择器 1)
2025-07-24 14:48:48,386 - INFO: 自动延续状态: is_checked=True, aria-checked=true
2025-07-24 14:48:48,386 - INFO: ✅ 自动延续已经开启
2025-07-24 14:48:48,387 - INFO: 步骤6: 点击创建活动
2025-07-24 14:48:48,387 - INFO: 准备提交创建活动
2025-07-24 14:48:48,387 - INFO: 尝试点击创建活动按钮 (选择器 1)
2025-07-24 14:48:53,396 - ERROR: 点击元素时发生异常 (尝试 1/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//button[contains(text(), '创建活动') and not(contains(text(), '取消'))]") to be visible

2025-07-24 14:48:58,408 - ERROR: 点击元素时发生异常 (尝试 2/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//button[contains(text(), '创建活动') and not(contains(text(), '取消'))]") to be visible

2025-07-24 14:49:03,410 - ERROR: 点击元素时发生异常 (尝试 3/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//button[contains(text(), '创建活动') and not(contains(text(), '取消'))]") to be visible

2025-07-24 14:49:03,410 - ERROR: 点击元素失败，超过重试次数: 创建活动按钮(选择器1)
2025-07-24 14:49:03,410 - INFO: 尝试点击创建活动按钮 (选择器 2)
2025-07-24 14:49:08,417 - ERROR: 点击元素时发生异常 (尝试 1/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//span[contains(text(), '创建活动') and not(contains(text(), '取消'))]/parent::button") to be visible

2025-07-24 14:49:13,424 - ERROR: 点击元素时发生异常 (尝试 2/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//span[contains(text(), '创建活动') and not(contains(text(), '取消'))]/parent::button") to be visible

2025-07-24 14:49:18,427 - ERROR: 点击元素时发生异常 (尝试 3/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//span[contains(text(), '创建活动') and not(contains(text(), '取消'))]/parent::button") to be visible

2025-07-24 14:49:18,427 - ERROR: 点击元素失败，超过重试次数: 创建活动按钮(选择器2)
2025-07-24 14:49:18,428 - INFO: 尝试点击创建活动按钮 (选择器 3)
2025-07-24 14:49:18,444 - INFO: 成功点击元素: 创建活动按钮(选择器3)
2025-07-24 14:49:18,444 - INFO: 等待活动创建处理 2.4 秒...
2025-07-24 14:49:20,902 - INFO: 提交后页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:49:20,932 - INFO: ✅ 活动创建请求已提交
2025-07-24 14:49:20,932 - INFO: ✅ 限时秒杀活动创建流程完成
2025-07-24 14:49:20,932 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 14:51:01,264 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 14:51:01,265 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 14:51:01,265 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 14:51:01,291 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 14:51:01,630 - INFO: 启动响应: {'requestId': 'c85d7ae6-9860-4085-8e85-ed79739a26e7', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 2658854, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': 'c85d7ae6-9860-4085-8e85-ed79739a26e7', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 14:51:01,630 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 14:51:01,630 - INFO: 正在初始化自动化实例...
2025-07-24 14:51:01,925 - INFO: Container code: 1277086407 started.
2025-07-24 14:51:01,925 - INFO: 开始执行操作类型: create_activity
2025-07-24 14:51:01,925 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_145101
2025-07-24 14:51:01,925 - INFO: 活动持续时间: 24小时
2025-07-24 14:51:01,925 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_145101
2025-07-24 14:51:01,926 - INFO: 🧹 清理多余的标签页...
2025-07-24 14:51:01,926 - INFO: 当前共有 2 个标签页
2025-07-24 14:51:01,962 - INFO: 已关闭标签页 1
2025-07-24 14:51:01,962 - INFO: 步骤1: 直接打开创建活动页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:51:09,233 - INFO: 页面加载成功 - 标题: 妙手-创建活动
2025-07-24 14:51:09,233 - INFO: 当前URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:51:09,233 - INFO: 等待创建活动页面完全加载 4.2 秒...
2025-07-24 14:51:13,471 - INFO: 步骤2: 已跳过点击创建活动按钮（直接访问创建页面）
2025-07-24 14:51:13,471 - INFO: ✅ 已成功进入创建活动页面
2025-07-24 14:51:13,475 - INFO: ✅ 创建活动页面网络请求已完成
2025-07-24 14:51:13,478 - INFO: 创建活动页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:51:13,478 - INFO: 创建活动页面标题: 妙手-创建活动
2025-07-24 14:51:13,478 - INFO: 🔍 保存创建活动页面调试信息...
2025-07-24 14:51:13,484 - INFO: 📄 已保存HTML源码: debug_pages\create_activity_page_20250724_145113.html
2025-07-24 14:51:13,638 - INFO: 📸 已保存页面截图: debug_pages\create_activity_page_20250724_145113.png
2025-07-24 14:51:13,648 - INFO: 📋 已保存页面信息: debug_pages\create_activity_page_20250724_145113_info.txt
2025-07-24 14:51:13,648 - INFO: 🔍 验证创建活动页面加载状态...
2025-07-24 14:51:13,648 - INFO: ✅ 确认已进入创建活动页面 (通过URL检查)
2025-07-24 14:51:13,648 - INFO: ✅ 创建活动页面加载验证通过，继续执行...
2025-07-24 14:51:13,649 - INFO: 步骤3: 开始选择店铺
2025-07-24 14:51:13,649 - INFO: 等待创建活动页面完全加载...
2025-07-24 14:51:15,653 - INFO: 🔍 调试: 查看页面上的所有label元素
2025-07-24 14:51:15,656 - INFO: 找到 8 个label元素:
2025-07-24 14:51:15,657 - INFO:   🎯 重要: label 0: '选择店铺
:' (ID: jx-id-4596-13, 可见: True)
2025-07-24 14:51:15,657 - INFO:       XPath: //*[@id="jx-id-4596-13"]
2025-07-24 14:51:15,657 - INFO:   label 1: '活动名称
:' (ID: jx-id-4596-16, 可见)
2025-07-24 14:51:15,657 - INFO:   label 2: '开始时间
:' (ID: jx-id-4596-17, 可见)
2025-07-24 14:51:15,657 - INFO:   label 3: '结束时间
:' (ID: jx-id-4596-19, 可见)
2025-07-24 14:51:15,657 - INFO:   label 4: '产品类别
:' (ID: jx-id-4596-21, 可见)
2025-07-24 14:51:15,657 - INFO:   label 5: '按产品(SPU)' (ID: no-id, 可见)
2025-07-24 14:51:15,657 - INFO:   label 6: '按单品(SKU)' (ID: no-id, 可见)
2025-07-24 14:51:15,657 - INFO:   label 7: '自动延续:' (ID: jx-id-4596-24, 可见)
2025-07-24 14:51:15,658 - INFO: 🎯 第一步：点击'选择店铺'输入框
2025-07-24 14:51:15,658 - INFO: 等待 2.7 秒后开始操作...
2025-07-24 14:51:18,328 - INFO:   🎯 尝试点击: 级联选择器容器
2025-07-24 14:51:18,328 - INFO:      选择器: //div[contains(@class, 'jx-cascader')]
2025-07-24 14:51:18,350 - INFO:      找到 7 个匹配元素
2025-07-24 14:51:18,382 - INFO:      元素可见性: True
2025-07-24 14:51:18,976 - INFO:   ✅ 成功点击: 级联选择器容器
2025-07-24 14:51:20,996 - INFO:   🎉 下拉菜单已成功弹出！
2025-07-24 14:51:20,996 - INFO: 🎯 第二步：等待下拉菜单出现
2025-07-24 14:51:20,996 - INFO:   等待下拉菜单加载 6.0 秒...
2025-07-24 14:51:27,013 - INFO:   ✅ 下拉菜单已显示
2025-07-24 14:51:27,013 - INFO: 🎯 第三步：选择第一个可用的国家/站点
2025-07-24 14:51:27,013 - INFO:   等待国家列表加载 3.0 秒...
2025-07-24 14:51:30,031 - INFO:   🔍 查找所有可用的国家选项...
2025-07-24 14:51:30,037 - INFO:   找到 2 个级联节点
2025-07-24 14:51:30,052 - INFO:   🌍 找到国家选项 1: 泰国
2025-07-24 14:51:30,650 - INFO:   ✅ 成功选择国家: 泰国 (方法1)
2025-07-24 14:51:32,652 - INFO:   🎉 已选择国家: 泰国
2025-07-24 14:51:32,652 - INFO: 步骤4: 设置产品类别为按单品(SKU)
2025-07-24 14:51:32,652 - INFO: 设置产品类别: 按单品(SKU)
2025-07-24 14:52:02,673 - INFO: 尝试选择按单品(SKU) (选择器 1)
2025-07-24 14:52:02,678 - INFO: ✅ 成功选择按单品(SKU)
2025-07-24 14:52:02,678 - INFO: 等待选择生效 1.7 秒...
2025-07-24 14:52:04,362 - INFO: 步骤5: 开启自动延续
2025-07-24 14:52:04,362 - INFO: 检查并开启自动延续开关
2025-07-24 14:52:04,362 - INFO: 检查自动延续开关 (选择器 1)
2025-07-24 14:52:04,374 - INFO: 自动延续状态: is_checked=True, aria-checked=true
2025-07-24 14:52:04,374 - INFO: ✅ 自动延续已经开启
2025-07-24 14:52:04,375 - INFO: 步骤6: 点击创建活动
2025-07-24 14:52:04,375 - INFO: 准备提交创建活动
2025-07-24 14:52:04,375 - INFO: 尝试点击创建活动按钮 (选择器 1)
2025-07-24 14:52:09,387 - ERROR: 点击元素时发生异常 (尝试 1/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//button[contains(text(), '创建活动') and not(contains(text(), '取消'))]") to be visible

2025-07-24 14:52:14,395 - ERROR: 点击元素时发生异常 (尝试 2/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//button[contains(text(), '创建活动') and not(contains(text(), '取消'))]") to be visible

2025-07-24 14:52:19,402 - ERROR: 点击元素时发生异常 (尝试 3/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//button[contains(text(), '创建活动') and not(contains(text(), '取消'))]") to be visible

2025-07-24 14:52:19,402 - ERROR: 点击元素失败，超过重试次数: 创建活动按钮(选择器1)
2025-07-24 14:52:19,403 - INFO: 尝试点击创建活动按钮 (选择器 2)
2025-07-24 14:52:24,417 - ERROR: 点击元素时发生异常 (尝试 1/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//span[contains(text(), '创建活动') and not(contains(text(), '取消'))]/parent::button") to be visible

2025-07-24 14:52:29,429 - ERROR: 点击元素时发生异常 (尝试 2/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//span[contains(text(), '创建活动') and not(contains(text(), '取消'))]/parent::button") to be visible

2025-07-24 14:52:34,431 - ERROR: 点击元素时发生异常 (尝试 3/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//span[contains(text(), '创建活动') and not(contains(text(), '取消'))]/parent::button") to be visible

2025-07-24 14:52:34,431 - ERROR: 点击元素失败，超过重试次数: 创建活动按钮(选择器2)
2025-07-24 14:52:34,432 - INFO: 尝试点击创建活动按钮 (选择器 3)
2025-07-24 14:52:34,454 - INFO: 成功点击元素: 创建活动按钮(选择器3)
2025-07-24 14:52:34,454 - INFO: 等待活动创建处理 2.5 秒...
2025-07-24 14:52:36,911 - INFO: 提交后页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:52:36,941 - INFO: ✅ 活动创建请求已提交
2025-07-24 14:52:36,941 - INFO: ✅ 限时秒杀活动创建流程完成
2025-07-24 14:52:36,942 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 14:55:09,031 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 14:55:09,031 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 14:55:09,031 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 14:55:09,059 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 14:55:09,409 - INFO: 启动响应: {'requestId': 'c2b197e4-eb25-4b9a-bb4f-46ab632317d2', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 2906633, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': 'c2b197e4-eb25-4b9a-bb4f-46ab632317d2', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 14:55:09,409 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 14:55:09,409 - INFO: 正在初始化自动化实例...
2025-07-24 14:55:09,696 - INFO: Container code: 1277086407 started.
2025-07-24 14:55:09,697 - INFO: 开始执行操作类型: create_activity
2025-07-24 14:55:09,697 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_145509
2025-07-24 14:55:09,697 - INFO: 活动持续时间: 24小时
2025-07-24 14:55:09,697 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_145509
2025-07-24 14:55:09,697 - INFO: 🧹 清理多余的标签页...
2025-07-24 14:55:09,697 - INFO: 当前共有 2 个标签页
2025-07-24 14:55:09,739 - INFO: 已关闭标签页 1
2025-07-24 14:55:09,739 - INFO: 步骤1: 直接打开创建活动页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:55:16,605 - INFO: 页面加载成功 - 标题: 妙手-创建活动
2025-07-24 14:55:16,606 - INFO: 当前URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:55:16,606 - INFO: 等待创建活动页面完全加载 3.6 秒...
2025-07-24 14:55:20,213 - INFO: 步骤2: 已跳过点击创建活动按钮（直接访问创建页面）
2025-07-24 14:55:20,213 - INFO: ✅ 已成功进入创建活动页面
2025-07-24 14:55:20,215 - INFO: ✅ 创建活动页面网络请求已完成
2025-07-24 14:55:20,218 - INFO: 创建活动页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:55:20,218 - INFO: 创建活动页面标题: 妙手-创建活动
2025-07-24 14:55:20,218 - INFO: 🔍 保存创建活动页面调试信息...
2025-07-24 14:55:20,227 - INFO: 📄 已保存HTML源码: debug_pages\create_activity_page_20250724_145520.html
2025-07-24 14:55:20,396 - INFO: 📸 已保存页面截图: debug_pages\create_activity_page_20250724_145520.png
2025-07-24 14:55:20,405 - INFO: 📋 已保存页面信息: debug_pages\create_activity_page_20250724_145520_info.txt
2025-07-24 14:55:20,405 - INFO: 🔍 验证创建活动页面加载状态...
2025-07-24 14:55:20,406 - INFO: ✅ 确认已进入创建活动页面 (通过URL检查)
2025-07-24 14:55:20,406 - INFO: ✅ 创建活动页面加载验证通过，继续执行...
2025-07-24 14:55:20,406 - INFO: 步骤3: 开始选择店铺
2025-07-24 14:55:20,406 - INFO: 等待创建活动页面完全加载...
2025-07-24 14:55:22,414 - INFO: 🔍 调试: 查看页面上的所有label元素
2025-07-24 14:55:22,417 - INFO: 找到 8 个label元素:
2025-07-24 14:55:22,417 - INFO:   🎯 重要: label 0: '选择店铺
:' (ID: jx-id-6660-13, 可见: True)
2025-07-24 14:55:22,417 - INFO:       XPath: //*[@id="jx-id-6660-13"]
2025-07-24 14:55:22,417 - INFO:   label 1: '活动名称
:' (ID: jx-id-6660-16, 可见)
2025-07-24 14:55:22,417 - INFO:   label 2: '开始时间
:' (ID: jx-id-6660-17, 可见)
2025-07-24 14:55:22,417 - INFO:   label 3: '结束时间
:' (ID: jx-id-6660-19, 可见)
2025-07-24 14:55:22,418 - INFO:   label 4: '产品类别
:' (ID: jx-id-6660-21, 可见)
2025-07-24 14:55:22,418 - INFO:   label 5: '按产品(SPU)' (ID: no-id, 可见)
2025-07-24 14:55:22,418 - INFO:   label 6: '按单品(SKU)' (ID: no-id, 可见)
2025-07-24 14:55:22,418 - INFO:   label 7: '自动延续:' (ID: jx-id-6660-24, 可见)
2025-07-24 14:55:22,418 - INFO: 🎯 第一步：点击'选择店铺'输入框
2025-07-24 14:55:22,418 - INFO: 等待 3.4 秒后开始操作...
2025-07-24 14:55:25,847 - INFO:   🎯 尝试点击: 级联选择器容器
2025-07-24 14:55:25,847 - INFO:      选择器: //div[contains(@class, 'jx-cascader')]
2025-07-24 14:55:25,857 - INFO:      找到 7 个匹配元素
2025-07-24 14:55:25,867 - INFO:      元素可见性: True
2025-07-24 14:55:26,460 - INFO:   ✅ 成功点击: 级联选择器容器
2025-07-24 14:55:28,475 - INFO:   🎉 下拉菜单已成功弹出！
2025-07-24 14:55:28,475 - INFO: 🎯 第二步：等待下拉菜单出现
2025-07-24 14:55:28,475 - INFO:   等待下拉菜单加载 4.5 秒...
2025-07-24 14:55:32,972 - INFO:   ✅ 下拉菜单已显示
2025-07-24 14:55:32,973 - INFO: 🎯 第三步：选择第一个可用的国家/站点
2025-07-24 14:55:32,973 - INFO:   等待国家列表加载 3.0 秒...
2025-07-24 14:55:35,954 - INFO:   🔍 查找所有可用的国家选项...
2025-07-24 14:55:35,958 - INFO:   找到 2 个级联节点
2025-07-24 14:55:35,970 - INFO:   🌍 找到国家选项 1: 泰国
2025-07-24 14:55:36,557 - INFO:   ✅ 成功选择国家: 泰国 (方法1)
2025-07-24 14:55:38,567 - INFO:   🎉 已选择国家: 泰国
2025-07-24 14:55:38,567 - INFO: 步骤4: 设置产品类别为按单品(SKU)
2025-07-24 14:55:38,567 - INFO: 设置产品类别: 按单品(SKU)
2025-07-24 14:56:08,589 - INFO: 尝试选择按单品(SKU) (选择器 1)
2025-07-24 14:56:08,594 - INFO: ✅ 成功选择按单品(SKU)
2025-07-24 14:56:08,594 - INFO: 等待选择生效 1.6 秒...
2025-07-24 14:56:10,191 - INFO: 步骤5: 开启自动延续
2025-07-24 14:56:10,191 - INFO: 检查并开启自动延续开关
2025-07-24 14:56:10,191 - INFO: 检查自动延续开关 (选择器 1)
2025-07-24 14:56:10,200 - INFO: 自动延续状态: is_checked=True, aria-checked=true
2025-07-24 14:56:10,200 - INFO: ✅ 自动延续已经开启
2025-07-24 14:56:10,200 - INFO: 步骤6: 点击创建活动
2025-07-24 14:56:10,200 - INFO: 准备提交创建活动
2025-07-24 14:56:10,200 - INFO: 尝试点击创建活动按钮 (选择器 1)
2025-07-24 14:56:15,201 - ERROR: 点击元素时发生异常 (尝试 1/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//button[contains(text(), '创建活动') and not(contains(text(), '取消'))]") to be visible

2025-07-24 14:56:20,213 - ERROR: 点击元素时发生异常 (尝试 2/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//button[contains(text(), '创建活动') and not(contains(text(), '取消'))]") to be visible

2025-07-24 14:56:25,227 - ERROR: 点击元素时发生异常 (尝试 3/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//button[contains(text(), '创建活动') and not(contains(text(), '取消'))]") to be visible

2025-07-24 14:56:25,227 - ERROR: 点击元素失败，超过重试次数: 创建活动按钮(选择器1)
2025-07-24 14:56:25,227 - INFO: 尝试点击创建活动按钮 (选择器 2)
2025-07-24 14:56:30,232 - ERROR: 点击元素时发生异常 (尝试 1/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//span[contains(text(), '创建活动') and not(contains(text(), '取消'))]/parent::button") to be visible

2025-07-24 14:56:35,246 - ERROR: 点击元素时发生异常 (尝试 2/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//span[contains(text(), '创建活动') and not(contains(text(), '取消'))]/parent::button") to be visible

2025-07-24 14:56:40,257 - ERROR: 点击元素时发生异常 (尝试 3/3): Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//span[contains(text(), '创建活动') and not(contains(text(), '取消'))]/parent::button") to be visible

2025-07-24 14:56:40,257 - ERROR: 点击元素失败，超过重试次数: 创建活动按钮(选择器2)
2025-07-24 14:56:40,257 - INFO: 尝试点击创建活动按钮 (选择器 3)
2025-07-24 14:56:40,276 - INFO: 成功点击元素: 创建活动按钮(选择器3)
2025-07-24 14:56:40,276 - INFO: 等待活动创建处理...
2025-07-24 14:56:40,276 - INFO: 等待活动创建处理 3 秒... (尝试 1/3)
2025-07-24 14:56:43,282 - INFO: 当前页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:56:43,290 - INFO: ✅ 创建按钮已消失，可能创建成功
2025-07-24 14:56:43,305 - INFO: ✅ 活动创建成功
2025-07-24 14:56:43,306 - INFO: ✅ 限时秒杀活动创建流程完成
2025-07-24 14:56:43,306 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 14:58:41,762 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 14:58:41,762 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 14:58:41,763 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 14:58:41,772 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 14:58:42,112 - INFO: 启动响应: {'requestId': '8b7e765d-e642-4f82-b1f3-fafba5882d7c', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 3119335, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '8b7e765d-e642-4f82-b1f3-fafba5882d7c', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 14:58:42,112 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 14:58:42,112 - INFO: 正在初始化自动化实例...
2025-07-24 14:58:42,410 - INFO: Container code: 1277086407 started.
2025-07-24 14:58:42,410 - INFO: 开始执行操作类型: create_activity
2025-07-24 14:58:42,410 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_145841
2025-07-24 14:58:42,410 - INFO: 活动持续时间: 24小时
2025-07-24 14:58:42,410 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_145841
2025-07-24 14:58:42,410 - INFO: 🧹 清理多余的标签页...
2025-07-24 14:58:42,410 - INFO: 当前共有 2 个标签页
2025-07-24 14:58:42,421 - INFO: 已关闭标签页 1
2025-07-24 14:58:42,422 - INFO: 步骤1: 直接打开创建活动页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:58:53,945 - INFO: 页面加载成功 - 标题: 妙手-创建活动
2025-07-24 14:58:53,945 - INFO: 当前URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:58:53,945 - INFO: 等待创建活动页面完全加载 4.9 秒...
2025-07-24 14:58:58,902 - INFO: 步骤2: 已跳过点击创建活动按钮（直接访问创建页面）
2025-07-24 14:58:58,902 - INFO: ✅ 已成功进入创建活动页面
2025-07-24 14:58:58,905 - INFO: ✅ 创建活动页面网络请求已完成
2025-07-24 14:58:58,907 - INFO: 创建活动页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:58:58,907 - INFO: 创建活动页面标题: 妙手-创建活动
2025-07-24 14:58:58,907 - INFO: 🔍 保存创建活动页面调试信息...
2025-07-24 14:58:58,913 - INFO: 📄 已保存HTML源码: debug_pages\create_activity_page_20250724_145858.html
2025-07-24 14:58:59,039 - INFO: 📸 已保存页面截图: debug_pages\create_activity_page_20250724_145858.png
2025-07-24 14:58:59,052 - INFO: 📋 已保存页面信息: debug_pages\create_activity_page_20250724_145858_info.txt
2025-07-24 14:58:59,052 - INFO: 🔍 验证创建活动页面加载状态...
2025-07-24 14:58:59,052 - INFO: ✅ 确认已进入创建活动页面 (通过URL检查)
2025-07-24 14:58:59,052 - INFO: ✅ 创建活动页面加载验证通过，继续执行...
2025-07-24 14:58:59,052 - INFO: 步骤3: 开始选择店铺
2025-07-24 14:58:59,052 - INFO: 等待创建活动页面完全加载...
2025-07-24 14:59:01,054 - INFO: 🔍 调试: 查看页面上的所有label元素
2025-07-24 14:59:01,057 - INFO: 找到 8 个label元素:
2025-07-24 14:59:01,057 - INFO:   🎯 重要: label 0: '选择店铺
:' (ID: jx-id-7987-13, 可见: True)
2025-07-24 14:59:01,058 - INFO:       XPath: //*[@id="jx-id-7987-13"]
2025-07-24 14:59:01,058 - INFO:   label 1: '活动名称
:' (ID: jx-id-7987-16, 可见)
2025-07-24 14:59:01,058 - INFO:   label 2: '开始时间
:' (ID: jx-id-7987-17, 可见)
2025-07-24 14:59:01,058 - INFO:   label 3: '结束时间
:' (ID: jx-id-7987-19, 可见)
2025-07-24 14:59:01,058 - INFO:   label 4: '产品类别
:' (ID: jx-id-7987-21, 可见)
2025-07-24 14:59:01,058 - INFO:   label 5: '按产品(SPU)' (ID: no-id, 可见)
2025-07-24 14:59:01,058 - INFO:   label 6: '按单品(SKU)' (ID: no-id, 可见)
2025-07-24 14:59:01,058 - INFO:   label 7: '自动延续:' (ID: jx-id-7987-24, 可见)
2025-07-24 14:59:01,058 - INFO: 🎯 第一步：点击'选择店铺'输入框
2025-07-24 14:59:01,058 - INFO: 等待 3.1 秒后开始操作...
2025-07-24 14:59:04,124 - INFO:   🎯 尝试点击: 级联选择器容器
2025-07-24 14:59:04,124 - INFO:      选择器: //div[contains(@class, 'jx-cascader')]
2025-07-24 14:59:04,133 - INFO:      找到 7 个匹配元素
2025-07-24 14:59:04,141 - INFO:      元素可见性: True
2025-07-24 14:59:04,723 - INFO:   ✅ 成功点击: 级联选择器容器
2025-07-24 14:59:06,734 - INFO:   🎉 下拉菜单已成功弹出！
2025-07-24 14:59:06,735 - INFO: 🎯 第二步：等待下拉菜单出现
2025-07-24 14:59:06,735 - INFO:   等待下拉菜单加载 3.7 秒...
2025-07-24 14:59:10,480 - INFO:   ✅ 下拉菜单已显示
2025-07-24 14:59:10,480 - INFO: 🎯 第三步：选择第一个可用的国家/站点
2025-07-24 14:59:10,481 - INFO:   等待国家列表加载 2.5 秒...
2025-07-24 14:59:12,971 - INFO:   🔍 查找所有可用的国家选项...
2025-07-24 14:59:12,976 - INFO:   找到 2 个级联节点
2025-07-24 14:59:12,987 - INFO:   🌍 找到国家选项 1: 泰国
2025-07-24 14:59:13,597 - INFO:   ✅ 成功选择国家: 泰国 (方法1)
2025-07-24 14:59:15,601 - INFO:   🎉 已选择国家: 泰国
2025-07-24 14:59:15,602 - INFO: 步骤4: 设置产品类别为按单品(SKU)
2025-07-24 14:59:15,602 - INFO: 设置产品类别: 按单品(SKU)
2025-07-24 14:59:45,617 - INFO: 尝试选择按单品(SKU) (选择器 1)
2025-07-24 14:59:45,622 - INFO: ✅ 成功选择按单品(SKU)
2025-07-24 14:59:45,622 - INFO: 等待选择生效 1.6 秒...
2025-07-24 14:59:47,253 - INFO: 步骤5: 开启自动延续
2025-07-24 14:59:47,253 - INFO: 检查并开启自动延续开关
2025-07-24 14:59:47,253 - INFO: 检查自动延续开关 (选择器 1)
2025-07-24 14:59:47,263 - INFO: 自动延续状态: is_checked=True, aria-checked=true
2025-07-24 14:59:47,263 - INFO: ✅ 自动延续已经开启
2025-07-24 14:59:47,264 - INFO: 步骤6: 点击创建活动
2025-07-24 14:59:47,264 - INFO: 准备提交创建活动
2025-07-24 14:59:47,264 - INFO: 尝试点击创建活动按钮 (选择器 1)
2025-07-24 14:59:47,264 - INFO:    选择器: //button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button')]//span[contains(text(), '创建活动')]/..
2025-07-24 14:59:47,266 - INFO:    找到 0 个匹配元素
2025-07-24 14:59:47,267 - WARNING:    未找到匹配的按钮元素
2025-07-24 14:59:47,267 - INFO: 尝试点击创建活动按钮 (选择器 2)
2025-07-24 14:59:47,267 - INFO:    选择器: //button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button') and contains(., '创建活动') and not(contains(., '取消'))]
2025-07-24 14:59:47,272 - INFO:    找到 1 个匹配元素
2025-07-24 14:59:47,278 - INFO:    按钮状态: 可见=True, 可用=True
2025-07-24 14:59:47,825 - INFO: ✅ 成功点击创建活动按钮 (选择器 2)
2025-07-24 14:59:47,825 - INFO: 等待活动创建处理...
2025-07-24 14:59:47,825 - INFO: 等待活动创建处理 3 秒... (尝试 1/3)
2025-07-24 14:59:50,838 - INFO: 当前页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 14:59:50,853 - INFO: ✅ 检测到成功提示消息
2025-07-24 14:59:50,878 - INFO: ✅ 活动创建成功
2025-07-24 14:59:50,878 - INFO: ✅ 限时秒杀活动创建流程完成
2025-07-24 14:59:50,878 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 15:00:36,532 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 15:00:36,532 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 15:00:36,532 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 15:00:36,560 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 15:00:36,854 - INFO: 启动响应: {'requestId': '3c84c081-9e07-454f-b11b-54e9ed8e3836', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 3234076, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '3c84c081-9e07-454f-b11b-54e9ed8e3836', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 15:00:36,854 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 15:00:36,854 - INFO: 正在初始化自动化实例...
2025-07-24 15:00:37,157 - INFO: Container code: 1277086407 started.
2025-07-24 15:00:37,157 - INFO: 开始执行操作类型: create_activity
2025-07-24 15:00:37,157 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_150036
2025-07-24 15:00:37,157 - INFO: 活动持续时间: 24小时
2025-07-24 15:00:37,157 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_150036
2025-07-24 15:00:37,158 - INFO: 🧹 清理多余的标签页...
2025-07-24 15:00:37,158 - INFO: 当前共有 1 个标签页
2025-07-24 15:00:37,158 - INFO: 步骤1: 直接打开创建活动页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:00:47,079 - INFO: 页面加载成功 - 标题: 妙手-创建活动
2025-07-24 15:00:47,079 - INFO: 当前URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:00:47,079 - INFO: 等待创建活动页面完全加载 4.1 秒...
2025-07-24 15:00:51,171 - INFO: 步骤2: 已跳过点击创建活动按钮（直接访问创建页面）
2025-07-24 15:00:51,171 - INFO: ✅ 已成功进入创建活动页面
2025-07-24 15:00:51,174 - INFO: ✅ 创建活动页面网络请求已完成
2025-07-24 15:00:51,176 - INFO: 创建活动页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:00:51,176 - INFO: 创建活动页面标题: 妙手-创建活动
2025-07-24 15:00:51,176 - INFO: 🔍 保存创建活动页面调试信息...
2025-07-24 15:00:51,182 - INFO: 📄 已保存HTML源码: debug_pages\create_activity_page_20250724_150051.html
2025-07-24 15:00:51,340 - INFO: 📸 已保存页面截图: debug_pages\create_activity_page_20250724_150051.png
2025-07-24 15:00:51,353 - INFO: 📋 已保存页面信息: debug_pages\create_activity_page_20250724_150051_info.txt
2025-07-24 15:00:51,353 - INFO: 🔍 验证创建活动页面加载状态...
2025-07-24 15:00:51,353 - INFO: ✅ 确认已进入创建活动页面 (通过URL检查)
2025-07-24 15:00:51,353 - INFO: ✅ 创建活动页面加载验证通过，继续执行...
2025-07-24 15:00:51,353 - INFO: 步骤3: 开始选择店铺
2025-07-24 15:00:51,354 - INFO: 等待创建活动页面完全加载...
2025-07-24 15:00:53,358 - INFO: 🔍 调试: 查看页面上的所有label元素
2025-07-24 15:00:53,361 - INFO: 找到 8 个label元素:
2025-07-24 15:00:53,362 - INFO:   🎯 重要: label 0: '选择店铺
:' (ID: jx-id-371-13, 可见: True)
2025-07-24 15:00:53,362 - INFO:       XPath: //*[@id="jx-id-371-13"]
2025-07-24 15:00:53,362 - INFO:   label 1: '活动名称
:' (ID: jx-id-371-16, 可见)
2025-07-24 15:00:53,362 - INFO:   label 2: '开始时间
:' (ID: jx-id-371-17, 可见)
2025-07-24 15:00:53,362 - INFO:   label 3: '结束时间
:' (ID: jx-id-371-19, 可见)
2025-07-24 15:00:53,362 - INFO:   label 4: '产品类别
:' (ID: jx-id-371-21, 可见)
2025-07-24 15:00:53,362 - INFO:   label 5: '按产品(SPU)' (ID: no-id, 可见)
2025-07-24 15:00:53,362 - INFO:   label 6: '按单品(SKU)' (ID: no-id, 可见)
2025-07-24 15:00:53,362 - INFO:   label 7: '自动延续:' (ID: jx-id-371-24, 可见)
2025-07-24 15:00:53,362 - INFO: 🎯 第一步：点击'选择店铺'输入框
2025-07-24 15:00:53,362 - INFO: 等待 4.4 秒后开始操作...
2025-07-24 15:00:57,726 - INFO:   🎯 尝试点击: 级联选择器容器
2025-07-24 15:00:57,726 - INFO:      选择器: //div[contains(@class, 'jx-cascader')]
2025-07-24 15:00:57,735 - INFO:      找到 7 个匹配元素
2025-07-24 15:00:57,744 - INFO:      元素可见性: True
2025-07-24 15:00:58,308 - INFO:   ✅ 成功点击: 级联选择器容器
2025-07-24 15:01:00,326 - INFO:   🎉 下拉菜单已成功弹出！
2025-07-24 15:01:00,326 - INFO: 🎯 第二步：等待下拉菜单出现
2025-07-24 15:01:00,326 - INFO:   等待下拉菜单加载 3.3 秒...
2025-07-24 15:01:03,674 - INFO:   ✅ 下拉菜单已显示
2025-07-24 15:01:03,674 - INFO: 🎯 第三步：选择第一个可用的国家/站点
2025-07-24 15:01:03,674 - INFO:   等待国家列表加载 3.7 秒...
2025-07-24 15:01:07,361 - INFO:   🔍 查找所有可用的国家选项...
2025-07-24 15:01:07,365 - INFO:   找到 2 个级联节点
2025-07-24 15:01:07,378 - INFO:   🌍 找到国家选项 1: 泰国
2025-07-24 15:01:07,987 - INFO:   ✅ 成功选择国家: 泰国 (方法1)
2025-07-24 15:01:10,003 - INFO:   🎉 已选择国家: 泰国
2025-07-24 15:01:10,003 - INFO: 步骤4: 设置产品类别为按单品(SKU)
2025-07-24 15:01:10,003 - INFO: 设置产品类别: 按单品(SKU)
2025-07-24 15:01:40,021 - INFO: 尝试选择按单品(SKU) (选择器 1)
2025-07-24 15:01:40,026 - INFO: ✅ 成功选择按单品(SKU)
2025-07-24 15:01:40,026 - INFO: 等待选择生效 1.0 秒...
2025-07-24 15:01:41,063 - INFO: 步骤5: 开启自动延续
2025-07-24 15:01:41,063 - INFO: 检查并开启自动延续开关
2025-07-24 15:01:41,063 - INFO: 检查自动延续开关 (选择器 1)
2025-07-24 15:01:41,073 - INFO: 自动延续状态: is_checked=True, aria-checked=true
2025-07-24 15:01:41,074 - INFO: ✅ 自动延续已经开启
2025-07-24 15:01:41,074 - INFO: 步骤6: 点击创建活动
2025-07-24 15:01:41,074 - INFO: 准备提交创建活动
2025-07-24 15:01:41,074 - INFO: 尝试点击创建活动按钮 (选择器 1)
2025-07-24 15:01:41,074 - INFO:    选择器: //button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button')]//span[contains(text(), '创建活动')]/..
2025-07-24 15:01:41,077 - INFO:    找到 0 个匹配元素
2025-07-24 15:01:41,077 - WARNING:    未找到匹配的按钮元素
2025-07-24 15:01:41,077 - INFO: 尝试点击创建活动按钮 (选择器 2)
2025-07-24 15:01:41,077 - INFO:    选择器: //button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button') and contains(., '创建活动') and not(contains(., '取消'))]
2025-07-24 15:01:41,080 - INFO:    找到 1 个匹配元素
2025-07-24 15:01:41,086 - INFO:    按钮状态: 可见=True, 可用=True
2025-07-24 15:01:41,659 - INFO: ✅ 成功点击创建活动按钮 (选择器 2)
2025-07-24 15:01:41,659 - INFO: 等待活动创建处理...
2025-07-24 15:01:41,660 - INFO: 等待活动创建处理 3 秒... (尝试 1/3)
2025-07-24 15:01:44,673 - INFO: 当前页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:01:44,687 - INFO: ✅ 检测到成功提示消息
2025-07-24 15:01:44,713 - INFO: ✅ 活动创建成功
2025-07-24 15:01:44,713 - INFO: ✅ 限时秒杀活动创建流程完成
2025-07-24 15:01:44,714 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 15:06:09,178 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 15:06:09,178 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 15:06:09,178 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 15:06:09,208 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 15:06:09,542 - INFO: 启动响应: {'requestId': 'a7b456f2-d486-4842-ac1a-91a412688f8a', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 3566766, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': 'a7b456f2-d486-4842-ac1a-91a412688f8a', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 15:06:09,542 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 15:06:09,542 - INFO: 正在初始化自动化实例...
2025-07-24 15:06:09,842 - INFO: Container code: 1277086407 started.
2025-07-24 15:06:09,843 - INFO: 开始执行操作类型: create_activity
2025-07-24 15:06:09,843 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_150609
2025-07-24 15:06:09,843 - INFO: 活动持续时间: 24小时
2025-07-24 15:06:09,843 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_150609
2025-07-24 15:06:09,843 - INFO: 🧹 清理多余的标签页...
2025-07-24 15:06:09,843 - INFO: 当前共有 2 个标签页
2025-07-24 15:06:09,891 - INFO: 已关闭标签页 1
2025-07-24 15:06:09,891 - INFO: 步骤1: 直接打开创建活动页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:06:22,237 - INFO: 页面加载成功 - 标题: 妙手-创建活动
2025-07-24 15:06:22,237 - INFO: 当前URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:06:22,238 - INFO: 等待创建活动页面完全加载 3.2 秒...
2025-07-24 15:06:25,480 - INFO: 步骤2: 已跳过点击创建活动按钮（直接访问创建页面）
2025-07-24 15:06:25,480 - INFO: ✅ 已成功进入创建活动页面
2025-07-24 15:06:25,483 - INFO: ✅ 创建活动页面网络请求已完成
2025-07-24 15:06:25,487 - INFO: 创建活动页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:06:25,487 - INFO: 创建活动页面标题: 妙手-创建活动
2025-07-24 15:06:25,487 - INFO: 🔍 保存创建活动页面调试信息...
2025-07-24 15:06:25,494 - INFO: 📄 已保存HTML源码: debug_pages\create_activity_page_20250724_150625.html
2025-07-24 15:06:25,662 - INFO: 📸 已保存页面截图: debug_pages\create_activity_page_20250724_150625.png
2025-07-24 15:06:25,674 - INFO: 📋 已保存页面信息: debug_pages\create_activity_page_20250724_150625_info.txt
2025-07-24 15:06:25,674 - INFO: 🔍 验证创建活动页面加载状态...
2025-07-24 15:06:25,674 - INFO: ✅ 确认已进入创建活动页面 (通过URL检查)
2025-07-24 15:06:25,674 - INFO: ✅ 创建活动页面加载验证通过，继续执行...
2025-07-24 15:06:25,674 - INFO: 步骤3: 开始选择店铺
2025-07-24 15:06:25,675 - INFO: 等待创建活动页面完全加载...
2025-07-24 15:06:27,686 - INFO: 🔍 调试: 查看页面上的所有label元素
2025-07-24 15:06:27,690 - INFO: 找到 8 个label元素:
2025-07-24 15:06:27,690 - INFO:   🎯 重要: label 0: '选择店铺
:' (ID: jx-id-2858-13, 可见: True)
2025-07-24 15:06:27,691 - INFO:       XPath: //*[@id="jx-id-2858-13"]
2025-07-24 15:06:27,691 - INFO:   label 1: '活动名称
:' (ID: jx-id-2858-16, 可见)
2025-07-24 15:06:27,691 - INFO:   label 2: '开始时间
:' (ID: jx-id-2858-17, 可见)
2025-07-24 15:06:27,691 - INFO:   label 3: '结束时间
:' (ID: jx-id-2858-19, 可见)
2025-07-24 15:06:27,691 - INFO:   label 4: '产品类别
:' (ID: jx-id-2858-21, 可见)
2025-07-24 15:06:27,691 - INFO:   label 5: '按产品(SPU)' (ID: no-id, 可见)
2025-07-24 15:06:27,691 - INFO:   label 6: '按单品(SKU)' (ID: no-id, 可见)
2025-07-24 15:06:27,691 - INFO:   label 7: '自动延续:' (ID: jx-id-2858-24, 可见)
2025-07-24 15:06:27,691 - INFO: 🎯 第一步：点击'选择店铺'输入框
2025-07-24 15:06:27,691 - INFO: 等待 2.7 秒后开始操作...
2025-07-24 15:06:30,389 - INFO:   🎯 尝试点击: 级联选择器容器
2025-07-24 15:06:30,389 - INFO:      选择器: //div[contains(@class, 'jx-cascader')]
2025-07-24 15:06:30,398 - INFO:      找到 7 个匹配元素
2025-07-24 15:06:30,407 - INFO:      元素可见性: True
2025-07-24 15:06:30,994 - INFO:   ✅ 成功点击: 级联选择器容器
2025-07-24 15:06:33,006 - INFO:   🎉 下拉菜单已成功弹出！
2025-07-24 15:06:33,006 - INFO: 🎯 第二步：等待下拉菜单出现
2025-07-24 15:06:33,006 - INFO:   等待下拉菜单加载 3.0 秒...
2025-07-24 15:06:36,020 - INFO:   ✅ 下拉菜单已显示
2025-07-24 15:06:36,021 - INFO: 🎯 第三步：选择第一个可用的国家/站点
2025-07-24 15:06:36,021 - INFO:   等待国家列表加载 2.0 秒...
2025-07-24 15:06:38,044 - INFO:   🔍 查找所有可用的国家选项...
2025-07-24 15:06:38,049 - INFO:   找到 2 个级联节点
2025-07-24 15:06:38,065 - INFO:   🌍 找到国家选项 1: 泰国
2025-07-24 15:06:38,663 - INFO:   ✅ 成功选择国家: 泰国 (方法1)
2025-07-24 15:06:40,667 - INFO:   🎉 已选择国家: 泰国
2025-07-24 15:06:40,667 - INFO: 步骤4: 设置产品类别为按单品(SKU)
2025-07-24 15:06:40,667 - INFO: 设置产品类别: 按单品(SKU)
2025-07-24 15:07:10,683 - INFO: 尝试选择按单品(SKU) (选择器 1)
2025-07-24 15:07:10,689 - INFO: ✅ 成功选择按单品(SKU)
2025-07-24 15:07:10,689 - INFO: 等待选择生效 1.7 秒...
2025-07-24 15:07:12,388 - INFO: 步骤5: 开启自动延续
2025-07-24 15:07:12,388 - INFO: 检查并开启自动延续开关
2025-07-24 15:07:12,388 - INFO: 检查自动延续开关 (选择器 1)
2025-07-24 15:07:12,401 - INFO: 自动延续状态: is_checked=True, aria-checked=true
2025-07-24 15:07:12,401 - INFO: ✅ 自动延续已经开启
2025-07-24 15:07:12,401 - INFO: 步骤6: 点击创建活动
2025-07-24 15:07:12,401 - INFO: 准备提交创建活动
2025-07-24 15:07:12,401 - INFO: 尝试点击创建活动按钮 (选择器 1)
2025-07-24 15:07:12,402 - INFO:    选择器: //button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button')]//span[contains(text(), '创建活动')]/..
2025-07-24 15:07:12,405 - INFO:    找到 0 个匹配元素
2025-07-24 15:07:12,405 - WARNING:    未找到匹配的按钮元素
2025-07-24 15:07:12,405 - INFO: 尝试点击创建活动按钮 (选择器 2)
2025-07-24 15:07:12,406 - INFO:    选择器: //button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button') and contains(., '创建活动') and not(contains(., '取消'))]
2025-07-24 15:07:12,410 - INFO:    找到 1 个匹配元素
2025-07-24 15:07:12,415 - INFO:    按钮状态: 可见=True, 可用=True
2025-07-24 15:07:12,976 - INFO: ✅ 成功点击创建活动按钮 (选择器 2)
2025-07-24 15:07:12,977 - INFO: 等待活动创建处理...
2025-07-24 15:07:12,977 - INFO: 等待活动创建处理 3 秒... (尝试 1/3)
2025-07-24 15:07:15,984 - INFO: 当前页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:07:16,005 - INFO: ✅ 检测到成功提示消息
2025-07-24 15:07:16,037 - INFO: ✅ 活动创建成功
2025-07-24 15:07:16,037 - INFO: ✅ 限时秒杀活动创建流程完成
2025-07-24 15:07:16,038 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 15:11:19,882 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 15:11:19,882 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 15:11:19,882 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 15:11:19,913 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 15:11:20,204 - INFO: 启动响应: {'requestId': '594de19c-ba34-4b76-b3dc-4cca81bca90c', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 3877429, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '594de19c-ba34-4b76-b3dc-4cca81bca90c', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 15:11:20,204 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 15:11:20,205 - INFO: 正在初始化自动化实例...
2025-07-24 15:11:20,508 - INFO: Container code: 1277086407 started.
2025-07-24 15:11:20,508 - INFO: 开始执行操作类型: create_activity
2025-07-24 15:11:20,508 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_151119
2025-07-24 15:11:20,508 - INFO: 活动持续时间: 24小时
2025-07-24 15:11:20,508 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_151119
2025-07-24 15:11:20,508 - INFO: 🧹 清理多余的标签页...
2025-07-24 15:11:20,508 - INFO: 当前共有 2 个标签页
2025-07-24 15:11:20,552 - INFO: 已关闭标签页 1
2025-07-24 15:11:20,552 - INFO: 步骤1: 直接打开创建活动页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:11:25,740 - INFO: 页面加载成功 - 标题: 妙手-创建活动
2025-07-24 15:11:25,740 - INFO: 当前URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:11:25,740 - INFO: 等待创建活动页面完全加载 4.7 秒...
2025-07-24 15:11:30,486 - INFO: 步骤2: 已跳过点击创建活动按钮（直接访问创建页面）
2025-07-24 15:11:30,486 - INFO: ✅ 已成功进入创建活动页面
2025-07-24 15:11:34,759 - INFO: ✅ 创建活动页面网络请求已完成
2025-07-24 15:11:34,762 - INFO: 创建活动页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:11:34,762 - INFO: 创建活动页面标题: 妙手-创建活动
2025-07-24 15:11:34,762 - INFO: 🔍 保存创建活动页面调试信息...
2025-07-24 15:11:34,768 - INFO: 📄 已保存HTML源码: debug_pages\create_activity_page_20250724_151134.html
2025-07-24 15:11:34,912 - INFO: 📸 已保存页面截图: debug_pages\create_activity_page_20250724_151134.png
2025-07-24 15:11:34,928 - INFO: 📋 已保存页面信息: debug_pages\create_activity_page_20250724_151134_info.txt
2025-07-24 15:11:34,928 - INFO: 🔍 验证创建活动页面加载状态...
2025-07-24 15:11:34,928 - INFO: ✅ 确认已进入创建活动页面 (通过URL检查)
2025-07-24 15:11:34,928 - INFO: ✅ 创建活动页面加载验证通过，继续执行...
2025-07-24 15:11:34,929 - INFO: 步骤3: 开始选择店铺
2025-07-24 15:11:34,929 - INFO: 等待创建活动页面完全加载...
2025-07-24 15:11:36,939 - INFO: 🔍 调试: 查看页面上的所有label元素
2025-07-24 15:11:36,943 - INFO: 找到 8 个label元素:
2025-07-24 15:11:36,943 - INFO:   🎯 重要: label 0: '选择店铺
:' (ID: jx-id-848-13, 可见: True)
2025-07-24 15:11:36,944 - INFO:       XPath: //*[@id="jx-id-848-13"]
2025-07-24 15:11:36,944 - INFO:   label 1: '活动名称
:' (ID: jx-id-848-16, 可见)
2025-07-24 15:11:36,944 - INFO:   label 2: '开始时间
:' (ID: jx-id-848-17, 可见)
2025-07-24 15:11:36,944 - INFO:   label 3: '结束时间
:' (ID: jx-id-848-19, 可见)
2025-07-24 15:11:36,944 - INFO:   label 4: '产品类别
:' (ID: jx-id-848-21, 可见)
2025-07-24 15:11:36,944 - INFO:   label 5: '按产品(SPU)' (ID: no-id, 可见)
2025-07-24 15:11:36,945 - INFO:   label 6: '按单品(SKU)' (ID: no-id, 可见)
2025-07-24 15:11:36,945 - INFO:   label 7: '自动延续:' (ID: jx-id-848-24, 可见)
2025-07-24 15:11:36,945 - INFO: 🎯 第一步：点击'选择店铺'输入框
2025-07-24 15:11:36,945 - INFO: 等待 2.9 秒后开始操作...
2025-07-24 15:11:39,906 - INFO:   🎯 尝试点击: 级联选择器容器
2025-07-24 15:11:39,907 - INFO:      选择器: //div[contains(@class, 'jx-cascader')]
2025-07-24 15:11:39,916 - INFO:      找到 7 个匹配元素
2025-07-24 15:11:39,931 - INFO:      元素可见性: True
2025-07-24 15:11:40,526 - INFO:   ✅ 成功点击: 级联选择器容器
2025-07-24 15:11:42,546 - INFO:   🎉 下拉菜单已成功弹出！
2025-07-24 15:11:42,547 - INFO: 🎯 第二步：等待下拉菜单出现
2025-07-24 15:11:42,548 - INFO:   等待下拉菜单加载 3.5 秒...
2025-07-24 15:11:46,106 - INFO:   ✅ 下拉菜单已显示
2025-07-24 15:11:46,106 - INFO: 🎯 第三步：选择第一个可用的国家/站点
2025-07-24 15:11:46,106 - INFO:   等待国家列表加载 2.1 秒...
2025-07-24 15:11:48,226 - INFO:   🔍 查找所有可用的国家选项...
2025-07-24 15:11:48,233 - INFO:   找到 2 个级联节点
2025-07-24 15:11:48,248 - INFO:   🌍 找到国家选项 1: 马来
2025-07-24 15:11:48,840 - INFO:   ✅ 成功选择国家: 马来 (方法1)
2025-07-24 15:11:50,843 - INFO:   🎉 已选择国家: 马来
2025-07-24 15:11:50,843 - INFO: 步骤4: 设置产品类别为按单品(SKU)
2025-07-24 15:11:50,843 - INFO: 设置产品类别: 按单品(SKU)
2025-07-24 15:12:20,854 - INFO: 尝试选择按单品(SKU) (选择器 1)
2025-07-24 15:12:20,863 - INFO: ✅ 成功选择按单品(SKU)
2025-07-24 15:12:20,863 - INFO: 等待选择生效 1.9 秒...
2025-07-24 15:12:22,734 - INFO: 步骤5: 开启自动延续
2025-07-24 15:12:22,734 - INFO: 检查并开启自动延续开关
2025-07-24 15:12:22,734 - INFO: 检查自动延续开关 (选择器 1)
2025-07-24 15:12:22,935 - INFO: 自动延续状态: is_checked=True, aria-checked=true
2025-07-24 15:12:22,935 - INFO: ✅ 自动延续已经开启
2025-07-24 15:12:22,935 - INFO: 步骤6: 点击创建活动
2025-07-24 15:12:22,935 - INFO: 准备提交创建活动
2025-07-24 15:12:22,936 - INFO: 尝试点击创建活动按钮 (选择器 1)
2025-07-24 15:12:22,936 - INFO:    选择器: //button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button')]//span[contains(text(), '创建活动')]/..
2025-07-24 15:12:22,952 - INFO:    找到 0 个匹配元素
2025-07-24 15:12:22,952 - WARNING:    未找到匹配的按钮元素
2025-07-24 15:12:22,952 - INFO: 尝试点击创建活动按钮 (选择器 2)
2025-07-24 15:12:22,952 - INFO:    选择器: //button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button') and contains(., '创建活动') and not(contains(., '取消'))]
2025-07-24 15:12:22,980 - INFO:    找到 1 个匹配元素
2025-07-24 15:12:23,002 - INFO:    按钮状态: 可见=True, 可用=True
2025-07-24 15:12:23,578 - INFO: ✅ 成功点击创建活动按钮 (选择器 2)
2025-07-24 15:12:23,578 - INFO: 等待活动创建处理...
2025-07-24 15:12:23,578 - INFO: 等待活动创建处理 3 秒... (尝试 1/3)
2025-07-24 15:12:26,582 - INFO: 当前页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:12:26,599 - INFO: ✅ 检测到成功提示消息
2025-07-24 15:12:26,624 - INFO: ✅ 活动创建成功
2025-07-24 15:12:26,624 - INFO: ✅ 限时秒杀活动创建流程完成
2025-07-24 15:12:26,624 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 15:19:20,698 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 15:19:20,699 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 15:19:20,699 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 15:19:20,707 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 15:19:21,008 - INFO: 启动响应: {'requestId': '0295781d-b67f-4bba-8cc0-7a51e04f1e85', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 4358232, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '0295781d-b67f-4bba-8cc0-7a51e04f1e85', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 15:19:21,008 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 15:19:21,008 - INFO: 正在初始化自动化实例...
2025-07-24 15:19:21,344 - INFO: Container code: 1277086407 started.
2025-07-24 15:19:21,345 - INFO: 开始执行操作类型: create_activity
2025-07-24 15:19:21,345 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_151920
2025-07-24 15:19:21,345 - INFO: 活动持续时间: 24小时
2025-07-24 15:19:21,345 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_151920
2025-07-24 15:19:21,345 - INFO: 🧹 清理多余的标签页...
2025-07-24 15:19:21,345 - INFO: 当前共有 2 个标签页
2025-07-24 15:19:21,393 - INFO: 已关闭标签页 1
2025-07-24 15:19:21,393 - INFO: 步骤1: 直接打开创建活动页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:19:30,655 - INFO: 页面加载成功 - 标题: 妙手-创建活动
2025-07-24 15:19:30,655 - INFO: 当前URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:19:30,655 - INFO: 等待创建活动页面完全加载 3.7 秒...
2025-07-24 15:19:34,321 - INFO: 步骤2: 已跳过点击创建活动按钮（直接访问创建页面）
2025-07-24 15:19:34,321 - INFO: ✅ 已成功进入创建活动页面
2025-07-24 15:19:34,324 - INFO: ✅ 创建活动页面网络请求已完成
2025-07-24 15:19:34,333 - INFO: 创建活动页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:19:34,333 - INFO: 创建活动页面标题: 妙手-创建活动
2025-07-24 15:19:34,333 - INFO: 🔍 保存创建活动页面调试信息...
2025-07-24 15:19:34,345 - INFO: 📄 已保存HTML源码: debug_pages\create_activity_page_20250724_151934.html
2025-07-24 15:20:04,367 - WARNING: 保存页面调试信息失败: Page.screenshot: Timeout 30000ms exceeded.
Call log:
  - taking page screenshot
  - waiting for fonts to load...
  - fonts loaded

2025-07-24 15:20:04,368 - INFO: 🔍 验证创建活动页面加载状态...
2025-07-24 15:20:04,368 - INFO: ✅ 确认已进入创建活动页面 (通过URL检查)
2025-07-24 15:20:04,368 - INFO: ✅ 创建活动页面加载验证通过，继续执行...
2025-07-24 15:20:04,368 - INFO: 步骤3: 开始选择店铺
2025-07-24 15:20:04,368 - INFO: 等待创建活动页面完全加载...
2025-07-24 15:20:06,377 - INFO: 🔍 调试: 查看页面上的所有label元素
2025-07-24 15:20:06,383 - INFO: 找到 8 个label元素:
2025-07-24 15:20:06,383 - INFO:   🎯 重要: label 0: '选择店铺
:' (ID: jx-id-7731-13, 可见: True)
2025-07-24 15:20:06,383 - INFO:       XPath: //*[@id="jx-id-7731-13"]
2025-07-24 15:20:06,383 - INFO:   label 1: '活动名称
:' (ID: jx-id-7731-16, 可见)
2025-07-24 15:20:06,383 - INFO:   label 2: '开始时间
:' (ID: jx-id-7731-17, 可见)
2025-07-24 15:20:06,384 - INFO:   label 3: '结束时间
:' (ID: jx-id-7731-19, 可见)
2025-07-24 15:20:06,384 - INFO:   label 4: '产品类别
:' (ID: jx-id-7731-21, 可见)
2025-07-24 15:20:06,384 - INFO:   label 5: '按产品(SPU)' (ID: no-id, 可见)
2025-07-24 15:20:06,384 - INFO:   label 6: '按单品(SKU)' (ID: no-id, 可见)
2025-07-24 15:20:06,384 - INFO:   label 7: '自动延续:' (ID: jx-id-7731-24, 可见)
2025-07-24 15:20:06,384 - INFO: 🎯 第一步：点击'选择店铺'输入框
2025-07-24 15:20:06,384 - INFO: 等待 2.4 秒后开始操作...
2025-07-24 15:20:08,784 - INFO:   🎯 尝试点击: 级联选择器容器
2025-07-24 15:20:08,784 - INFO:      选择器: //div[contains(@class, 'jx-cascader')]
2025-07-24 15:20:08,794 - INFO:      找到 7 个匹配元素
2025-07-24 15:20:08,805 - INFO:      元素可见性: True
2025-07-24 15:20:09,375 - INFO:   ✅ 成功点击: 级联选择器容器
2025-07-24 15:20:11,397 - INFO:   🎉 下拉菜单已成功弹出！
2025-07-24 15:20:11,398 - INFO: 🎯 第二步：等待下拉菜单出现
2025-07-24 15:20:11,398 - INFO:   等待下拉菜单加载 5.4 秒...
2025-07-24 15:20:16,808 - INFO:   ✅ 下拉菜单已显示
2025-07-24 15:20:16,809 - INFO: 🎯 第三步：选择第一个可用的国家/站点
2025-07-24 15:20:16,809 - INFO:   等待国家列表加载 2.8 秒...
2025-07-24 15:20:19,627 - INFO:   🔍 查找所有可用的国家选项...
2025-07-24 15:20:19,632 - INFO:   找到 2 个级联节点
2025-07-24 15:20:19,647 - INFO:   🌍 找到国家选项 1: 马来
2025-07-24 15:20:23,537 - INFO:   ✅ 成功选择国家: 马来 (方法1)
2025-07-24 15:20:25,545 - INFO:   🎉 已选择国家: 马来
2025-07-24 15:20:25,545 - INFO: 步骤4: 设置产品类别为按单品(SKU)
2025-07-24 15:20:25,545 - INFO: 设置产品类别: 按单品(SKU)
2025-07-24 15:20:55,561 - INFO: 尝试选择按单品(SKU) (选择器 1)
2025-07-24 15:20:55,567 - INFO: ✅ 成功选择按单品(SKU)
2025-07-24 15:20:55,567 - INFO: 等待选择生效 1.6 秒...
2025-07-24 15:20:57,141 - INFO: 步骤5: 开启自动延续
2025-07-24 15:20:57,141 - INFO: 检查并开启自动延续开关
2025-07-24 15:20:57,141 - INFO: 检查自动延续开关 (选择器 1)
2025-07-24 15:20:57,153 - INFO: 自动延续状态: is_checked=True, aria-checked=true
2025-07-24 15:20:57,154 - INFO: ✅ 自动延续已经开启
2025-07-24 15:20:57,154 - INFO: 步骤6: 点击创建活动
2025-07-24 15:20:57,154 - INFO: 准备提交创建活动
2025-07-24 15:20:57,154 - INFO: 尝试点击创建活动按钮 (选择器 1)
2025-07-24 15:20:57,154 - INFO:    选择器: //button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button')]//span[contains(text(), '创建活动')]/..
2025-07-24 15:20:57,157 - INFO:    找到 0 个匹配元素
2025-07-24 15:20:57,157 - WARNING:    未找到匹配的按钮元素
2025-07-24 15:20:57,157 - INFO: 尝试点击创建活动按钮 (选择器 2)
2025-07-24 15:20:57,157 - INFO:    选择器: //button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button') and contains(., '创建活动') and not(contains(., '取消'))]
2025-07-24 15:20:57,162 - INFO:    找到 1 个匹配元素
2025-07-24 15:20:57,168 - INFO:    按钮状态: 可见=True, 可用=True
2025-07-24 15:21:00,614 - INFO: ✅ 成功点击创建活动按钮 (选择器 2)
2025-07-24 15:21:00,614 - INFO: 等待活动创建处理...
2025-07-24 15:21:00,614 - INFO: 等待活动创建处理 3 秒... (尝试 1/3)
2025-07-24 15:21:03,619 - INFO: 当前页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:21:03,643 - INFO: ✅ 检测到成功提示消息
2025-07-24 15:21:03,669 - INFO: ✅ 活动创建成功
2025-07-24 15:21:03,669 - INFO: ✅ 限时秒杀活动创建流程完成
2025-07-24 15:21:03,669 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 15:33:18,014 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 15:33:18,014 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 15:33:18,014 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 15:33:18,043 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 15:33:18,331 - INFO: 启动响应: {'requestId': '13be8fb8-8daa-4acf-990d-cb6d7b7cbe4d', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 5195555, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '13be8fb8-8daa-4acf-990d-cb6d7b7cbe4d', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 15:33:18,331 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 15:33:18,331 - INFO: 正在初始化自动化实例...
2025-07-24 15:33:18,630 - INFO: Container code: 1277086407 started.
2025-07-24 15:33:18,630 - INFO: 开始执行操作类型: create_activity
2025-07-24 15:33:18,630 - INFO: 活动名称: 环境专用-妙手6_限时秒杀_20250724_153318
2025-07-24 15:33:18,630 - INFO: 活动持续时间: 24小时
2025-07-24 15:33:18,631 - INFO: 开始创建限时秒杀活动: 环境专用-妙手6_限时秒杀_20250724_153318
2025-07-24 15:33:18,631 - INFO: 🧹 清理多余的标签页...
2025-07-24 15:33:18,631 - INFO: 当前共有 2 个标签页
2025-07-24 15:33:18,666 - INFO: 已关闭标签页 1
2025-07-24 15:33:18,666 - INFO: 步骤1: 正在打开页面: https://erp.91miaoshou.com/tiktok/marketing/flashSale
2025-07-24 15:33:37,128 - INFO: 页面加载成功 - 标题: 妙手-限时秒杀
2025-07-24 15:33:37,129 - INFO: 当前URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale
2025-07-24 15:33:37,129 - INFO: 等待 1.1 秒...
2025-07-24 15:33:38,269 - INFO: 步骤2: 点击创建活动按钮
2025-07-24 15:33:38,269 - INFO: 🔍 调试: 查看页面上的所有按钮
2025-07-24 15:33:38,279 - INFO: 找到 71 个按钮:
2025-07-24 15:33:38,280 - INFO:   按钮 0: '搜索' (可见)
2025-07-24 15:33:38,280 - INFO:   按钮 1: '重置' (可见)
2025-07-24 15:33:38,280 - INFO:   按钮 2: '创建活动' (可见)
2025-07-24 15:33:38,280 - INFO:   按钮 3: '自动延续活动' (可见)
2025-07-24 15:33:38,280 - INFO:   按钮 4: '自动延续跟踪记录
11' (可见)
2025-07-24 15:33:38,280 - INFO:   按钮 5: '同步活动' (可见)
2025-07-24 15:33:38,280 - INFO:   按钮 6: '管理产品' (可见)
2025-07-24 15:33:38,280 - INFO:   按钮 7: '编辑' (可见)
2025-07-24 15:33:38,280 - INFO:   按钮 8: '终止' (可见)
2025-07-24 15:33:38,280 - INFO:   按钮 9: '复制' (可见)
2025-07-24 15:33:38,376 - INFO: 成功点击元素: 创建活动按钮
2025-07-24 15:33:38,376 - INFO: 🔍 等待新标签页打开...
2025-07-24 15:33:39,384 - INFO: 尝试 1/15: 当前共有 2 个标签页
2025-07-24 15:33:39,391 - INFO:   标签页 0: URL=https://erp.91miaoshou.com/tiktok/marketing/flashSale/create, 标题=妙手-创建活动
2025-07-24 15:33:39,391 - INFO: ✅ 找到创建活动标签页: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:33:39,411 - INFO: ✅ 已切换到创建活动标签页并等待加载完成
2025-07-24 15:33:39,437 - INFO: 创建活动页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:33:39,437 - INFO: 创建活动页面标题: 妙手-创建活动
2025-07-24 15:33:39,437 - INFO: 🔍 保存创建活动页面调试信息...
2025-07-24 15:33:39,446 - INFO: 📄 已保存HTML源码: debug_pages\create_activity_page_20250724_153339.html
2025-07-24 15:33:39,610 - INFO: 📸 已保存页面截图: debug_pages\create_activity_page_20250724_153339.png
2025-07-24 15:33:39,627 - INFO: 📋 已保存页面信息: debug_pages\create_activity_page_20250724_153339_info.txt
2025-07-24 15:33:39,627 - INFO: 🔍 检查页面跳转状态...
2025-07-24 15:33:39,630 - INFO: 当前页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
2025-07-24 15:33:39,630 - INFO: 当前页面标题: 妙手-创建活动
2025-07-24 15:33:39,630 - INFO: ✅ 确认已跳转到创建活动页面 (通过URL检查)
2025-07-24 15:33:39,631 - INFO: ✅ 页面跳转检查通过，继续执行...
2025-07-24 15:33:39,631 - INFO: 步骤3: 开始选择店铺
2025-07-24 15:33:39,631 - INFO: 等待创建活动页面完全加载...
2025-07-24 15:33:41,636 - INFO: 🔍 调试: 查看页面上的所有label元素
2025-07-24 15:33:41,640 - INFO: 找到 8 个label元素:
2025-07-24 15:33:41,640 - INFO:   🎯 重要: label 0: '选择店铺
:' (ID: jx-id-7987-13, 可见: True)
2025-07-24 15:33:41,640 - INFO:       XPath: //*[@id="jx-id-7987-13"]
2025-07-24 15:33:41,640 - INFO:   label 1: '活动名称
:' (ID: jx-id-7987-16, 可见)
2025-07-24 15:33:41,640 - INFO:   label 2: '开始时间
:' (ID: jx-id-7987-17, 可见)
2025-07-24 15:33:41,641 - INFO:   label 3: '结束时间
:' (ID: jx-id-7987-19, 可见)
2025-07-24 15:33:41,641 - INFO:   label 4: '产品类别
:' (ID: jx-id-7987-21, 可见)
2025-07-24 15:33:41,641 - INFO:   label 5: '按产品(SPU)' (ID: no-id, 可见)
2025-07-24 15:33:41,641 - INFO:   label 6: '按单品(SKU)' (ID: no-id, 可见)
2025-07-24 15:33:41,641 - INFO:   label 7: '自动延续:' (ID: jx-id-7987-24, 可见)
2025-07-24 15:33:41,641 - INFO: 🎯 第一步：点击'选择店铺'输入框
2025-07-24 15:33:41,641 - INFO: 等待 4.6 秒后开始操作...
2025-07-24 15:33:46,287 - INFO:   🎯 尝试点击: 输入框(placeholder)
2025-07-24 15:33:46,287 - INFO:      选择器: //input[@placeholder='请选择或输入搜索']
2025-07-24 15:33:46,294 - INFO:      找到 1 个匹配元素
2025-07-24 15:33:46,307 - INFO:      元素可见性: True
2025-07-24 15:33:46,910 - INFO:   ✅ 成功点击: 输入框(placeholder)
2025-07-24 15:33:48,924 - INFO:   🎉 下拉菜单已成功弹出！
2025-07-24 15:33:48,924 - INFO: 🎯 第二步：等待下拉菜单出现
2025-07-24 15:33:48,924 - INFO:   等待下拉菜单加载 3.9 秒...
2025-07-24 15:33:52,859 - INFO:   ✅ 下拉菜单已显示
2025-07-24 15:33:52,859 - INFO: 🎯 第三步：选择国家（泰国）
2025-07-24 15:33:52,859 - INFO:   等待国家列表加载 2.9 秒...
2025-07-24 15:33:55,728 - INFO:   🎯 尝试勾选泰国复选框: 方法1
2025-07-24 15:33:55,728 - INFO:      选择器: //li[contains(@class, 'jx-cascader-node')]//span[contains(text(), '泰国')]/../..//span[@class='jx-checkbox__inner']
2025-07-24 15:33:55,762 - INFO:      找到 51 个泰国复选框
2025-07-24 15:33:55,767 - INFO:      复选框可见性: True
2025-07-24 15:33:56,442 - INFO:   ✅ 成功勾选泰国复选框: 方法1
2025-07-24 15:33:58,448 - WARNING:   ⚠️ 复选框可能未勾选成功
2025-07-24 15:33:58,448 - INFO: 🎯 第四步：勾选按单品(SKU)
2025-07-24 15:33:58,449 - INFO: 等待页面稳定 2.4 秒...
2025-07-24 15:34:00,908 - INFO:   🎯 尝试勾选按单品(SKU): 方法1
2025-07-24 15:34:00,908 - INFO:      选择器: //label[contains(@class, 'jx-radio')][.//span[contains(text(), '按单品(SKU)')]]
2025-07-24 15:34:00,912 - INFO:      找到 0 个按单品(SKU)选项
2025-07-24 15:34:00,912 - WARNING:      未找到按单品(SKU)选项: 方法1
2025-07-24 15:34:00,912 - INFO:   🎯 尝试勾选按单品(SKU): 方法2
2025-07-24 15:34:00,912 - INFO:      选择器: //input[@type='radio'][@value='2']
2025-07-24 15:34:00,916 - INFO:      找到 1 个按单品(SKU)选项
2025-07-24 15:34:00,920 - INFO:      元素可见性: True
2025-07-24 15:34:31,459 - WARNING:   ❌ 勾选按单品(SKU)失败: 方法2 - ElementHandle.click: Timeout 30000ms exceeded.
Call log:
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <span class="jx-radio__inner"></span> intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <span class="jx-radio__inner"></span> intercepts pointer events
    - retrying click action
      - waiting 100ms
    58 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <span class="jx-radio__inner"></span> intercepts pointer events
     - retrying click action
       - waiting 500ms

2025-07-24 15:34:31,459 - INFO:   🎯 尝试勾选按单品(SKU): 方法3
2025-07-24 15:34:31,459 - INFO:      选择器: //label[.//span[contains(text(), '按单品(SKU)')]]//span[@class='jx-radio__inner']
2025-07-24 15:34:31,463 - INFO:      找到 0 个按单品(SKU)选项
2025-07-24 15:34:31,463 - WARNING:      未找到按单品(SKU)选项: 方法3
2025-07-24 15:34:31,463 - INFO:   🎯 尝试勾选按单品(SKU): 方法4
2025-07-24 15:34:31,463 - INFO:      选择器: //span[contains(text(), '按单品(SKU)')]/..
2025-07-24 15:34:31,466 - INFO:      找到 0 个按单品(SKU)选项
2025-07-24 15:34:31,466 - WARNING:      未找到按单品(SKU)选项: 方法4
2025-07-24 15:34:31,467 - INFO:   🎯 尝试勾选按单品(SKU): 方法5
2025-07-24 15:34:31,467 - INFO:      选择器: //label[contains(@class, 'jx-radio')][contains(., '按单品(SKU)')]
2025-07-24 15:34:31,471 - INFO:      找到 1 个按单品(SKU)选项
2025-07-24 15:34:31,476 - INFO:      元素可见性: True
2025-07-24 15:34:32,012 - INFO:   ✅ 成功勾选按单品(SKU): 方法5
2025-07-24 15:34:33,020 - INFO: 🎯 第五步：勾选自动延续
2025-07-24 15:34:33,020 - INFO: 等待页面稳定 1.0 秒...
2025-07-24 15:34:34,045 - INFO:   🎯 尝试勾选自动延续: 方法1
2025-07-24 15:34:34,045 - INFO:      选择器: //div[contains(@class, 'jx-switch')]
2025-07-24 15:34:34,049 - INFO:      找到 2 个自动延续选项
2025-07-24 15:34:34,052 - INFO:      元素可见性: True
2025-07-24 15:34:34,597 - INFO:   ✅ 成功勾选自动延续: 方法1
2025-07-24 15:34:35,611 - INFO: 🎯 第六步：点击创建活动
2025-07-24 15:34:35,611 - INFO: 等待页面稳定 1.6 秒...
2025-07-24 15:34:37,225 - INFO:   🎯 尝试点击创建活动: 方法1
2025-07-24 15:34:37,225 - INFO:      选择器: //button[.//span[contains(text(), '创建活动')]]
2025-07-24 15:34:37,228 - INFO:      找到 0 个创建活动按钮
2025-07-24 15:34:37,228 - WARNING:      未找到创建活动按钮: 方法1
2025-07-24 15:34:37,228 - INFO:   🎯 尝试点击创建活动: 方法2
2025-07-24 15:34:37,228 - INFO:      选择器: //button[contains(@class, 'jx-button--primary')]
2025-07-24 15:34:37,231 - INFO:      找到 1 个创建活动按钮
2025-07-24 15:34:37,234 - INFO:      元素可见性: True
2025-07-24 15:34:37,812 - INFO:   ✅ 成功点击创建活动: 方法2
2025-07-24 15:34:39,814 - INFO: 🎉 活动创建流程已完成！
2025-07-24 15:34:39,814 - INFO: 📋 创建的活动信息:
2025-07-24 15:34:39,814 - INFO:    - 活动名称: 107 泰国汽车 3-9
2025-07-24 15:34:39,814 - INFO:    - 选择国家: 泰国
2025-07-24 15:34:39,814 - INFO:    - 产品类别: 按单品(SKU)
2025-07-24 15:34:39,814 - INFO:    - 自动延续: 已启用
2025-07-24 15:34:39,814 - INFO: 步骤4: 设置产品类别为按单品(SKU)
2025-07-24 15:34:39,815 - INFO: 设置产品类别: 按单品(SKU)
2025-07-24 15:34:39,818 - INFO: ✅ 按单品(SKU)已经被选中
2025-07-24 15:34:39,819 - INFO: 步骤5: 开启自动延续
2025-07-24 15:34:39,819 - INFO: 检查并开启自动延续开关
2025-07-24 15:34:39,819 - INFO: 尝试自动延续开关 (选择器 1)
2025-07-24 15:34:44,828 - WARNING: 自动延续选择器 1 失败: Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//input[@class='jx-switch__input' and @type='checkbox' and @role='switch']") to be visible
    15 × locator resolved to hidden <input name="" role="switch" true-value="1" type="checkbox" false-value="2" id="jx-id-7987-47" aria-checked="true" aria-disabled="false" class="jx-switch__input"/>

2025-07-24 15:34:44,829 - INFO: 尝试自动延续开关 (选择器 2)
2025-07-24 15:34:49,845 - WARNING: 自动延续选择器 2 失败: Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//div[contains(@class, 'auto-extension-label')]/following-sibling::div//input[@type='checkbox']") to be visible

2025-07-24 15:34:49,845 - INFO: 尝试自动延续开关 (选择器 3)
2025-07-24 15:34:54,859 - WARNING: 自动延续选择器 3 失败: Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//div[contains(@class, 'jx-switch')]//input[@class='jx-switch__input']") to be visible
    15 × locator resolved to hidden <input name="" role="switch" true-value="1" type="checkbox" false-value="2" id="jx-id-7987-47" aria-checked="true" aria-disabled="false" class="jx-switch__input"/>

2025-07-24 15:34:54,859 - INFO: 尝试自动延续开关 (选择器 4)
2025-07-24 15:34:59,870 - WARNING: 自动延续选择器 4 失败: Page.wait_for_selector: Timeout 5000ms exceeded.
Call log:
  - waiting for locator("//input[@type='checkbox' and @role='switch' and contains(@true-value, '1')]") to be visible
    15 × locator resolved to hidden <input name="" role="switch" true-value="1" type="checkbox" false-value="2" id="jx-id-7987-47" aria-checked="true" aria-disabled="false" class="jx-switch__input"/>

2025-07-24 15:34:59,871 - ERROR: 所有选择器都失败，无法操作自动延续开关
2025-07-24 15:34:59,871 - INFO: 🔍 页面保持打开状态，便于检查结果...
2025-07-24 15:34:59,906 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 15:34:59,906 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 15:34:59,906 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 15:34:59,908 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 15:35:00,240 - INFO: 启动响应: {'requestId': '6d17cb76-ae13-451a-aff0-3e05e5aea378', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 5297460, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '6d17cb76-ae13-451a-aff0-3e05e5aea378', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 15:35:00,240 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 15:35:00,240 - INFO: 正在初始化自动化实例...
2025-07-24 15:35:00,242 - ERROR: 运行妙手自动化操作时发生错误: It looks like you are using Playwright Sync API inside the asyncio loop.
Please use the Async API instead.
2025-07-24 15:40:21,055 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 15:40:21,055 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 15:40:21,055 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 15:40:21,078 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 15:40:21,421 - INFO: 启动响应: {'requestId': 'c439b715-24f5-4ac2-a528-82a3863ce917', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 5618645, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': 'c439b715-24f5-4ac2-a528-82a3863ce917', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 15:40:21,421 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 15:40:21,421 - INFO: 正在初始化自动化实例...
2025-07-24 15:43:21,694 - ERROR: 运行妙手自动化操作时发生错误: BrowserType.connect_over_cdp: Timeout 180000ms exceeded.
Call log:
  - <ws preparing> retrieving websocket url from http://127.0.0.1:2766
  - <ws connecting> ws://127.0.0.1:2766/devtools/browser/dc97d3eb-6cdd-4d0d-b59b-46c24448e01c
  - <ws connected> ws://127.0.0.1:2766/devtools/browser/dc97d3eb-6cdd-4d0d-b59b-46c24448e01c

2025-07-24 15:43:21,735 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 15:43:21,735 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 15:43:21,735 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 15:43:21,738 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 15:43:22,081 - INFO: 启动响应: {'requestId': '082305a6-0da3-4372-ad3a-7c0b48003104', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 5799302, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '082305a6-0da3-4372-ad3a-7c0b48003104', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 15:43:22,081 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 15:43:22,081 - INFO: 正在初始化自动化实例...
2025-07-24 15:43:22,082 - ERROR: 运行妙手自动化操作时发生错误: It looks like you are using Playwright Sync API inside the asyncio loop.
Please use the Async API instead.
2025-07-24 16:19:52,113 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 16:19:52,114 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 16:19:52,114 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 16:19:52,122 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 16:19:52,481 - INFO: 启动响应: {'requestId': '79b8312b-19ec-4502-acdd-52b441e93878', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 7989704, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '79b8312b-19ec-4502-acdd-52b441e93878', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 16:19:52,481 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 16:19:52,481 - INFO: 正在初始化自动化实例...
2025-07-24 16:21:03,737 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 16:21:03,737 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 16:21:03,738 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 16:21:03,745 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 16:21:03,994 - INFO: 启动响应: {'requestId': '9b1b6f6e-3462-4b5d-8577-7c0301d3cec4', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 8061219, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '9b1b6f6e-3462-4b5d-8577-7c0301d3cec4', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 16:21:03,994 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 16:21:03,994 - INFO: 正在初始化自动化实例...
2025-07-24 16:21:03,996 - ERROR: 运行妙手自动化操作时发生错误: It looks like you are using Playwright Sync API inside the asyncio loop.
Please use the Async API instead.
2025-07-24 16:21:55,457 - INFO: 正在启动指纹浏览器环境: 环境专用-妙手6 (1277086407)
2025-07-24 16:21:55,458 - INFO: 发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start
2025-07-24 16:21:55,458 - INFO: 请求数据: {'containerCode': '1277086407', 'skipSystemResourceCheck': True}
2025-07-24 16:21:55,476 - INFO: 指纹浏览器服务连接测试成功
2025-07-24 16:21:55,844 - INFO: 启动响应: {'requestId': '36a02f6f-3640-4d92-b36f-04fa8a37a9e1', 'msg': 'Success', 'code': 0, 'data': {'action': 'startBrowserByCode', 'backgroundPluginId': 'cfnohfaoebgagflmgikojfpbcdmepkig', 'browserID': 126908640, 'browserPath': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio', 'containerCode': '1277086407', 'containerId': 126908640, 'debuggingPort': '2766', 'downloadPath': 'C:\\Users\\<USER>\\Desktop\\Hubstudio\\环境专用-妙手6', 'duplicate': 8113068, 'err': '成功(Success)', 'ip': '***************', 'isDynamicIp': False, 'launcherPage': 'about:blank', 'proxyType': 'local', 'requestId': '36a02f6f-3640-4d92-b36f-04fa8a37a9e1', 'runMode': 2, 'statusCode': '0', 'webdriver': 'C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe'}}
2025-07-24 16:21:55,844 - INFO: 指纹浏览器启动成功，调试端口: 2766
2025-07-24 16:21:55,845 - INFO: 正在初始化自动化实例...
