#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试错误修复功能的脚本
"""

import sys
import time
import requests
from playwright import sync_api
import logging
import threading
from threading import Thread
from datetime import datetime
from logging import FileHandler
import pandas as pd
import os
from openpyxl import load_workbook
from typing import Dict, List, Optional
import gc
from concurrent.futures import ThreadPoolExecutor
from playwright.sync_api import sync_playwright
import json
import random

# 导入主程序
# 由于文件名包含空格，我们需要使用importlib
import importlib.util
import sys

# 加载主程序模块
spec = importlib.util.spec_from_file_location("miaoshou_automation", "miaoshou_automation-addproduct_fixed copy.py")
miaoshou_module = importlib.util.module_from_spec(spec)
sys.modules["miaoshou_automation"] = miaoshou_module
spec.loader.exec_module(miaoshou_module)

# 导入MiaoshouAutomation类
MiaoshouAutomation = miaoshou_module.MiaoshouAutomation

def setup_logging(container_code: str) -> logging.Logger:
    """设置日志记录"""
    logger = logging.getLogger(f"miaoshou_{container_code}")
    logger.setLevel(logging.INFO)
    
    # 清除现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 创建日志目录
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 创建文件处理器
    current_date = datetime.now().strftime('%Y-%m-%d')
    log_file = os.path.join(log_dir, f"miaoshou_{container_code}_{current_date}.log")
    file_handler = FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 创建格式器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s: %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器到日志器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def start_browser(container_code: str) -> Dict:
    """启动指纹浏览器"""
    url = 'http://127.0.0.1:6873/api/v1/browser/start'
    data = {
        "containerCode": container_code,
        "skipSystemResourceCheck": True
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            res_json = response.json()
            if res_json.get('code') == 0:
                return {
                    'success': True,
                    'debugging_port': res_json.get('data', {}).get('debuggingPort'),
                    'message': res_json.get('msg', 'Success')
                }
            else:
                return {
                    'success': False,
                    'message': res_json.get('msg', 'Unknown error')
                }
        else:
            return {
                'success': False,
                'message': f'HTTP {response.status_code}'
            }
    except Exception as e:
        return {
            'success': False,
            'message': str(e)
        }

def main():
    """主函数"""
    container_code = "1254658148"  # 你的容器代码
    env_name = "环境专用-妙手1"
    
    # 设置日志
    logger = setup_logging(container_code)
    logger.info(f"开始测试错误修复功能: {env_name} ({container_code})")
    
    # 启动浏览器
    logger.info("正在启动指纹浏览器...")
    browser_result = start_browser(container_code)
    
    if not browser_result['success']:
        logger.error(f"启动浏览器失败: {browser_result['message']}")
        return False
    
    debugging_port = browser_result['debugging_port']
    logger.info(f"浏览器启动成功，调试端口: {debugging_port}")
    
    try:
        # 创建自动化实例
        automation = MiaoshouAutomation(container_code, debugging_port, logger, env_name)
        logger.info("自动化实例创建成功")
        
        # 这里你可以手动导航到有错误的页面，然后调用错误修复方法
        # 或者直接运行完整的添加产品流程
        
        # 运行添加产品流程（包含错误修复）
        success = automation.add_products_to_flash_sale()
        
        if success:
            logger.info("✅ 测试完成：错误修复功能正常")
        else:
            logger.error("❌ 测试失败：错误修复功能异常")
            
        return success
        
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        return False
    
    finally:
        logger.info("测试结束")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
