页面调试信息 - create_activity_page
时间: 20250724_142641
URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create
标题: 妙手-创建活动
页面大小: 71892 字符

==================================================
表单元素列表:
  input 0: {'type': 'input', 'index': 0, 'id': 'jx-id-3352-38', 'name': 'no-name', 'placeholder': '请选择或输入搜索', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 1: {'type': 'input', 'index': 1, 'id': 'no-id', 'name': 'no-name', 'placeholder': '请选择或输入搜索', 'className': 'jx-cascader__search-input', 'type_attr': 'text'}
  input 2: {'type': 'input', 'index': 2, 'id': 'jx-id-3352-39', 'name': 'no-name', 'placeholder': '请输入活动名称', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 3: {'type': 'input', 'index': 3, 'id': 'jx-id-3352-40', 'name': 'no-name', 'placeholder': '选择开始日期时间', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 4: {'type': 'input', 'index': 4, 'id': 'jx-id-3352-43', 'name': 'no-name', 'placeholder': '选择结束日期时间', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 5: {'type': 'input', 'index': 5, 'id': 'no-id', 'name': 'jx-id-3352-23', 'placeholder': 'no-placeholder', 'className': 'jx-radio__original', 'type_attr': 'radio'}
  input 6: {'type': 'input', 'index': 6, 'id': 'no-id', 'name': 'jx-id-3352-23', 'placeholder': 'no-placeholder', 'className': 'jx-radio__original', 'type_attr': 'radio'}
  input 7: {'type': 'input', 'index': 7, 'id': 'jx-id-3352-47', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-switch__input', 'type_attr': 'checkbox'}
  input 8: {'type': 'input', 'index': 8, 'id': 'J_minimumPluginVersion', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'no-class', 'type_attr': 'text'}
  input 9: {'type': 'input', 'index': 9, 'id': 'J_appInfo', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'no-class', 'type_attr': 'hidden'}
  input 10: {'type': 'input', 'index': 10, 'id': 'jx-id-3352-51', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 11: {'type': 'input', 'index': 11, 'id': 'jx-id-3352-52', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 12: {'type': 'input', 'index': 12, 'id': 'jx-id-3352-41', 'name': 'no-name', 'placeholder': '选择日期', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 13: {'type': 'input', 'index': 13, 'id': 'jx-id-3352-42', 'name': 'no-name', 'placeholder': '选择时间', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 14: {'type': 'input', 'index': 14, 'id': 'jx-id-3352-44', 'name': 'no-name', 'placeholder': '选择日期', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 15: {'type': 'input', 'index': 15, 'id': 'jx-id-3352-45', 'name': 'no-name', 'placeholder': '选择时间', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 16: {'type': 'input', 'index': 16, 'id': 'J_dwh_logS', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'no-class', 'type_attr': 'text'}
  label 0: {'type': 'label', 'index': 0, 'id': 'jx-id-3352-13', 'text': '选择店铺\n:', 'for': 'jx-id-3352-38'}
  label 1: {'type': 'label', 'index': 1, 'id': 'jx-id-3352-16', 'text': '活动名称\n:', 'for': 'jx-id-3352-39'}
  label 2: {'type': 'label', 'index': 2, 'id': 'jx-id-3352-17', 'text': '开始时间\n:', 'for': 'jx-id-3352-40'}
  label 3: {'type': 'label', 'index': 3, 'id': 'jx-id-3352-19', 'text': '结束时间\n:', 'for': 'jx-id-3352-43'}
  label 4: {'type': 'label', 'index': 4, 'id': 'jx-id-3352-21', 'text': '产品类别\n:', 'for': 'jx-id-3352-46'}
  label 5: {'type': 'label', 'index': 5, 'id': 'no-id', 'text': '按产品(SPU)', 'for': 'no-for'}
  label 6: {'type': 'label', 'index': 6, 'id': 'no-id', 'text': '按单品(SKU)', 'for': 'no-for'}
  label 7: {'type': 'label', 'index': 7, 'id': 'jx-id-3352-24', 'text': '自动延续:', 'for': 'jx-id-3352-47'}
  button 0: {'type': 'button', 'index': 0, 'id': 'no-id', 'text': '创建活动', 'className': 'jx-button jx-button--primary jx-button--default pro-button'}
  button 1: {'type': 'button', 'index': 1, 'id': 'no-id', 'text': '取消创建', 'className': 'jx-button jx-button--default pro-button'}
  button 2: {'type': 'button', 'index': 2, 'id': 'no-id', 'text': 'no-text', 'className': 'd-arrow-left jx-picker-panel__icon-btn'}
  button 3: {'type': 'button', 'index': 3, 'id': 'no-id', 'text': 'no-text', 'className': 'jx-picker-panel__icon-btn arrow-left'}
  button 4: {'type': 'button', 'index': 4, 'id': 'no-id', 'text': 'no-text', 'className': 'jx-picker-panel__icon-btn arrow-right'}
  button 5: {'type': 'button', 'index': 5, 'id': 'no-id', 'text': 'no-text', 'className': 'jx-picker-panel__icon-btn d-arrow-right'}
  button 6: {'type': 'button', 'index': 6, 'id': 'no-id', 'text': '此刻', 'className': 'jx-button jx-button--small is-text jx-picker-panel__link-btn'}
  button 7: {'type': 'button', 'index': 7, 'id': 'no-id', 'text': '确定', 'className': 'jx-button jx-button--small is-plain jx-picker-panel__link-btn'}
  button 8: {'type': 'button', 'index': 8, 'id': 'no-id', 'text': 'no-text', 'className': 'd-arrow-left jx-picker-panel__icon-btn'}
  button 9: {'type': 'button', 'index': 9, 'id': 'no-id', 'text': 'no-text', 'className': 'jx-picker-panel__icon-btn arrow-left'}
  button 10: {'type': 'button', 'index': 10, 'id': 'no-id', 'text': 'no-text', 'className': 'jx-picker-panel__icon-btn arrow-right'}
  button 11: {'type': 'button', 'index': 11, 'id': 'no-id', 'text': 'no-text', 'className': 'jx-picker-panel__icon-btn d-arrow-right'}
  button 12: {'type': 'button', 'index': 12, 'id': 'no-id', 'text': '此刻', 'className': 'jx-button jx-button--small is-text jx-picker-panel__link-btn'}
  button 13: {'type': 'button', 'index': 13, 'id': 'no-id', 'text': '确定', 'className': 'jx-button jx-button--small is-plain jx-picker-panel__link-btn'}
