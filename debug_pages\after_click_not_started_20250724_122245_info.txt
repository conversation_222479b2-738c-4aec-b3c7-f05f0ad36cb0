页面调试信息 - after_click_not_started
时间: 20250724_122245
URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale
标题: 妙手-限时秒杀
页面大小: 110440 字符

==================================================
表单元素列表:
  input 0: {'type': 'input', 'index': 0, 'id': 'jx-id-6805-52', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-select__input is-small', 'type_attr': 'text'}
  input 1: {'type': 'input', 'index': 1, 'id': 'jx-id-6805-53', 'name': 'no-name', 'placeholder': '请选择或输入搜索', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 2: {'type': 'input', 'index': 2, 'id': 'no-id', 'name': 'no-name', 'placeholder': '请选择或输入搜索', 'className': 'jx-cascader__search-input', 'type_attr': 'text'}
  input 3: {'type': 'input', 'index': 3, 'id': 'jx-id-6805-54', 'name': 'no-name', 'placeholder': '多个用逗号分隔，支持Excel复制粘贴', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 4: {'type': 'input', 'index': 4, 'id': 'jx-id-6805-55', 'name': 'no-name', 'placeholder': '多个用逗号分隔，支持Excel复制粘贴', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 5: {'type': 'input', 'index': 5, 'id': 'jx-id-6805-56', 'name': 'no-name', 'placeholder': '多个用逗号分隔，支持Excel复制粘贴', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 6: {'type': 'input', 'index': 6, 'id': 'jx-id-6805-57', 'name': 'no-name', 'placeholder': '多个用逗号分隔，支持Excel复制粘贴', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 7: {'type': 'input', 'index': 7, 'id': 'no-id', 'name': 'no-name', 'placeholder': '活动开始时间', 'className': 'jx-range-input', 'type_attr': 'text'}
  input 8: {'type': 'input', 'index': 8, 'id': 'no-id', 'name': 'no-name', 'placeholder': '活动结束时间', 'className': 'jx-range-input', 'type_attr': 'text'}
  input 9: {'type': 'input', 'index': 9, 'id': 'no-id', 'name': 'jx-id-6805-26', 'placeholder': 'no-placeholder', 'className': 'jx-radio-button__original-radio', 'type_attr': 'radio'}
  input 10: {'type': 'input', 'index': 10, 'id': 'no-id', 'name': 'jx-id-6805-26', 'placeholder': 'no-placeholder', 'className': 'jx-radio-button__original-radio', 'type_attr': 'radio'}
  input 11: {'type': 'input', 'index': 11, 'id': 'no-id', 'name': 'jx-id-6805-26', 'placeholder': 'no-placeholder', 'className': 'jx-radio-button__original-radio', 'type_attr': 'radio'}
  input 12: {'type': 'input', 'index': 12, 'id': 'no-id', 'name': 'jx-id-6805-26', 'placeholder': 'no-placeholder', 'className': 'jx-radio-button__original-radio', 'type_attr': 'radio'}
  input 13: {'type': 'input', 'index': 13, 'id': 'no-id', 'name': 'jx-id-6805-26', 'placeholder': 'no-placeholder', 'className': 'jx-radio-button__original-radio', 'type_attr': 'radio'}
  input 14: {'type': 'input', 'index': 14, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 15: {'type': 'input', 'index': 15, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 16: {'type': 'input', 'index': 16, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 17: {'type': 'input', 'index': 17, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 18: {'type': 'input', 'index': 18, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 19: {'type': 'input', 'index': 19, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 20: {'type': 'input', 'index': 20, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 21: {'type': 'input', 'index': 21, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 22: {'type': 'input', 'index': 22, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 23: {'type': 'input', 'index': 23, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 24: {'type': 'input', 'index': 24, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 25: {'type': 'input', 'index': 25, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 26: {'type': 'input', 'index': 26, 'id': 'jx-id-6805-63', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-input__inner', 'type_attr': 'number'}
  input 27: {'type': 'input', 'index': 27, 'id': 'jx-id-6805-64', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-select__input is-small', 'type_attr': 'text'}
  input 28: {'type': 'input', 'index': 28, 'id': 'J_minimumPluginVersion', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'no-class', 'type_attr': 'text'}
  input 29: {'type': 'input', 'index': 29, 'id': 'J_appInfo', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'no-class', 'type_attr': 'hidden'}
  input 30: {'type': 'input', 'index': 30, 'id': 'jx-id-6805-69', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 31: {'type': 'input', 'index': 31, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 32: {'type': 'input', 'index': 32, 'id': 'jx-id-6805-70', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 33: {'type': 'input', 'index': 33, 'id': 'jx-id-6805-71', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 34: {'type': 'input', 'index': 34, 'id': 'jx-id-6805-58', 'name': 'no-name', 'placeholder': '开始日期', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 35: {'type': 'input', 'index': 35, 'id': 'jx-id-6805-59', 'name': 'no-name', 'placeholder': '开始时间', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 36: {'type': 'input', 'index': 36, 'id': 'jx-id-6805-60', 'name': 'no-name', 'placeholder': '结束日期', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 37: {'type': 'input', 'index': 37, 'id': 'jx-id-6805-61', 'name': 'no-name', 'placeholder': '结束时间', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 38: {'type': 'input', 'index': 38, 'id': 'J_dwh_logS', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'no-class', 'type_attr': 'text'}
  label 0: {'type': 'label', 'index': 0, 'id': 'jx-id-6805-20', 'text': '产品ID\n:', 'for': 'jx-id-6805-54'}
  label 1: {'type': 'label', 'index': 1, 'id': 'jx-id-6805-21', 'text': '产品名称\n:', 'for': 'jx-id-6805-55'}
  label 2: {'type': 'label', 'index': 2, 'id': 'jx-id-6805-22', 'text': '活动名称\n:', 'for': 'jx-id-6805-56'}
  label 3: {'type': 'label', 'index': 3, 'id': 'jx-id-6805-23', 'text': '活动ID\n:', 'for': 'jx-id-6805-57'}
  label 4: {'type': 'label', 'index': 4, 'id': 'no-id', 'text': '全部(1381)', 'for': 'no-for'}
  label 5: {'type': 'label', 'index': 5, 'id': 'no-id', 'text': '进行中(426)', 'for': 'no-for'}
  label 6: {'type': 'label', 'index': 6, 'id': 'no-id', 'text': '未开始(15)', 'for': 'no-for'}
  label 7: {'type': 'label', 'index': 7, 'id': 'no-id', 'text': '已终止(0)', 'for': 'no-for'}
  label 8: {'type': 'label', 'index': 8, 'id': 'no-id', 'text': '已结束(940)', 'for': 'no-for'}
  label 9: {'type': 'label', 'index': 9, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 10: {'type': 'label', 'index': 10, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 11: {'type': 'label', 'index': 11, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 12: {'type': 'label', 'index': 12, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 13: {'type': 'label', 'index': 13, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 14: {'type': 'label', 'index': 14, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 15: {'type': 'label', 'index': 15, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 16: {'type': 'label', 'index': 16, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 17: {'type': 'label', 'index': 17, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 18: {'type': 'label', 'index': 18, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 19: {'type': 'label', 'index': 19, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 20: {'type': 'label', 'index': 20, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 21: {'type': 'label', 'index': 21, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 22: {'type': 'label', 'index': 22, 'id': 'no-id', 'text': '全选', 'for': 'no-for'}
  label 23: {'type': 'label', 'index': 23, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 24: {'type': 'label', 'index': 24, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  button 0: {'type': 'button', 'index': 0, 'id': 'no-id', 'text': '搜索', 'className': 'jx-button jx-button--primary jx-button--small pro-button J_queryFormSearch'}
  button 1: {'type': 'button', 'index': 1, 'id': 'no-id', 'text': '重置', 'className': 'jx-button jx-button--small pro-button'}
  button 2: {'type': 'button', 'index': 2, 'id': 'no-id', 'text': '创建活动', 'className': 'jx-button jx-button--primary jx-button--small pro-button'}
  button 3: {'type': 'button', 'index': 3, 'id': 'no-id', 'text': '批量终止', 'className': 'jx-button jx-button--primary jx-button--small pro-button'}
  button 4: {'type': 'button', 'index': 4, 'id': 'no-id', 'text': '自动延续活动', 'className': 'jx-button jx-button--primary jx-button--small pro-button'}
  button 5: {'type': 'button', 'index': 5, 'id': 'no-id', 'text': '自动延续跟踪记录\n8', 'className': 'jx-button jx-button--primary jx-button--small pro-button'}
  button 6: {'type': 'button', 'index': 6, 'id': 'no-id', 'text': '同步活动', 'className': 'jx-button jx-button--primary jx-button--small pro-button'}
  button 7: {'type': 'button', 'index': 7, 'id': 'no-id', 'text': '管理产品', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 8: {'type': 'button', 'index': 8, 'id': 'no-id', 'text': '编辑', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 9: {'type': 'button', 'index': 9, 'id': 'no-id', 'text': '终止', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 10: {'type': 'button', 'index': 10, 'id': 'no-id', 'text': '复制', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 11: {'type': 'button', 'index': 11, 'id': 'no-id', 'text': '同步', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 12: {'type': 'button', 'index': 12, 'id': 'no-id', 'text': '管理产品', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 13: {'type': 'button', 'index': 13, 'id': 'no-id', 'text': '编辑', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 14: {'type': 'button', 'index': 14, 'id': 'no-id', 'text': '终止', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 15: {'type': 'button', 'index': 15, 'id': 'no-id', 'text': '复制', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 16: {'type': 'button', 'index': 16, 'id': 'no-id', 'text': '同步', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 17: {'type': 'button', 'index': 17, 'id': 'no-id', 'text': '管理产品', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 18: {'type': 'button', 'index': 18, 'id': 'no-id', 'text': '编辑', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 19: {'type': 'button', 'index': 19, 'id': 'no-id', 'text': '终止', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 20: {'type': 'button', 'index': 20, 'id': 'no-id', 'text': '复制', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 21: {'type': 'button', 'index': 21, 'id': 'no-id', 'text': '同步', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 22: {'type': 'button', 'index': 22, 'id': 'no-id', 'text': '管理产品', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 23: {'type': 'button', 'index': 23, 'id': 'no-id', 'text': '编辑', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 24: {'type': 'button', 'index': 24, 'id': 'no-id', 'text': '终止', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 25: {'type': 'button', 'index': 25, 'id': 'no-id', 'text': '复制', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 26: {'type': 'button', 'index': 26, 'id': 'no-id', 'text': '同步', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 27: {'type': 'button', 'index': 27, 'id': 'no-id', 'text': '管理产品', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 28: {'type': 'button', 'index': 28, 'id': 'no-id', 'text': '编辑', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 29: {'type': 'button', 'index': 29, 'id': 'no-id', 'text': '终止', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 30: {'type': 'button', 'index': 30, 'id': 'no-id', 'text': '复制', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 31: {'type': 'button', 'index': 31, 'id': 'no-id', 'text': '同步', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 32: {'type': 'button', 'index': 32, 'id': 'no-id', 'text': '管理产品', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 33: {'type': 'button', 'index': 33, 'id': 'no-id', 'text': '编辑', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 34: {'type': 'button', 'index': 34, 'id': 'no-id', 'text': '终止', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 35: {'type': 'button', 'index': 35, 'id': 'no-id', 'text': '复制', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 36: {'type': 'button', 'index': 36, 'id': 'no-id', 'text': '同步', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 37: {'type': 'button', 'index': 37, 'id': 'no-id', 'text': '管理产品', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 38: {'type': 'button', 'index': 38, 'id': 'no-id', 'text': '编辑', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 39: {'type': 'button', 'index': 39, 'id': 'no-id', 'text': '终止', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 40: {'type': 'button', 'index': 40, 'id': 'no-id', 'text': '复制', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 41: {'type': 'button', 'index': 41, 'id': 'no-id', 'text': '同步', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 42: {'type': 'button', 'index': 42, 'id': 'no-id', 'text': '管理产品', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 43: {'type': 'button', 'index': 43, 'id': 'no-id', 'text': '编辑', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 44: {'type': 'button', 'index': 44, 'id': 'no-id', 'text': '终止', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 45: {'type': 'button', 'index': 45, 'id': 'no-id', 'text': '复制', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 46: {'type': 'button', 'index': 46, 'id': 'no-id', 'text': '同步', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 47: {'type': 'button', 'index': 47, 'id': 'no-id', 'text': '管理产品', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 48: {'type': 'button', 'index': 48, 'id': 'no-id', 'text': '编辑', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 49: {'type': 'button', 'index': 49, 'id': 'no-id', 'text': '终止', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 50: {'type': 'button', 'index': 50, 'id': 'no-id', 'text': '复制', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 51: {'type': 'button', 'index': 51, 'id': 'no-id', 'text': '同步', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 52: {'type': 'button', 'index': 52, 'id': 'no-id', 'text': '管理产品', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 53: {'type': 'button', 'index': 53, 'id': 'no-id', 'text': '编辑', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 54: {'type': 'button', 'index': 54, 'id': 'no-id', 'text': '终止', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 55: {'type': 'button', 'index': 55, 'id': 'no-id', 'text': '复制', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 56: {'type': 'button', 'index': 56, 'id': 'no-id', 'text': '同步', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 57: {'type': 'button', 'index': 57, 'id': 'no-id', 'text': '管理产品', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 58: {'type': 'button', 'index': 58, 'id': 'no-id', 'text': '编辑', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 59: {'type': 'button', 'index': 59, 'id': 'no-id', 'text': '终止', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 60: {'type': 'button', 'index': 60, 'id': 'no-id', 'text': '复制', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 61: {'type': 'button', 'index': 61, 'id': 'no-id', 'text': '同步', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 62: {'type': 'button', 'index': 62, 'id': 'no-id', 'text': '全选', 'className': 'jx-button jx-button--small pro-button'}
  button 63: {'type': 'button', 'index': 63, 'id': 'no-id', 'text': '反选', 'className': 'jx-button jx-button--small pro-button'}
  button 64: {'type': 'button', 'index': 64, 'id': 'no-id', 'text': 'no-text', 'className': 'btn-prev'}
  button 65: {'type': 'button', 'index': 65, 'id': 'no-id', 'text': 'no-text', 'className': 'btn-next'}
  button 66: {'type': 'button', 'index': 66, 'id': 'no-id', 'text': '今天', 'className': 'jx-picker-panel__shortcut'}
  button 67: {'type': 'button', 'index': 67, 'id': 'no-id', 'text': '昨天', 'className': 'jx-picker-panel__shortcut'}
  button 68: {'type': 'button', 'index': 68, 'id': 'no-id', 'text': '近7天', 'className': 'jx-picker-panel__shortcut'}
  button 69: {'type': 'button', 'index': 69, 'id': 'no-id', 'text': '近30天', 'className': 'jx-picker-panel__shortcut'}
  button 70: {'type': 'button', 'index': 70, 'id': 'no-id', 'text': '近90天', 'className': 'jx-picker-panel__shortcut'}
  button 71: {'type': 'button', 'index': 71, 'id': 'no-id', 'text': 'no-text', 'className': 'jx-picker-panel__icon-btn d-arrow-left'}
  button 72: {'type': 'button', 'index': 72, 'id': 'no-id', 'text': 'no-text', 'className': 'jx-picker-panel__icon-btn arrow-left'}
  button 73: {'type': 'button', 'index': 73, 'id': 'no-id', 'text': 'no-text', 'className': 'jx-picker-panel__icon-btn d-arrow-right'}
  button 74: {'type': 'button', 'index': 74, 'id': 'no-id', 'text': 'no-text', 'className': 'jx-picker-panel__icon-btn arrow-right'}
  button 75: {'type': 'button', 'index': 75, 'id': 'no-id', 'text': '清空', 'className': 'jx-button jx-button--small is-text jx-picker-panel__link-btn'}
  button 76: {'type': 'button', 'index': 76, 'id': 'no-id', 'text': '确定', 'className': 'jx-button jx-button--small is-disabled is-plain jx-picker-panel__link-btn'}
