#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指纹浏览器连接测试脚本
用于检查指纹浏览器服务是否正常运行
"""

import requests
import json
from datetime import datetime

def test_fingerprint_browser_connection():
    """测试指纹浏览器连接"""
    print("=" * 60)
    print("🔍 指纹浏览器连接测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    base_url = "http://127.0.0.1:6873"
    
    # 测试1: 基础连接测试
    print("📡 测试1: 基础连接测试")
    try:
        response = requests.get(base_url, timeout=5)
        print(f"✅ 连接成功 - 状态码: {response.status_code}")
        if response.text:
            print(f"响应内容: {response.text[:200]}...")
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 无法连接到指纹浏览器服务")
        print("💡 请检查:")
        print("   1. 指纹浏览器管理程序是否已启动")
        print("   2. 服务是否监听6873端口")
        print("   3. 防火墙是否阻止了连接")
        return False
    except requests.exceptions.Timeout:
        print("❌ 连接超时")
        return False
    except Exception as e:
        print(f"❌ 连接异常: {e}")
        return False
    
    print()
    
    # 测试2: API端点测试
    print("📡 测试2: API端点测试")
    api_endpoints = [
        "/api/v1/browser",
        "/api/v1/browser/list",
        "/api/v1/browser/start"
    ]
    
    for endpoint in api_endpoints:
        url = base_url + endpoint
        try:
            if endpoint == "/api/v1/browser/start":
                # POST请求测试（不实际启动）
                print(f"🔍 测试POST {endpoint} (仅检查端点)")
                continue
            else:
                # GET请求测试
                response = requests.get(url, timeout=5)
                print(f"✅ {endpoint} - 状态码: {response.status_code}")
                
                if response.headers.get('content-type', '').startswith('application/json'):
                    try:
                        json_data = response.json()
                        print(f"   响应: {json.dumps(json_data, ensure_ascii=False)[:100]}...")
                    except:
                        print(f"   响应: {response.text[:100]}...")
                        
        except requests.exceptions.ConnectionError:
            print(f"❌ {endpoint} - 连接失败")
        except Exception as e:
            print(f"⚠️  {endpoint} - 异常: {e}")
    
    print()
    
    # 测试3: 环境列表获取
    print("📡 测试3: 获取环境列表")
    try:
        list_url = base_url + "/api/v1/browser/list"
        response = requests.get(list_url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 环境列表获取成功")
            print(f"响应代码: {data.get('code', 'N/A')}")
            
            if 'data' in data and isinstance(data['data'], list):
                env_count = len(data['data'])
                print(f"环境数量: {env_count}")
                
                if env_count > 0:
                    print("前3个环境:")
                    for i, env in enumerate(data['data'][:3]):
                        container_code = env.get('containerCode', 'N/A')
                        name = env.get('name', 'N/A')
                        status = env.get('status', 'N/A')
                        print(f"  {i+1}. {container_code} - {name} (状态: {status})")
                else:
                    print("⚠️  没有找到可用环境")
            else:
                print("⚠️  响应格式异常")
                print(f"响应内容: {json.dumps(data, ensure_ascii=False)[:200]}...")
        else:
            print(f"❌ 获取环境列表失败 - 状态码: {response.status_code}")
            print(f"响应: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ 获取环境列表异常: {e}")
    
    print()
    
    # 测试4: 测试启动API（不实际启动）
    print("📡 测试4: 测试启动API格式")
    start_url = base_url + "/api/v1/browser/start"
    test_data = {
        "containerCode": "test_env_123",
        "skipSystemResourceCheck": True
    }
    
    print(f"启动API URL: {start_url}")
    print(f"测试数据格式: {json.dumps(test_data, ensure_ascii=False)}")
    print("💡 注意: 这里不会实际发送启动请求，避免创建测试环境")
    
    print()
    print("=" * 60)
    print("🎯 测试总结")
    print("=" * 60)
    print("如果上述测试都通过，说明指纹浏览器服务运行正常")
    print("如果有失败项，请:")
    print("1. 确保指纹浏览器管理程序已启动")
    print("2. 检查端口6873是否被占用")
    print("3. 查看指纹浏览器程序的日志")
    print("4. 重启指纹浏览器管理程序")
    
    return True

def test_environment_config():
    """测试环境配置文件"""
    print("\n📋 测试环境配置文件")
    print("-" * 40)
    
    try:
        import pandas as pd
        from openpyxl import load_workbook
        
        env_file = "miaoshou_environment.xlsx"
        
        # 使用pandas读取
        df = pd.read_excel(env_file)
        print(f"✅ 环境配置文件读取成功")
        print(f"环境数量: {len(df)}")
        print("环境列表:")
        print(df.to_string(index=False))
        
        return True
        
    except FileNotFoundError:
        print(f"❌ 环境配置文件不存在: {env_file}")
        return False
    except Exception as e:
        print(f"❌ 读取环境配置文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 妙手ERP自动化 - 连接测试工具")
    
    # 测试指纹浏览器连接
    browser_ok = test_fingerprint_browser_connection()
    
    # 测试环境配置
    config_ok = test_environment_config()
    
    print("\n" + "=" * 60)
    print("🏁 最终结果")
    print("=" * 60)
    
    if browser_ok and config_ok:
        print("✅ 所有测试通过，可以运行主程序")
        print("💡 运行命令: python miaoshou_automation.py")
    else:
        print("❌ 部分测试失败，请解决问题后重试")
        if not browser_ok:
            print("   - 指纹浏览器连接问题")
        if not config_ok:
            print("   - 环境配置文件问题")

if __name__ == '__main__':
    main()
