#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
妙手ERP限时秒杀自动化程序 - 修复版本
专门修复JavaScript点击分页选择器的问题

主要修复：
1. 修复JavaScript选择器语法错误
2. 完善添加产品流程
3. 增强错误处理

作者：基于张丽原程序改编
日期：2025-07-10
"""

import sys
import time
import requests
from playwright import sync_api
import logging
import threading
from threading import Thread
from datetime import datetime
from logging import FileHandler
import pandas as pd
import os
from openpyxl import load_workbook
from typing import Dict, List, Optional
import gc
from concurrent.futures import ThreadPoolExecutor
from playwright.sync_api import sync_playwright
import json

# ==================== 全局配置变量 ====================

# 线程控制
max_threads = 1  # 修改为1个线程便于调试
semaphore = threading.Semaphore(max_threads)
chunk_size = 100

# 时间控制
wait_time = 0  # 程序启动前等待时间（秒）
operation_wait_time = 2  # 操作间隔时间（秒）
page_load_timeout = 30000  # 页面加载超时时间（毫秒）

# 重试配置
try_open_times = 5  # 尝试打开网页的次数
max_open_times = 1000  # 最大打开浏览器次数

# 文件路径配置
environment_path = "miaoshou_environment.xlsx"  # 环境配置文件
config_path = "miaoshou_config.json"  # 配置文件

# 妙手ERP相关URL
MIAOSHOU_BASE_URL = "https://erp.91miaoshou.com"
FLASH_SALE_URL = f"{MIAOSHOU_BASE_URL}/tiktok/marketing/flashSale"

# 浏览器计数器
open_counter = 0

# 当前日期
current_date = datetime.now().strftime('%Y-%m-%d %H-%M-%S')

# ==================== 工具函数 ====================

# 创建全局锁对象用于Excel写入
excel_lock = threading.Lock()

def write_to_excel_realtime(data: Dict, file_name: str):
    """实时写入Excel文件"""
    with excel_lock:
        reports_dir = 'reports'
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        file_path = os.path.join(reports_dir, file_name)

        if os.path.exists(file_path):
            df = pd.read_excel(file_path)
        else:
            df = pd.DataFrame()

        new_row = pd.DataFrame([data])
        df = pd.concat([df, new_row], ignore_index=True)
        df.to_excel(file_path, index=False, engine='openpyxl')

def load_config(config_file: str = config_path) -> Dict:
    """加载配置文件"""
    default_config = {
        "flash_sale": {
            "discount_percentage": 20,  # 默认折扣百分比
            "limit_quantity": 100,      # 默认限购数量
            "activity_duration": 24     # 活动持续时间（小时）
        },
        "selectors": {
            "create_activity_btn": "//button[contains(text(), '创建活动')]",
            "activity_name_input": "//input[@placeholder='请输入活动名称']",
            "add_product_btn": "//button[contains(text(), '添加产品')]",
            "discount_input": "//input[@placeholder='请输入折扣']",
            "quantity_input": "//input[@placeholder='请输入限购数量')]"
        }
    }
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 合并默认配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
        except Exception as e:
            print(f"加载配置文件失败，使用默认配置: {e}")
    
    # 创建默认配置文件
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(default_config, f, ensure_ascii=False, indent=2)
    
    return default_config

# ==================== 核心自动化类 ====================

class MiaoshouAutomation:
    """妙手ERP自动化操作类"""
    
    def __init__(self, container_code: str, debugging_port: int, logger: logging.Logger, env_name: str):
        """
        初始化妙手自动化实例
        
        Args:
            container_code: 容器代码
            debugging_port: 调试端口
            logger: 日志记录器
            env_name: 环境名称
        """
        self.container_code = container_code
        self.debugging_port = debugging_port
        self.logger = logger
        self.env_name = env_name
        self.config = load_config()
        
        # 初始化Playwright
        self.playwright = sync_api.sync_playwright().start()
        self.browser, self.browser_context = self.get_browser_context(
            self.playwright, self.debugging_port)
    
    @staticmethod
    def get_browser_context(playwright, port: int):
        """获取浏览器上下文"""
        browser = playwright.chromium.connect_over_cdp(f"http://127.0.0.1:{port}")
        context = browser.contexts[0]
        return browser, context
    
    def close_browser(self):
        """关闭浏览器"""
        url = 'http://127.0.0.1:6873/api/v1/browser/stop'
        data = {"containerCode": self.container_code}
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 200:
                res_json = response.json()
                if res_json.get('code') == 0:
                    self.logger.info(f"成功关闭了容器代码为 {self.container_code} 的浏览器")
                else:
                    self.logger.error(f"关闭容器代码为 {self.container_code} 的浏览器失败。错误：{res_json.get('msg')}")
            else:
                self.logger.error(f"关闭容器代码为 {self.container_code} 的浏览器失败。HTTP状态码：{response.status_code}")
                self.close_browser_with_playwright()
        except Exception as e:
            self.logger.error(f"关闭浏览器时发生异常：{e}")
            self.close_browser_with_playwright()
    
    def close_browser_with_playwright(self):
        """使用Playwright关闭浏览器"""
        try:
            self.browser.close()
            self.logger.info(f"使用Playwright成功关闭了容器代码为 {self.container_code} 的浏览器")
        except Exception as e:
            self.logger.error(f"使用Playwright关闭容器代码为 {self.container_code} 的浏览器失败。异常：{e}")
    
    def close_other_pages(self, current_page):
        """关闭除当前页面外的所有页面"""
        for page in self.browser_context.pages:
            if page != current_page:
                page.close()
        time.sleep(30)
        self.close_browser()
    
    def wait_and_click(self, page, xpath: str, description: str = "", timeout: int = 10000, retries: int = 3) -> bool:
        """等待元素并点击"""
        for attempt in range(retries):
            try:
                page.wait_for_selector(f"xpath={xpath}", timeout=timeout)
                
                js_script = f'''
                (function() {{
                    const xpath = "{xpath}";
                    const element = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
                    if (element) {{
                        const clickEvent = new MouseEvent("click", {{
                            bubbles: true,
                            cancelable: true,
                            view: window
                        }});
                        element.dispatchEvent(clickEvent);
                        return true;
                    }}
                    return false;
                }})()
                '''
                
                result = page.evaluate(js_script)
                if result:
                    self.logger.info(f"成功点击元素: {description or xpath}")
                    return True
                else:
                    self.logger.warning(f"元素存在但点击失败: {description or xpath}")
                    
            except Exception as e:
                self.logger.error(f"点击元素时发生异常 (尝试 {attempt + 1}/{retries}): {e}")
                
        self.logger.error(f"点击元素失败，超过重试次数: {description or xpath}")
        return False
    
    def input_text(self, page, xpath: str, text: str, description: str = "", clear_first: bool = True) -> bool:
        """输入文本到指定元素"""
        try:
            page.wait_for_selector(f"xpath={xpath}", timeout=10000)
            element = page.locator(f"xpath={xpath}")
            
            if clear_first:
                element.clear()
            
            element.fill(text)
            self.logger.info(f"成功输入文本到 {description or xpath}: {text}")
            return True
            
        except Exception as e:
            self.logger.error(f"输入文本失败 {description or xpath}: {e}")
            return False

    def save_page_debug_info(self, page, step_name: str):
        """保存页面调试信息：HTML源码和截图"""
        try:
            import os
            from datetime import datetime

            # 创建调试目录
            debug_dir = "debug_pages"
            if not os.path.exists(debug_dir):
                os.makedirs(debug_dir)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存HTML源码
            html_content = page.content()
            html_file = os.path.join(debug_dir, f"{step_name}_{timestamp}.html")
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            self.logger.info(f"📄 已保存HTML源码: {html_file}")

            # 保存截图
            screenshot_file = os.path.join(debug_dir, f"{step_name}_{timestamp}.png")
            page.screenshot(path=screenshot_file, full_page=True)
            self.logger.info(f"📸 已保存页面截图: {screenshot_file}")

            return True

        except Exception as e:
            self.logger.warning(f"保存页面调试信息失败: {e}")
            return False

    def add_products_to_flash_sale(self) -> bool:
        """
        为未开始的秒杀活动添加产品 - 完整流程

        Returns:
            bool: 添加是否成功
        """
        try:
            self.logger.info("🚀 开始为未开始的秒杀活动添加产品")

            # 第0步：关闭多余的标签页
            self.logger.info("🎯 第0步：关闭多余的标签页")
            all_pages = self.browser_context.pages
            self.logger.info(f"当前打开的标签页数量: {len(all_pages)}")

            # 关闭除第一个之外的所有标签页
            for i, page in enumerate(all_pages):
                if i > 0:  # 保留第一个标签页
                    try:
                        self.logger.info(f"关闭标签页 {i+1}: {page.url}")
                        page.close()
                    except Exception as e:
                        self.logger.warning(f"关闭标签页 {i+1} 失败: {e}")

            # 使用第一个标签页
            main_page = all_pages[0]
            self.logger.info("✅ 已关闭多余标签页，使用第一个标签页")

            # 第一步：导航到秒杀活动页面
            self.logger.info("🎯 第一步：导航到秒杀活动页面")
            main_page.goto(FLASH_SALE_URL, timeout=page_load_timeout)
            main_page.wait_for_load_state("domcontentloaded", timeout=page_load_timeout)
            self.logger.info("✅ 页面DOM加载完成")

            # 等待页面完全加载
            time.sleep(5)
            self.logger.info("✅ 成功导航到秒杀活动页面")

            # 第二步：点击未开始标签
            self.logger.info("🎯 第二步：点击未开始标签")

            # 等待页面稳定
            wait_seconds = 2 + (time.time() % 2)  # 2-4秒随机等待
            self.logger.info(f"等待页面稳定 {wait_seconds:.1f} 秒...")
            time.sleep(wait_seconds)

            # 点击未开始标签
            not_started_selectors = [
                "//label[contains(@class, 'pro-radio-button')][contains(., '未开始')]",
                "//label[contains(@class, 'jx-radio-button')][contains(., '未开始')]",
                "//span[contains(text(), '未开始')]/parent::label",
                "//label[contains(., '未开始')]"
            ]

            not_started_clicked = False
            for i, selector in enumerate(not_started_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击未开始标签: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    elements = main_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个未开始标签")

                    if elements:
                        element = elements[0]
                        is_visible = element.is_visible()
                        self.logger.info(f"     元素可见性: {is_visible}")

                        if is_visible:
                            element.scroll_into_view_if_needed()
                            time.sleep(0.5)
                            element.click()
                            self.logger.info(f"  ✅ 成功点击未开始标签: 方法{i+1}")
                            time.sleep(2)
                            not_started_clicked = True
                            break
                        else:
                            self.logger.warning(f"     未开始标签不可见: 方法{i+1}")
                    else:
                        self.logger.warning(f"     未找到未开始标签: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击未开始标签失败: 方法{i+1} - {e}")
                    continue

            if not not_started_clicked:
                self.logger.error("❌ 第二步失败：无法点击未开始标签")
                return False

            self.logger.info("✅ 成功点击未开始标签")

            # 第三步：点击第一个活动的管理产品
            self.logger.info("🎯 第三步：点击第一个活动的管理产品")

            # 等待活动列表加载
            wait_seconds = 3 + (time.time() % 1)  # 3-4秒随机等待
            self.logger.info(f"等待活动列表加载 {wait_seconds:.1f} 秒...")
            time.sleep(wait_seconds)

            # 保存点击未开始后的页面状态
            self.logger.info("🔍 保存点击未开始后的页面状态...")
            self.save_page_debug_info(main_page, "after_click_not_started")

            # 调试：查看页面上的所有按钮和链接
            self.logger.info("🔍 调试：查看页面上的所有按钮和链接...")
            try:
                js_script = '''
                (function() {
                    const elements = [];
                    // 获取所有button和span元素
                    document.querySelectorAll('button, span, a').forEach((el, index) => {
                        const text = el.innerText.trim();
                        if (text && (text.includes('管理产品') || text.includes('编辑') || text.includes('产品'))) {
                            elements.push({
                                tagName: el.tagName,
                                index: index,
                                text: text,
                                className: el.className,
                                visible: el.offsetParent !== null
                            });
                        }
                    });
                    return elements;
                })()
                '''

                buttons = main_page.evaluate(js_script)
                self.logger.info(f"找到 {len(buttons)} 个可能的操作按钮:")

                for btn in buttons[:50]:  # 显示前50个
                    self.logger.info(f"  {btn['tagName']} {btn['index']}: '{btn['text']}' (可见: {btn['visible']})")

            except Exception as e:
                self.logger.warning(f"调试按钮失败: {e}")

            # 点击第一个管理产品按钮
            manage_product_selectors = [
                "(//button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button')][contains(., '管理产品')])[1]",
                "(//button[contains(., '管理产品')])[1]",
                "(//span[contains(text(), '管理产品')]/parent::button)[1]",
                "//button[contains(@class, 'jx-button--primary')][contains(., '管理产品')]"
            ]

            manage_clicked = False
            for i, selector in enumerate(manage_product_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击管理产品: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    elements = main_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个管理产品按钮")

                    if elements:
                        element = elements[0]
                        is_visible = element.is_visible()
                        self.logger.info(f"     元素可见性: {is_visible}")

                        if is_visible:
                            element.scroll_into_view_if_needed()
                            time.sleep(0.5)
                            element.click()
                            self.logger.info(f"  ✅ 成功点击管理产品: 方法{i+1}")
                            time.sleep(3)
                            manage_clicked = True
                            break
                        else:
                            self.logger.warning(f"     管理产品按钮不可见: 方法{i+1}")
                    else:
                        self.logger.warning(f"     未找到管理产品按钮: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击管理产品失败: 方法{i+1} - {e}")
                    continue

            if not manage_clicked:
                self.logger.error("❌ 第三步失败：无法点击管理产品")
                return False

            self.logger.info("✅ 成功点击管理产品按钮")

            # 第四步：处理新标签页并添加产品
            self.logger.info("🎯 第四步：处理新标签页并添加产品")

            # 等待新标签页打开
            self.logger.info("等待新标签页打开...")
            product_page = None

            # 等待新标签页出现，最多等待15秒
            for attempt in range(15):
                time.sleep(1)
                all_pages = self.browser_context.pages
                self.logger.info(f"等待尝试 {attempt+1}/15: 当前标签页数量 {len(all_pages)}")

                # 查找新打开的产品管理标签页
                for i, p in enumerate(all_pages):
                    try:
                        page_url = p.url
                        page_title = p.title()
                        self.logger.info(f"  标签页 {i+1}: URL={page_url}, 标题={page_title}")

                        # 检查是否是产品管理页面
                        if ("/create" in page_url and "step=2" in page_url) or "管理活动产品" in page_title:
                            product_page = p
                            self.logger.info("✅ 检测到新标签页已打开")
                            break
                    except Exception as e:
                        self.logger.warning(f"检查标签页 {i+1} 失败: {e}")
                        continue

                if product_page:
                    break

            if not product_page:
                self.logger.error("❌ 第四步失败：未检测到新标签页")
                return False

            # 切换到产品管理页面
            self.logger.info(f"当前打开的标签页数量: {len(self.browser_context.pages)}")
            self.logger.info(f"切换到产品管理页面: {product_page.url}")

            # 等待产品管理页面加载
            self.logger.info("等待产品管理页面加载...")
            try:
                product_page.wait_for_load_state("domcontentloaded", timeout=15000)
                self.logger.info("DOM内容加载完成，继续等待页面稳定...")

                # 等待网络空闲
                try:
                    product_page.wait_for_load_state('networkidle', timeout=15000)
                    self.logger.info("网络空闲状态达成")
                except:
                    self.logger.warning("网络空闲等待超时，继续执行")

                # 再等待一段时间确保所有元素都已渲染
                time.sleep(3)
                self.logger.info("✅ 产品管理页面加载完成")

            except Exception as e:
                self.logger.warning(f"等待页面加载失败: {e}")

            # 保存产品管理页面的调试信息
            self.logger.info("🔍 保存产品管理页面调试信息...")
            self.save_page_debug_info(product_page, "product_management_page")

            return self._handle_add_products_dialog(product_page)

        except Exception as e:
            self.logger.error(f"添加产品到秒杀活动失败: {e}")
            return False

        finally:
            # 保持页面打开以便检查结果
            self.logger.info("🔍 页面保持打开状态，便于检查结果...")

    def _handle_add_products_dialog(self, product_page) -> bool:
        """处理添加产品对话框的完整流程"""
        try:
            # 调试：查看产品管理页面上的所有按钮
            self.logger.info("🔍 调试：查看产品管理页面上的所有按钮...")
            try:
                js_script = '''
                (function() {
                    const buttons = document.querySelectorAll('button, a');
                    const buttonInfo = [];
                    buttons.forEach((btn, index) => {
                        const text = btn.innerText.trim();
                        if (text) {
                            buttonInfo.push({
                                tagName: btn.tagName,
                                index: index,
                                text: text,
                                className: btn.className,
                                visible: btn.offsetParent !== null
                            });
                        }
                    });
                    return buttonInfo;
                })()
                '''

                buttons = product_page.evaluate(js_script)
                self.logger.info(f"找到 {len(buttons)} 个按钮:")

                for btn in buttons[:12]:  # 显示前12个按钮
                    self.logger.info(f"  {btn['tagName']} {btn['index']}: '{btn['text']}' (可见: {btn['visible']})")

            except Exception as e:
                self.logger.warning(f"调试按钮失败: {e}")

            # 第五步：点击添加产品按钮
            self.logger.info("🎯 第五步：点击添加产品按钮")

            # 再等待一下确保按钮完全加载
            time.sleep(2)

            # 添加产品按钮的选择器
            add_product_selectors = [
                "//button[contains(@class, 'jx-button--primary') and contains(@class, 'pro-button')][contains(., '添加产品')]",
                "//span[contains(text(), '添加产品')]/parent::button",
                "//div[contains(@class, 'operate-left-box')]//button[contains(., '添加产品')]",
                "//button[contains(text(), '添加产品')]",
                "//button[@aria-disabled='false'][contains(., '添加产品')]"
            ]

            add_product_clicked = False
            for i, selector in enumerate(add_product_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击添加产品: 方法{i+1}")
                    self.logger.info(f"     选择器: {selector}")

                    elements = product_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个添加产品按钮")

                    if elements:
                        element = elements[0]
                        is_visible = element.is_visible()
                        self.logger.info(f"     元素可见性: {is_visible}")

                        if is_visible:
                            element.scroll_into_view_if_needed()
                            time.sleep(0.5)
                            element.click()
                            self.logger.info(f"  ✅ 成功点击添加产品: 方法{i+1}")
                            time.sleep(2)
                            add_product_clicked = True
                            break
                        else:
                            self.logger.warning(f"     添加产品按钮不可见: 方法{i+1}")
                    else:
                        self.logger.warning(f"     未找到添加产品按钮: 方法{i+1}")

                except Exception as e:
                    self.logger.warning(f"  ❌ 点击添加产品失败: 方法{i+1} - {e}")
                    continue

            if not add_product_clicked:
                self.logger.error("❌ 第五步失败：无法点击添加产品")
                self.save_page_debug_info(product_page, "failed_click_add_product")
                return False

            self.logger.info("✅ 成功点击添加产品按钮")

            # 等待添加产品弹窗加载
            time.sleep(6)

            # 第六步：处理添加产品弹窗
            self.logger.info("🎯 第六步：处理添加产品弹窗")

            # 步骤1：勾选"隐藏已参与限时秒杀的产品"
            self.logger.info("📋 步骤1：勾选隐藏已参与限时秒杀的产品")
            time.sleep(2)

            hide_checkbox_selectors = [
                "//label[contains(@class, 'jx-checkbox')][contains(., '隐藏已参与限时秒杀的产品')]",
                "//input[@type='checkbox'][following-sibling::*[contains(text(), '隐藏已参与限时秒杀的产品')]]",
                "//span[contains(text(), '隐藏已参与限时秒杀的产品')]/parent::label",
                "//label[contains(@class, 'jx-checkbox') and contains(@class, 'pro-checkbox')][contains(., '隐藏已参与限时秒杀的产品')]"
            ]

            hide_checkbox_clicked = False
            for i, selector in enumerate(hide_checkbox_selectors):
                try:
                    self.logger.info(f"  🎯 尝试勾选隐藏复选框: 方法{i+1}")
                    elements = product_page.query_selector_all(f"xpath={selector}")

                    if elements:
                        element = elements[0]
                        if element.is_visible():
                            element.click()
                            self.logger.info(f"  ✅ 成功勾选隐藏复选框: 方法{i+1}")
                            time.sleep(3)
                            hide_checkbox_clicked = True
                            break
                except Exception as e:
                    self.logger.warning(f"  ❌ 勾选隐藏复选框失败: 方法{i+1} - {e}")

            if not hide_checkbox_clicked:
                self.logger.warning("⚠️ 未能勾选隐藏复选框，继续执行")

            # 等待复选框操作完成，页面数据更新
            time.sleep(4)

            # 步骤2：获取可添加产品总数量
            self.logger.info("📊 步骤2：获取可添加产品总数量")

            total_count = 0
            total_selectors = [
                "//div[contains(@class, 'jx-pagination__total')]//span",
                "//span[contains(text(), '条')]",
                "//div[contains(@class, 'jx-pagination__total')]",
                "//div[contains(@class, 'pagination')]//span[contains(text(), '条')]",
                "//*[contains(text(), '条') and contains(text(), '共')]"
            ]

            # 先尝试获取页面上所有可能包含数量信息的元素
            self.logger.info("🔍 调试：查找所有可能包含数量信息的元素...")
            try:
                all_text_elements = product_page.query_selector_all("xpath=//*[contains(text(), '条')]")
                self.logger.info(f"找到 {len(all_text_elements)} 个包含'条'的元素:")
                for i, elem in enumerate(all_text_elements[:10]):
                    try:
                        text = elem.inner_text().strip()
                        if text:
                            self.logger.info(f"  元素 {i+1}: '{text}'")
                    except:
                        pass
            except Exception as e:
                self.logger.warning(f"调试查找元素失败: {e}")

            # 尝试获取总数
            for i, selector in enumerate(total_selectors):
                try:
                    self.logger.info(f"🎯 尝试获取总数: 方法{i+1} - {selector}")
                    elements = product_page.query_selector_all(f"xpath={selector}")
                    self.logger.info(f"     找到 {len(elements)} 个匹配元素")

                    if elements:
                        for j, element in enumerate(elements):
                            try:
                                text = element.inner_text().strip()
                                self.logger.info(f"     元素 {j+1} 文本: '{text}'")

                                # 提取数字
                                import re
                                numbers = re.findall(r'\d+', text)
                                if numbers:
                                    potential_count = int(numbers[0])
                                    self.logger.info(f"     提取到数字: {potential_count}")

                                    # 如果数字看起来合理（不是页码等小数字）
                                    if potential_count > 0:
                                        total_count = potential_count
                                        self.logger.info(f"📊 检测到可添加产品总数: {total_count}")
                                        break
                            except Exception as e:
                                self.logger.warning(f"     处理元素文本失败: {e}")

                        if total_count > 0:
                            break

                except Exception as e:
                    self.logger.warning(f"获取产品总数失败: 方法{i+1} - {e}")

            if total_count == 0:
                self.logger.warning("⚠️ 未能获取产品总数，默认按小于1000处理")
                total_count = 500

            # 等待数据分析完成
            time.sleep(3)

            # 步骤3：根据产品数量选择不同的操作策略
            return self._handle_product_selection_strategy(product_page, total_count)

        except Exception as e:
            self.logger.error(f"处理添加产品对话框失败: {e}")
            return False

    def _ensure_hide_checkbox_and_set_pagination(self, product_page) -> bool:
        """确保隐藏复选框勾选并通过URL参数设置分页"""
        try:
            self.logger.info("🔧 通过URL参数同时设置隐藏筛选和分页大小")

            # 获取当前URL并分析参数
            current_url = product_page.url
            self.logger.info(f"📍 当前URL: {current_url}")

            # 构建新的URL，包含所有必要参数
            js_result = product_page.evaluate("""
                () => {
                    try {
                        const currentUrl = new URL(window.location.href);

                        // 设置分页参数
                        currentUrl.searchParams.set('pageSize', '500');
                        currentUrl.searchParams.set('page', '1');

                        // 设置隐藏已参与产品的参数（尝试多种可能的参数名）
                        currentUrl.searchParams.set('hideParticipated', 'true');
                        currentUrl.searchParams.set('hideJoined', 'true');
                        currentUrl.searchParams.set('excludeParticipated', 'true');
                        currentUrl.searchParams.set('onlyAvailable', 'true');

                        const newUrl = currentUrl.toString();

                        // 直接跳转到新URL
                        window.location.href = newUrl;

                        return { success: true, newUrl: newUrl };

                    } catch (e) {
                        console.error("URL参数设置失败:", e);
                        return { success: false, message: e.message };
                    }
                }
            """)

            if js_result and js_result.get('success'):
                self.logger.info(f"✅ URL参数设置成功，跳转到: {js_result.get('newUrl')}")

                # 等待页面重新加载
                self.logger.info("⏳ 等待页面重新加载...")
                time.sleep(8)

                # 等待页面加载完成
                try:
                    product_page.wait_for_load_state('networkidle', timeout=30000)
                    self.logger.info("✅ 页面重新加载完成")
                    return True
                except Exception as e:
                    self.logger.warning(f"等待页面加载超时: {e}")
                    return True  # 继续执行，可能页面已经加载完成

            else:
                self.logger.error(f"❌ URL参数设置失败: {js_result.get('message') if js_result else '未知错误'}")
                return False

        except Exception as e:
            self.logger.error(f"URL参数设置异常: {e}")
            return False

    def _handle_product_selection_strategy(self, product_page, total_count: int) -> bool:
        """处理产品选择策略 - 使用直接设置分页大小的方法"""
        try:
            if total_count > 1000:
                self.logger.info(f"📈 产品数量 {total_count} > 1000，执行分页策略")
                time.sleep(2)

                # 直接通过JavaScript设置分页大小为500，避免复杂的点击操作
                self.logger.info("� 直接设置分页大小为500条/页")

                try:
                    # 方法1: 直接修改分页组件的内部状态
                    js_set_result = product_page.evaluate("""
                        () => {
                            try {
                                // 查找分页组件并直接设置页面大小
                                const paginationElement = document.querySelector('.jx-pagination');
                                if (paginationElement && paginationElement.__vue__) {
                                    // 如果是Vue组件，直接修改数据
                                    const vueInstance = paginationElement.__vue__;
                                    if (vueInstance.pageSize !== undefined) {
                                        vueInstance.pageSize = 500;
                                        vueInstance.$emit('size-change', 500);
                                        return { success: true, method: "Vue组件直接设置", message: "成功设置pageSize为500" };
                                    }
                                }

                                // 方法2: 查找并触发分页大小变更事件
                                const sizeSelect = document.querySelector('.jx-pagination__sizes .jx-select');
                                if (sizeSelect && sizeSelect.__vue__) {
                                    const selectVue = sizeSelect.__vue__;
                                    if (selectVue.value !== undefined) {
                                        selectVue.value = 500;
                                        selectVue.$emit('input', 500);
                                        selectVue.$emit('change', 500);
                                        return { success: true, method: "Select组件直接设置", message: "成功设置select值为500" };
                                    }
                                }

                                // 方法3: 模拟URL参数变更来改变分页大小
                                const currentUrl = new URL(window.location.href);
                                currentUrl.searchParams.set('pageSize', '500');
                                currentUrl.searchParams.set('page', '1');
                                window.history.pushState({}, '', currentUrl.toString());

                                // 触发页面重新加载数据
                                const event = new Event('popstate');
                                window.dispatchEvent(event);

                                return { success: true, method: "URL参数设置", message: "通过URL参数设置分页大小为500" };

                            } catch (e) {
                                console.error("设置分页大小失败:", e);
                                return { success: false, message: `设置失败: ${e.message}` };
                            }
                        }
                    """)

                    if js_set_result and js_set_result.get('success'):
                        self.logger.info(f"  ✅ 分页设置成功: {js_set_result.get('method')} - {js_set_result.get('message')}")

                        # 等待页面更新
                        self.logger.info("⏳ 等待页面数据重新加载...")
                        time.sleep(5)

                        # 验证分页设置是否生效
                        try:
                            verification_result = product_page.evaluate("""
                                () => {
                                    // 检查当前页面显示的产品数量
                                    const productRows = document.querySelectorAll('tbody tr, .product-row, [data-row-key]');
                                    const currentPageSize = productRows.length;

                                    // 检查分页显示文本
                                    const paginationText = document.querySelector('.jx-pagination__total');
                                    const paginationInfo = paginationText ? paginationText.textContent : '';

                                    // 检查页面大小选择器的当前值
                                    const sizeSelector = document.querySelector('.jx-pagination__sizes .jx-select__placeholder span');
                                    const currentSizeText = sizeSelector ? sizeSelector.textContent : '';

                                    return {
                                        currentPageSize: currentPageSize,
                                        paginationInfo: paginationInfo,
                                        currentSizeText: currentSizeText
                                    };
                                }
                            """)

                            self.logger.info(f"📊 分页验证结果:")
                            self.logger.info(f"   当前页面产品数量: {verification_result.get('currentPageSize')}")
                            self.logger.info(f"   分页信息: {verification_result.get('paginationInfo')}")
                            self.logger.info(f"   页面大小选择器: {verification_result.get('currentSizeText')}")

                        except Exception as e:
                            self.logger.warning(f"验证分页设置失败: {e}")

                    else:
                        self.logger.warning(f"  ❌ 分页设置失败: {js_set_result.get('message') if js_set_result else '未知错误'}")
                        self.logger.info("🔄 分页设置失败，继续使用默认分页")

                except Exception as e:
                    self.logger.warning(f"设置分页大小异常: {e}")

                # 等待页面稳定
                time.sleep(2)
                self.logger.info("✅ 准备全选当前页面的产品")

            else:
                # 产品数量 <= 1000，执行简单策略
                self.logger.info(f"📊 产品数量 {total_count} <= 1000，执行简单全选策略")
                time.sleep(2)

            # 通用的全选和确定流程
            return self._select_all_and_confirm(product_page)

        except Exception as e:
            self.logger.error(f"处理产品选择策略失败: {e}")
            return False

    def _select_all_and_confirm(self, product_page) -> bool:
        """全选产品并确定 - 增强版本"""
        try:
            # 首先尝试JavaScript方法全选
            self.logger.info("🎯 尝试使用JavaScript方法全选产品")

            js_select_result = product_page.evaluate("""
                () => {
                    try {
                        // 方法1: 查找并点击表头的全选复选框
                        const headerCheckboxes = document.querySelectorAll('thead input[type="checkbox"], .jx-table__header input[type="checkbox"]');
                        for (const checkbox of headerCheckboxes) {
                            if (checkbox.offsetParent !== null) {
                                checkbox.click();
                                return { success: true, method: "表头复选框", message: "成功点击表头全选复选框" };
                            }
                        }

                        // 方法2: 查找jx-checkbox组件
                        const jxCheckboxes = document.querySelectorAll('thead .jx-checkbox, .jx-table__header .jx-checkbox');
                        for (const checkbox of jxCheckboxes) {
                            if (checkbox.offsetParent !== null) {
                                checkbox.click();
                                return { success: true, method: "JX复选框组件", message: "成功点击JX复选框组件" };
                            }
                        }

                        // 方法3: 查找所有可见的复选框并全选
                        const allCheckboxes = document.querySelectorAll('input[type="checkbox"]');
                        let checkedCount = 0;
                        for (const checkbox of allCheckboxes) {
                            if (checkbox.offsetParent !== null && !checkbox.checked) {
                                checkbox.click();
                                checkedCount++;
                            }
                        }

                        if (checkedCount > 0) {
                            return { success: true, method: "批量选择", message: `成功选择了${checkedCount}个复选框` };
                        }

                        // 方法4: 通过Vue组件直接操作
                        const tableElement = document.querySelector('.jx-table, table');
                        if (tableElement && tableElement.__vue__) {
                            const vueInstance = tableElement.__vue__;
                            if (vueInstance.selectAll && typeof vueInstance.selectAll === 'function') {
                                vueInstance.selectAll();
                                return { success: true, method: "Vue组件方法", message: "通过Vue组件selectAll方法全选" };
                            }
                        }

                        return { success: false, message: "未找到可操作的全选元素" };

                    } catch (e) {
                        console.error("JavaScript全选失败:", e);
                        return { success: false, message: `JavaScript执行失败: ${e.message}` };
                    }
                }
            """)

            select_all_clicked = False
            if js_select_result and js_select_result.get('success'):
                self.logger.info(f"  ✅ JavaScript全选成功: {js_select_result.get('method')} - {js_select_result.get('message')}")
                select_all_clicked = True
                time.sleep(2)
            else:
                self.logger.warning(f"  ❌ JavaScript全选失败: {js_select_result.get('message') if js_select_result else '未知错误'}")

                # 如果JavaScript方法失败，尝试传统XPath方法
                self.logger.info("🔄 JavaScript方法失败，尝试传统XPath方法")
                select_all_selectors = [
                    "//thead//th//input[@type='checkbox']",
                    "//thead//th//span[contains(@class, 'jx-checkbox')]",
                    "//table//thead//input[@type='checkbox']",
                    "//div[contains(@class, 'jx-table__header')]//input[@type='checkbox']",
                    "//th[1]//input[@type='checkbox']",
                    "//th[1]//span[contains(@class, 'jx-checkbox')]"
                ]

                for i, selector in enumerate(select_all_selectors):
                    try:
                        self.logger.info(f"  🎯 尝试点击全选复选框: 方法{i+1}")
                        elements = product_page.query_selector_all(f"xpath={selector}")
                        self.logger.info(f"     找到 {len(elements)} 个全选复选框")

                        if elements:
                            element = elements[0]
                            if element.is_visible():
                                element.click()
                                self.logger.info(f"  ✅ 成功点击全选复选框: 方法{i+1}")
                                time.sleep(2)
                                select_all_clicked = True
                                break
                    except Exception as e:
                        self.logger.warning(f"  ❌ 点击全选复选框失败: 方法{i+1} - {e}")

            if not select_all_clicked:
                self.logger.warning("⚠️ 未能点击全选复选框")
                return False

            # 等待选择完成
            time.sleep(3)

            # 点击确定按钮
            self.logger.info("🎯 点击确定按钮")

            confirm_selectors = [
                "//button[contains(@class, 'jx-button--primary')][contains(text(), '确定')]",
                "//button[contains(text(), '确定')]",
                "//span[contains(text(), '确定')]/parent::button"
            ]

            confirm_clicked = False
            for i, selector in enumerate(confirm_selectors):
                try:
                    self.logger.info(f"  🎯 尝试点击确定按钮: 方法{i+1}")
                    elements = product_page.query_selector_all(f"xpath={selector}")

                    if elements:
                        element = elements[0]
                        if element.is_visible():
                            element.click()
                            self.logger.info(f"  ✅ 成功点击确定按钮: 方法{i+1}")
                            time.sleep(3)
                            confirm_clicked = True
                            break
                except Exception as e:
                    self.logger.warning(f"  ❌ 点击确定按钮失败: 方法{i+1} - {e}")

            if not confirm_clicked:
                self.logger.error("❌ 无法点击确定按钮")
                return False

            # 等待产品添加完成
            self.logger.info("⏳ 等待产品添加完成...")
            time.sleep(5)

            self.logger.info("✅ 产品添加流程完成")
            return True

        except Exception as e:
            self.logger.error(f"全选和确定流程失败: {e}")
            return False

# ==================== 主程序运行逻辑 ====================

def setup_logger(container_code: str) -> logging.Logger:
    """设置日志记录器"""
    logger = logging.getLogger(f"miaoshou_{container_code}")
    logger.setLevel(logging.INFO)

    # 清除现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 创建日志目录
    logs_dir = 'logs'
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)

    # 创建文件处理器
    log_file = os.path.join(logs_dir, f"miaoshou_{container_code}_{datetime.now().strftime('%Y-%m-%d')}.log")
    file_handler = FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 创建格式器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s: %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器到日志记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

def start_browser(container_code: str) -> Optional[int]:
    """启动指纹浏览器"""
    url = 'http://127.0.0.1:6873/api/v1/browser/start'
    data = {
        "containerCode": container_code,
        "skipSystemResourceCheck": True
    }

    try:
        # 测试连接
        requests.get('http://127.0.0.1:6873/api/v1/browser/list', timeout=5)
        print("指纹浏览器服务连接测试成功")

        response = requests.post(url, json=data, timeout=30)
        if response.status_code == 200:
            res_json = response.json()
            if res_json.get('code') == 0:
                debugging_port = res_json['data']['debuggingPort']
                print(f"指纹浏览器启动成功，调试端口: {debugging_port}")
                return int(debugging_port)
            else:
                print(f"启动指纹浏览器失败: {res_json.get('msg')}")
                return None
        else:
            print(f"启动指纹浏览器失败，HTTP状态码: {response.status_code}")
            return None

    except Exception as e:
        print(f"启动指纹浏览器时发生异常: {e}")
        return None

def run_automation_with_semaphore(container_code: str, env_name: str, operation_type: str,
                                activity_name: Optional[str] = None, duration_hours: Optional[int] = None) -> bool:
    """使用信号量控制的自动化运行函数"""
    global open_counter

    with semaphore:
        open_counter += 1
        if open_counter > max_open_times:
            print(f"已达到最大打开次数限制: {max_open_times}")
            return False

        logger = setup_logger(container_code)
        logger.info(f"正在启动指纹浏览器环境: {env_name} ({container_code})")
        logger.info(f"发送启动请求到: http://127.0.0.1:6873/api/v1/browser/start")
        logger.info(f"请求数据: {{'containerCode': '{container_code}', 'skipSystemResourceCheck': True}}")

        debugging_port = start_browser(container_code)
        if debugging_port is None:
            logger.error(f"无法启动容器代码为 {container_code} 的指纹浏览器")
            return False

        try:
            logger.info("正在初始化自动化实例...")
            automation = MiaoshouAutomation(container_code, debugging_port, logger, env_name)
            logger.info(f"Container code: {container_code} started.")

            logger.info(f"开始执行操作类型: {operation_type}")

            if operation_type == "add_products":
                logger.info("开始为未开始的秒杀活动添加产品")
                result = automation.add_products_to_flash_sale()
            elif operation_type == "create_activity":
                logger.info(f"开始创建限时秒杀活动: {activity_name}")
                result = automation.create_flash_sale_activity(activity_name, duration_hours)
            else:
                logger.error(f"未知的操作类型: {operation_type}")
                result = False

            # 记录结果到Excel
            result_data = {
                '时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                '环境名称': env_name,
                '容器代码': container_code,
                '操作类型': operation_type,
                '活动名称': activity_name or 'N/A',
                '结果': '成功' if result else '失败'
            }

            if operation_type == "add_products":
                write_to_excel_realtime(result_data, f"AddProducts_Results_{datetime.now().strftime('%Y-%m-%d')}.xlsx")
            else:
                write_to_excel_realtime(result_data, f"CreateActivity_Results_{datetime.now().strftime('%Y-%m-%d')}.xlsx")

            return result

        except Exception as e:
            logger.error(f"自动化执行过程中发生异常: {e}")
            return False

        finally:
            try:
                print("🔄 准备关闭浏览器...")
                # 注意：这里不关闭浏览器，让用户手动检查结果
                # automation.close_browser()
            except Exception as e:
                logger.error(f"关闭浏览器时发生异常: {e}")

def read_env_from_excel(file_path: str) -> Dict[str, str]:
    """从Excel文件读取环境配置"""
    try:
        df = pd.read_excel(file_path)
        env_dict = {}

        for _, row in df.iterrows():
            # 修复：正确处理数字类型的容器代码，去掉.0后缀
            container_code_raw = row.iloc[0]
            env_name_raw = row.iloc[1]

            # 处理容器代码：如果是数字，转换为整数再转字符串
            if pd.isna(container_code_raw):
                continue
            elif isinstance(container_code_raw, (int, float)):
                container_code = str(int(container_code_raw))
            else:
                container_code = str(container_code_raw).strip()

            # 处理环境名称
            if pd.isna(env_name_raw):
                continue
            else:
                env_name = str(env_name_raw).strip()

            if container_code and env_name and container_code != 'nan' and env_name != 'nan':
                env_dict[container_code] = env_name

        return env_dict

    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return {}

def create_sample_environment_file():
    """创建示例环境配置文件"""
    if not os.path.exists(environment_path):
        sample_data = {
            '环境ID': ['1254658148'],
            '环境名称': ['环境专用-妙手1']
        }
        df = pd.DataFrame(sample_data)
        df.to_excel(environment_path, index=False, engine='openpyxl')
        print(f"📁 创建示例环境配置文件: {environment_path}")
    else:
        print(f"✅ 环境配置文件已存在: {environment_path}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 妙手ERP限时秒杀自动化程序")
    print("=" * 60)

    # 创建必要的目录
    for dir_name in ['logs', 'reports', 'debug_pages']:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"📁 创建目录: {dir_name}")

    # 创建示例环境配置文件
    create_sample_environment_file()

    # 等待启动时间
    if wait_time > 0:
        print(f"⏳ 等待 {wait_time} 秒后开始执行...")
        time.sleep(wait_time)

    # 读取环境配置
    env_dict = read_env_from_excel(environment_path)
    if not env_dict:
        print(f"❌ 无法读取环境配置文件: {environment_path}")
        print("请确保文件存在且格式正确（第一列：环境ID，第二列：环境名称）")
        return

    print(f"📋 读取到 {len(env_dict)} 个环境配置")
    for env_id, env_name in env_dict.items():
        print(f"  - {env_id}: {env_name}")

    print("\n🚀 开始为未开始的秒杀活动添加商品...")

    success_count = 0
    for container_code, env_name in env_dict.items():
        print(f"\n🎯 处理环境: {env_name} ({container_code})")

        result = run_automation_with_semaphore(
            container_code=container_code,
            env_name=env_name,
            operation_type="add_products",
            activity_name=None,
            duration_hours=None
        )

        if result:
            success_count += 1
            print(f"✅ {env_name} 处理成功")
        else:
            print(f"❌ {env_name} 处理失败")

        # 环境间隔
        time.sleep(2)

    print(f"\n📊 执行完成: {success_count}/{len(env_dict)} 个环境处理成功")
    print("🔍 请检查浏览器页面和日志文件获取详细信息")

if __name__ == '__main__':
    main()




