页面调试信息 - failed_click_not_started
时间: 20250724_122121
URL: https://erp.91miaoshou.com/?redirect=%2Ftiktok%2Fmarketing%2FflashSale
标题: 妙手ERP - 免费TikTok、Shopee、TEMU、OZON等跨境电商ERP软件
页面大小: 58513 字符

==================================================
表单元素列表:
  input 0: {'type': 'input', 'index': 0, 'id': 'no-id', 'name': 'mobile', 'placeholder': '手机号/子账号/邮箱', 'className': 'account-input J_inputField', 'type_attr': 'text'}
  input 1: {'type': 'input', 'index': 1, 'id': 'no-id', 'name': 'password', 'placeholder': '密码', 'className': 'password-input J_inputField', 'type_attr': 'password'}
  input 2: {'type': 'input', 'index': 2, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'login-verify-form-input login-verify-account-input', 'type_attr': 'text'}
  input 3: {'type': 'input', 'index': 3, 'id': 'no-id', 'name': 'no-name', 'placeholder': '请输入验证码', 'className': 'login-verify-form-input login-verify-code-input', 'type_attr': 'text'}
  input 4: {'type': 'input', 'index': 4, 'id': 'no-id', 'name': 'captchaUuid', 'placeholder': 'no-placeholder', 'className': 'J_inputField', 'type_attr': 'hidden'}
  input 5: {'type': 'input', 'index': 5, 'id': 'no-id', 'name': 'mobile', 'placeholder': '手机号', 'className': 'register-mobile J_inputField', 'type_attr': 'text'}
  input 6: {'type': 'input', 'index': 6, 'id': 'no-id', 'name': 'password', 'placeholder': '密码', 'className': 'register-password J_inputField', 'type_attr': 'password'}
  input 7: {'type': 'input', 'index': 7, 'id': 'no-id', 'name': 'confirmPassword', 'placeholder': '确认密码', 'className': 'register-confirm-password J_inputField', 'type_attr': 'password'}
  input 8: {'type': 'input', 'index': 8, 'id': 'no-id', 'name': 'qq', 'placeholder': 'QQ账号', 'className': 'register-qq J_inputField', 'type_attr': 'text'}
  input 9: {'type': 'input', 'index': 9, 'id': 'no-id', 'name': 'code', 'placeholder': '短信验证码', 'className': 'register-mobile-code J_inputField', 'type_attr': 'text'}
  input 10: {'type': 'input', 'index': 10, 'id': 'no-id', 'name': 'captchaUuid', 'placeholder': 'no-placeholder', 'className': 'J_inputField', 'type_attr': 'hidden'}
  input 11: {'type': 'input', 'index': 11, 'id': 'no-id', 'name': 'mobileCountryCode', 'placeholder': 'no-placeholder', 'className': 'J_inputField', 'type_attr': 'hidden'}
  input 12: {'type': 'input', 'index': 12, 'id': 'no-id', 'name': 'email', 'placeholder': '请输入电子邮箱', 'className': 'register-email J_inputField', 'type_attr': 'text'}
  input 13: {'type': 'input', 'index': 13, 'id': 'no-id', 'name': 'mobile', 'placeholder': '手机号', 'className': 'register-mobile email-register-form__register-mobile validate-input-error J_inputField ', 'type_attr': 'text'}
  input 14: {'type': 'input', 'index': 14, 'id': 'no-id', 'name': 'password', 'placeholder': '密码', 'className': 'register-password J_inputField', 'type_attr': 'password'}
  input 15: {'type': 'input', 'index': 15, 'id': 'no-id', 'name': 'confirmPassword', 'placeholder': '确认密码', 'className': 'register-confirm-password J_inputField', 'type_attr': 'password'}
  input 16: {'type': 'input', 'index': 16, 'id': 'no-id', 'name': 'code', 'placeholder': '邮箱验证码', 'className': 'register-email-code J_inputField', 'type_attr': 'text'}
  input 17: {'type': 'input', 'index': 17, 'id': 'no-id', 'name': 'qq', 'placeholder': 'QQ账号', 'className': 'register-qq J_inputField', 'type_attr': 'text'}
  input 18: {'type': 'input', 'index': 18, 'id': 'no-id', 'name': 'mobile', 'placeholder': '手机号/子账号/邮箱', 'className': 'J_inputField', 'type_attr': 'text'}
  input 19: {'type': 'input', 'index': 19, 'id': 'no-id', 'name': 'password', 'placeholder': '密码', 'className': ' J_inputField', 'type_attr': 'password'}
  input 20: {'type': 'input', 'index': 20, 'id': 'no-id', 'name': 'captchaUuid', 'placeholder': 'no-placeholder', 'className': 'J_inputField', 'type_attr': 'hidden'}
  input 21: {'type': 'input', 'index': 21, 'id': 'no-id', 'name': 'mobile', 'placeholder': '注册妙手ERP主账号手机号', 'className': 'recovery-mobile J_inputField', 'type_attr': 'text'}
  input 22: {'type': 'input', 'index': 22, 'id': 'no-id', 'name': 'code', 'placeholder': '短信验证码', 'className': 'recovery-mobile-code J_inputField', 'type_attr': 'text'}
  input 23: {'type': 'input', 'index': 23, 'id': 'no-id', 'name': 'password', 'placeholder': '密码', 'className': 'recovery-mobile-password J_inputField', 'type_attr': 'password'}
  input 24: {'type': 'input', 'index': 24, 'id': 'no-id', 'name': 'confirmPassword', 'placeholder': '确认密码', 'className': 'recovery-mobile-confirm-password J_inputField', 'type_attr': 'password'}
  input 25: {'type': 'input', 'index': 25, 'id': 'no-id', 'name': 'captchaUuid', 'placeholder': 'no-placeholder', 'className': 'J_inputField', 'type_attr': 'hidden'}
  input 26: {'type': 'input', 'index': 26, 'id': 'no-id', 'name': 'email', 'placeholder': '请输入电子邮箱', 'className': 'email-recovery-input J_inputField', 'type_attr': 'text'}
  button 0: {'type': 'button', 'index': 0, 'id': 'no-id', 'text': '免费使用', 'className': 'free-use-button J_registerShowModalBtn'}
  button 1: {'type': 'button', 'index': 1, 'id': 'no-id', 'text': '立即登录', 'className': 'login login-button'}
  button 2: {'type': 'button', 'index': 2, 'id': 'no-id', 'text': '更多平台', 'className': 'more-platform-button'}
  button 3: {'type': 'button', 'index': 3, 'id': 'no-id', 'text': '查看详情', 'className': 'core-module-detail-btn J_registerShowModalBtn'}
  button 4: {'type': 'button', 'index': 4, 'id': 'no-id', 'text': '查看详情', 'className': 'core-module-detail-btn J_registerShowModalBtn'}
  button 5: {'type': 'button', 'index': 5, 'id': 'no-id', 'text': '查看详情', 'className': 'core-module-detail-btn J_registerShowModalBtn'}
  button 6: {'type': 'button', 'index': 6, 'id': 'no-id', 'text': '查看详情', 'className': 'core-module-detail-btn J_registerShowModalBtn'}
  button 7: {'type': 'button', 'index': 7, 'id': 'no-id', 'text': '查看详情', 'className': 'core-module-detail-btn J_registerShowModalBtn'}
  button 8: {'type': 'button', 'index': 8, 'id': 'no-id', 'text': '查看详情', 'className': 'core-module-detail-btn J_registerShowModalBtn'}
  button 9: {'type': 'button', 'index': 9, 'id': 'no-id', 'text': '查看详情', 'className': 'core-module-detail-btn J_registerShowModalBtn'}
  button 10: {'type': 'button', 'index': 10, 'id': 'no-id', 'text': '查看详情', 'className': 'core-module-detail-btn J_registerShowModalBtn'}
  button 11: {'type': 'button', 'index': 11, 'id': 'no-id', 'text': '查看详情', 'className': 'core-module-detail-btn J_registerShowModalBtn'}
