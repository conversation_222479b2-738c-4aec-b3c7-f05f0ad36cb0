妙手商品秒杀、折扣助手

背景说明
AddProductAutomation自动上传商品和库存-单个且每个运行后睡眠10分钟250415最终版-张丽.py
是我之前的Python程序，实现在ozon商品页面上传商品文件的操作
现在作为我们的技术参考，我希望实现类似的框架，一样的在指纹浏览器中启动，然后再去进行我们的商品秒杀、折扣助手业务

1、https://erp.91miaoshou.com/tiktok/marketing/flashSale
妙手的限时秒杀活动，先创建活动，图1
2、然后到管理活动产品页面去添加产品，比如https://erp.91miaoshou.com/tiktok/marketing/flashSale/create?shopId=7745227&platformPromotionId=7524632022040676097&site=TH&step=2
3、后续还有添加产品，设置折扣、限购数量等操作

现在我困惑的是要如何和你进行良好的沟通，让你能够明白我想要的操作，到底我要怎么上传对应的网页页面给你呢？
但是至少来说，我们现在需要一个基于上面这个Python程序的一个打开限时秒杀活动的雏形，你觉得呢？
