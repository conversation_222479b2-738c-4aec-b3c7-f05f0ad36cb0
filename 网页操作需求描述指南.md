# 网页操作需求描述指南

为了更好地帮助您实现自动化操作，请按照以下指南提供网页操作信息。

## 1. 页面基本信息

### 1.1 页面URL和标题
```
页面URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale
页面标题: 妙手-限时秒杀
```

### 1.2 页面截图
- 提供完整的页面截图
- 标注需要操作的元素位置
- 如果页面很长，可以分段截图

## 2. 元素定位信息

### 2.1 获取元素信息的方法

**使用浏览器开发者工具（推荐）：**
1. 按F12打开开发者工具
2. 点击左上角的选择元素工具（或按Ctrl+Shift+C）
3. 点击要操作的页面元素
4. 在Elements面板中右键该元素
5. 选择"Copy" → "Copy XPath"或"Copy selector"

### 2.2 提供的元素信息格式

```
元素名称: 创建活动按钮
元素类型: 按钮
XPath: //button[contains(text(), '创建活动')]
CSS选择器: .btn.btn-primary[data-action="create"]
元素ID: create-activity-btn
元素Class: btn btn-primary create-btn
```

## 3. 操作流程描述

### 3.1 详细步骤描述
```
操作流程：创建限时秒杀活动

1. 打开页面
   - URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale
   - 等待页面完全加载

2. 点击"创建活动"按钮
   - 位置: 页面右上角
   - XPath: //button[contains(text(), '创建活动')]
   - 预期结果: 弹出创建活动对话框

3. 填写活动信息
   - 活动名称输入框:
     * XPath: //input[@placeholder='请输入活动名称']
     * 输入内容: "双11限时秒杀活动"
   
   - 活动时间选择:
     * 开始时间: //input[@name='startTime']
     * 结束时间: //input[@name='endTime']
     * 默认值: 当前时间开始，24小时后结束

4. 确认创建
   - 按钮XPath: //button[contains(text(), '确认创建')]
   - 预期结果: 跳转到活动管理页面
```

### 3.2 条件分支处理
```
条件判断：
- 如果活动名称已存在 → 显示错误提示 → 修改活动名称
- 如果时间选择无效 → 显示时间错误 → 重新选择时间
- 如果网络超时 → 重试操作
```

## 4. 表单字段信息

### 4.1 表单字段详细信息
```
表单名称: 创建限时秒杀活动表单

字段列表:
1. 活动名称 (必填)
   - 字段名: activityName
   - 类型: 文本输入框
   - XPath: //input[@name='activityName']
   - 验证规则: 长度2-50字符，不能重复
   - 示例值: "双11限时秒杀"

2. 活动描述 (可选)
   - 字段名: description
   - 类型: 文本域
   - XPath: //textarea[@name='description']
   - 验证规则: 最大500字符
   - 示例值: "双11期间限时秒杀活动"

3. 开始时间 (必填)
   - 字段名: startTime
   - 类型: 日期时间选择器
   - XPath: //input[@name='startTime']
   - 格式: YYYY-MM-DD HH:mm:ss
   - 默认值: 当前时间

4. 结束时间 (必填)
   - 字段名: endTime
   - 类型: 日期时间选择器
   - XPath: //input[@name='endTime']
   - 格式: YYYY-MM-DD HH:mm:ss
   - 验证规则: 必须大于开始时间
```

## 5. 页面状态和变化

### 5.1 操作前后的页面变化
```
操作前状态:
- 页面显示活动列表
- "创建活动"按钮可点击
- 列表为空或显示现有活动

操作后状态:
- 弹出创建活动对话框
- 对话框包含表单字段
- 背景页面变暗（遮罩层）

提交后状态:
- 对话框关闭
- 页面刷新或跳转
- 新活动出现在列表中
- 显示成功提示消息
```

### 5.2 成功/失败判断标准
```
成功标准:
- 页面显示"创建成功"提示
- 新活动出现在活动列表中
- 页面URL包含新活动ID
- 没有错误提示信息

失败标准:
- 显示错误提示信息
- 表单字段显示验证错误
- 页面停留在创建对话框
- 网络请求返回错误状态
```

## 6. 特殊情况处理

### 6.1 加载和等待
```
页面加载等待:
- 等待页面完全加载: document.readyState === 'complete'
- 等待特定元素出现: //button[contains(text(), '创建活动')]
- 等待AJAX请求完成: 监听网络请求状态

动态内容处理:
- 下拉列表选项动态加载
- 表格数据分页加载
- 搜索结果实时更新
```

### 6.2 错误处理
```
常见错误情况:
1. 元素未找到
   - 原因: 页面结构变化、加载未完成
   - 处理: 增加等待时间、更新选择器

2. 点击无效
   - 原因: 元素被遮挡、不可点击
   - 处理: 滚动到元素位置、等待元素可点击

3. 输入失败
   - 原因: 输入框被禁用、格式验证
   - 处理: 检查元素状态、调整输入格式
```

## 7. 提供信息的模板

### 7.1 完整需求描述模板
```
【操作需求】: 创建限时秒杀活动

【页面信息】:
- URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale
- 标题: 妙手-限时秒杀

【操作步骤】:
1. 点击"创建活动"按钮
   - XPath: //button[contains(text(), '创建活动')]
2. 填写活动名称
   - XPath: //input[@name='activityName']
   - 内容: "测试活动"
3. 选择活动时间
   - 开始时间: //input[@name='startTime']
   - 结束时间: //input[@name='endTime']
4. 点击确认创建
   - XPath: //button[contains(text(), '确认创建')]

【成功标准】:
- 显示"创建成功"提示
- 跳转到活动详情页面

【附件】:
- 页面截图: screenshot.png
- 元素检查截图: element_inspect.png
```

## 8. 常用元素选择器示例

### 8.1 按钮元素
```
// 包含特定文本的按钮
//button[contains(text(), '创建活动')]
//button[contains(text(), '确认')]
//button[contains(text(), '取消')]

// 特定类名的按钮
//button[@class='btn btn-primary']
//button[contains(@class, 'create-btn')]

// 特定属性的按钮
//button[@data-action='create']
//button[@id='create-btn']
```

### 8.2 输入框元素
```
// 按name属性
//input[@name='activityName']
//input[@name='startTime']

// 按placeholder属性
//input[@placeholder='请输入活动名称']
//input[@placeholder='请选择时间']

// 按类型
//input[@type='text']
//input[@type='password']
//textarea[@name='description']
```

### 8.3 选择器元素
```
// 下拉选择框
//select[@name='category']
//select[@id='product-category']

// 选项
//option[@value='electronics']
//option[contains(text(), '电子产品')]

// 复选框和单选框
//input[@type='checkbox'][@name='agree']
//input[@type='radio'][@value='option1']
```

## 9. 调试和验证

### 9.1 在浏览器控制台验证选择器
```javascript
// 验证XPath
$x("//button[contains(text(), '创建活动')]")

// 验证CSS选择器
document.querySelector(".btn.btn-primary")
document.querySelectorAll(".product-item")

// 检查元素是否可见和可点击
element.offsetParent !== null  // 检查是否可见
!element.disabled              // 检查是否可点击
```

### 9.2 常用调试命令
```javascript
// 获取元素文本
element.innerText
element.textContent

// 获取元素属性
element.getAttribute('class')
element.getAttribute('data-id')

// 检查元素状态
element.style.display
element.disabled
element.checked
```

## 10. 快速开始 - 当前页面分析

根据日志显示，您的页面已经成功打开：
- **页面标题**: 妙手-限时秒杀
- **页面URL**: https://erp.91miaoshou.com/tiktok/marketing/flashSale

**下一步操作**：
1. 在打开的浏览器页面中按F12打开开发者工具
2. 找到"创建活动"或类似的按钮
3. 右键点击该按钮，选择"检查元素"
4. 在Elements面板中右键该元素，选择"Copy XPath"
5. 将获取的XPath提供给我

**示例格式**：
```
我找到了创建活动按钮：
XPath: //button[contains(text(), '创建活动')]
或者
XPath: //div[@class='create-btn']//button
```

通过提供以上详细信息，我们可以更准确地实现您需要的自动化操作功能。
