页面调试信息 - product_management_page
时间: 20250724_122526
URL: https://erp.91miaoshou.com/tiktok/marketing/flashSale/create?shopId=8905292&platformPromotionId=7530486698141026066&site=TH&step=2
标题: 妙手-管理活动产品
页面大小: 82945 字符

==================================================
表单元素列表:
  input 0: {'type': 'input', 'index': 0, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 1: {'type': 'input', 'index': 1, 'id': 'jx-id-3876-63', 'name': 'no-name', 'placeholder': '产品名称', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 2: {'type': 'input', 'index': 2, 'id': 'jx-id-3876-64', 'name': 'no-name', 'placeholder': '产品ID', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 3: {'type': 'input', 'index': 3, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 4: {'type': 'input', 'index': 4, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 5: {'type': 'input', 'index': 5, 'id': 'jx-id-3876-77', 'name': 'no-name', 'placeholder': '不限制', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 6: {'type': 'input', 'index': 6, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 7: {'type': 'input', 'index': 7, 'id': 'jx-id-3876-78', 'name': 'no-name', 'placeholder': '不限制', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 8: {'type': 'input', 'index': 8, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 9: {'type': 'input', 'index': 9, 'id': 'jx-id-3876-83', 'name': 'no-name', 'placeholder': '不限制', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 10: {'type': 'input', 'index': 10, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 11: {'type': 'input', 'index': 11, 'id': 'jx-id-3876-84', 'name': 'no-name', 'placeholder': '不限制', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 12: {'type': 'input', 'index': 12, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 13: {'type': 'input', 'index': 13, 'id': 'jx-id-3876-85', 'name': 'no-name', 'placeholder': '不限制', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 14: {'type': 'input', 'index': 14, 'id': 'no-id', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-checkbox__original', 'type_attr': 'checkbox'}
  input 15: {'type': 'input', 'index': 15, 'id': 'jx-id-3876-86', 'name': 'no-name', 'placeholder': '不限制', 'className': 'jx-input__inner', 'type_attr': 'text'}
  input 16: {'type': 'input', 'index': 16, 'id': 'jx-id-3876-65', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-input__inner', 'type_attr': 'number'}
  input 17: {'type': 'input', 'index': 17, 'id': 'jx-id-3876-66', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'jx-select__input is-small', 'type_attr': 'text'}
  input 18: {'type': 'input', 'index': 18, 'id': 'J_minimumPluginVersion', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'no-class', 'type_attr': 'text'}
  input 19: {'type': 'input', 'index': 19, 'id': 'J_appInfo', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'no-class', 'type_attr': 'hidden'}
  input 20: {'type': 'input', 'index': 20, 'id': 'J_dwh_logS', 'name': 'no-name', 'placeholder': 'no-placeholder', 'className': 'no-class', 'type_attr': 'text'}
  label 0: {'type': 'label', 'index': 0, 'id': 'no-id', 'text': '仅展示未设置秒杀价产品', 'for': 'no-for'}
  label 1: {'type': 'label', 'index': 1, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 2: {'type': 'label', 'index': 2, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 3: {'type': 'label', 'index': 3, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 4: {'type': 'label', 'index': 4, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 5: {'type': 'label', 'index': 5, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 6: {'type': 'label', 'index': 6, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  label 7: {'type': 'label', 'index': 7, 'id': 'no-id', 'text': 'no-text', 'for': 'no-for'}
  button 0: {'type': 'button', 'index': 0, 'id': 'no-id', 'text': '添加产品', 'className': 'jx-button jx-button--primary jx-button--small pro-button'}
  button 1: {'type': 'button', 'index': 1, 'id': 'no-id', 'text': '批量秒杀价格', 'className': 'jx-button jx-button--primary jx-button--small pro-button'}
  button 2: {'type': 'button', 'index': 2, 'id': 'no-id', 'text': '批量限购总量', 'className': 'jx-button jx-button--primary jx-button--small pro-button'}
  button 3: {'type': 'button', 'index': 3, 'id': 'no-id', 'text': '批量单用户限购量', 'className': 'jx-button jx-button--primary jx-button--small pro-button'}
  button 4: {'type': 'button', 'index': 4, 'id': 'no-id', 'text': '批量移除', 'className': 'jx-button jx-button--primary jx-button--small pro-button'}
  button 5: {'type': 'button', 'index': 5, 'id': 'no-id', 'text': '翻译', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 6: {'type': 'button', 'index': 6, 'id': 'no-id', 'text': '移除', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 7: {'type': 'button', 'index': 7, 'id': 'no-id', 'text': '同步', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 8: {'type': 'button', 'index': 8, 'id': 'no-id', 'text': '展开SKU(16)', 'className': 'jx-button jx-button--primary jx-button--small is-text expand-btn pro-button expand-btn'}
  button 9: {'type': 'button', 'index': 9, 'id': 'no-id', 'text': '移除', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 10: {'type': 'button', 'index': 10, 'id': 'no-id', 'text': '同步', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 11: {'type': 'button', 'index': 11, 'id': 'no-id', 'text': '展开SKU(10)', 'className': 'jx-button jx-button--primary jx-button--small is-text expand-btn pro-button expand-btn'}
  button 12: {'type': 'button', 'index': 12, 'id': 'no-id', 'text': '移除', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 13: {'type': 'button', 'index': 13, 'id': 'no-id', 'text': '同步', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 14: {'type': 'button', 'index': 14, 'id': 'no-id', 'text': '展开SKU(36)', 'className': 'jx-button jx-button--primary jx-button--small is-text expand-btn pro-button expand-btn'}
  button 15: {'type': 'button', 'index': 15, 'id': 'no-id', 'text': '移除', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 16: {'type': 'button', 'index': 16, 'id': 'no-id', 'text': '同步', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 17: {'type': 'button', 'index': 17, 'id': 'no-id', 'text': '展开SKU(77)', 'className': 'jx-button jx-button--primary jx-button--small is-text expand-btn pro-button expand-btn'}
  button 18: {'type': 'button', 'index': 18, 'id': 'no-id', 'text': '移除', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 19: {'type': 'button', 'index': 19, 'id': 'no-id', 'text': '同步', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 20: {'type': 'button', 'index': 20, 'id': 'no-id', 'text': '展开SKU(24)', 'className': 'jx-button jx-button--primary jx-button--small is-text expand-btn pro-button expand-btn'}
  button 21: {'type': 'button', 'index': 21, 'id': 'no-id', 'text': '移除', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 22: {'type': 'button', 'index': 22, 'id': 'no-id', 'text': '同步', 'className': 'jx-button jx-button--primary jx-button--small is-text pro-button'}
  button 23: {'type': 'button', 'index': 23, 'id': 'no-id', 'text': '展开SKU(12)', 'className': 'jx-button jx-button--primary jx-button--small is-text expand-btn pro-button expand-btn'}
  button 24: {'type': 'button', 'index': 24, 'id': 'no-id', 'text': 'no-text', 'className': 'btn-prev'}
  button 25: {'type': 'button', 'index': 25, 'id': 'no-id', 'text': 'no-text', 'className': 'btn-next'}
  button 26: {'type': 'button', 'index': 26, 'id': 'no-id', 'text': '提交', 'className': 'jx-button jx-button--primary jx-button--large pro-button'}
  button 27: {'type': 'button', 'index': 27, 'id': 'no-id', 'text': '取消', 'className': 'jx-button jx-button--large pro-button'}
